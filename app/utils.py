import os
import secrets
from PIL import Image
from flask import current_app
from functools import wraps
from flask_login import current_user
from flask import flash, redirect, url_for

def save_picture(form_picture, folder):
    """Save uploaded picture and return filename"""
    random_hex = secrets.token_hex(8)
    _, f_ext = os.path.splitext(form_picture.filename)
    picture_fn = random_hex + f_ext
    picture_path = os.path.join(current_app.root_path, 'static', 'uploads', folder, picture_fn)
    
    # Create directory if it doesn't exist
    os.makedirs(os.path.dirname(picture_path), exist_ok=True)
    
    # Resize image
    output_size = (800, 800)
    img = Image.open(form_picture)
    img.thumbnail(output_size)
    img.save(picture_path)
    
    return picture_fn

def admin_required(f):
    """Decorator to require admin access"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated or not current_user.is_admin():
            flash('Access denied. Admin privileges required.', 'error')
            return redirect(url_for('main.index'))
        return f(*args, **kwargs)
    return decorated_function

def manager_required(f):
    """Decorator to require manager access"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated or not current_user.is_manager():
            flash('Access denied. Manager privileges required.', 'error')
            return redirect(url_for('main.index'))
        return f(*args, **kwargs)
    return decorated_function

def delivery_required(f):
    """Decorator to require delivery personnel access"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated or not current_user.is_delivery():
            flash('Access denied. Delivery personnel access required.', 'error')
            return redirect(url_for('main.index'))
        return f(*args, **kwargs)
    return decorated_function

def client_required(f):
    """Decorator to require client access"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated or not current_user.is_client():
            flash('Access denied. Client access required.', 'error')
            return redirect(url_for('main.index'))
        return f(*args, **kwargs)
    return decorated_function

def format_currency(amount):
    """Format amount as Moroccan Dirham"""
    return f"{amount:.2f} Dh"

def generate_order_number():
    """Generate unique order number"""
    from datetime import datetime
    import random
    timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
    random_num = random.randint(100, 999)
    return f'YO{timestamp}{random_num}'

def generate_invoice_number():
    """Generate unique invoice number"""
    from datetime import datetime
    import random
    timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
    random_num = random.randint(100, 999)
    return f'INV{timestamp}{random_num}'

def send_email(to, subject, template, **kwargs):
    """Send email notification"""
    from flask_mail import Message
    from app import mail
    from flask import render_template
    
    msg = Message(
        subject=f'[YalaOffice] {subject}',
        recipients=[to],
        html=render_template(template, **kwargs),
        sender=current_app.config['MAIL_DEFAULT_SENDER']
    )
    mail.send(msg)

def create_notification(user_id, title, message, type='info'):
    """Create in-app notification"""
    # This would be implemented with a Notification model
    # For now, just a placeholder
    pass

def calculate_distance(lat1, lon1, lat2, lon2):
    """Calculate distance between two coordinates"""
    from math import radians, cos, sin, asin, sqrt

    # Convert decimal degrees to radians
    lat1, lon1, lat2, lon2 = map(radians, [lat1, lon1, lat2, lon2])

    # Haversine formula
    dlat = lat2 - lat1
    dlon = lon2 - lon1
    a = sin(dlat/2)**2 + cos(lat1) * cos(lat2) * sin(dlon/2)**2
    c = 2 * asin(sqrt(a))
    r = 6371  # Radius of earth in kilometers

    return c * r

def generate_invoice_pdf(order):
    """Generate PDF invoice for an order"""
    from reportlab.lib.pagesizes import letter, A4
    from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, Image
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch
    from reportlab.lib import colors
    from reportlab.lib.enums import TA_CENTER, TA_RIGHT, TA_LEFT
    from datetime import datetime
    import os

    # Create invoice directory if it doesn't exist
    invoice_dir = os.path.join(current_app.root_path, 'static', 'uploads', 'invoices')
    os.makedirs(invoice_dir, exist_ok=True)

    # Generate filename
    filename = f"invoice_{order.order_number}.pdf"
    filepath = os.path.join(invoice_dir, filename)

    # Create PDF document
    doc = SimpleDocTemplate(filepath, pagesize=A4)
    story = []

    # Get styles
    styles = getSampleStyleSheet()
    title_style = ParagraphStyle(
        'CustomTitle',
        parent=styles['Heading1'],
        fontSize=24,
        spaceAfter=30,
        alignment=TA_CENTER,
        textColor=colors.HexColor('#007bff')
    )

    # Company header
    story.append(Paragraph("YalaOffice", title_style))
    story.append(Paragraph("Office & School Supplies", styles['Normal']))
    story.append(Paragraph("Morocco", styles['Normal']))
    story.append(Paragraph("Phone: +212 XXX XXX XXX", styles['Normal']))
    story.append(Paragraph("Email: <EMAIL>", styles['Normal']))
    story.append(Spacer(1, 20))

    # Invoice title
    invoice_title = ParagraphStyle(
        'InvoiceTitle',
        parent=styles['Heading2'],
        fontSize=18,
        spaceAfter=20,
        alignment=TA_CENTER
    )
    story.append(Paragraph("INVOICE", invoice_title))
    story.append(Spacer(1, 20))

    # Invoice details
    invoice_data = [
        ['Invoice Number:', order.invoice.invoice_number if order.invoice else 'N/A'],
        ['Order Number:', order.order_number],
        ['Date:', order.created_at.strftime('%B %d, %Y') if order.created_at else 'N/A'],
        ['Customer:', order.customer.get_full_name()],
        ['Email:', order.customer.email],
        ['Phone:', order.delivery_phone],
        ['Delivery Address:', order.delivery_address],
        ['Payment Method:', order.payment_method.replace('_', ' ').title()],
    ]

    invoice_table = Table(invoice_data, colWidths=[2*inch, 4*inch])
    invoice_table.setStyle(TableStyle([
        ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
        ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
        ('FONTSIZE', (0, 0), (-1, -1), 10),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
    ]))
    story.append(invoice_table)
    story.append(Spacer(1, 30))

    # Order items
    items_data = [['Item', 'Quantity', 'Unit Price (Dh)', 'Total (Dh)']]

    for item in order.items:
        items_data.append([
            item.product.title,
            str(item.quantity),
            f"{item.unit_price:.2f}",
            f"{item.total_price:.2f}"
        ])

    # Add totals
    items_data.append(['', '', 'Subtotal:', f"{order.subtotal:.2f}"])
    if order.discount_amount > 0:
        items_data.append(['', '', 'Discount:', f"-{order.discount_amount:.2f}"])
    items_data.append(['', '', 'Total:', f"{order.total_amount:.2f}"])

    items_table = Table(items_data, colWidths=[3*inch, 1*inch, 1.5*inch, 1.5*inch])
    items_table.setStyle(TableStyle([
        ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
        ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
        ('ALIGN', (0, 1), (0, -4), 'LEFT'),
        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
        ('FONTSIZE', (0, 0), (-1, -1), 10),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
        ('BACKGROUND', (0, 1), (-1, -4), colors.beige),
        ('GRID', (0, 0), (-1, -4), 1, colors.black),
        ('BACKGROUND', (0, -3), (-1, -1), colors.lightgrey),
        ('FONTNAME', (0, -1), (-1, -1), 'Helvetica-Bold'),
    ]))
    story.append(items_table)
    story.append(Spacer(1, 30))

    # Footer
    footer_style = ParagraphStyle(
        'Footer',
        parent=styles['Normal'],
        fontSize=10,
        alignment=TA_CENTER,
        textColor=colors.grey
    )
    story.append(Paragraph("Thank you for your business!", footer_style))
    story.append(Paragraph("This is a computer-generated invoice.", footer_style))

    # Build PDF
    doc.build(story)

    return filename
