from flask import render_template, redirect, url_for, flash, request, jsonify, current_app
from flask_login import login_required, current_user
from functools import wraps
from app.admin import bp
from app.models import User, Product, Category, Branch, Order, ProductStock, OrderItem, PromoCode
from app import db

def admin_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated or not current_user.is_admin():
            flash('Access denied. Admin privileges required.', 'error')
            return redirect(url_for('main.index'))
        return f(*args, **kwargs)
    return decorated_function

@bp.route('/dashboard')
@login_required
@admin_required
def dashboard():
    """Admin dashboard with system overview"""
    # Get key statistics
    total_users = User.query.count()
    total_products = Product.query.filter_by(is_active=True).count()
    total_orders = Order.query.count()
    pending_orders = Order.query.filter_by(status='pending').count()
    
    # Recent orders
    recent_orders = Order.query.order_by(Order.created_at.desc()).limit(5).all()
    
    # Low stock products
    low_stock_products = []
    for stock in ProductStock.query.all():
        if stock.is_low_stock():
            low_stock_products.append(stock)
    
    return render_template('admin/dashboard.html',
                         total_users=total_users,
                         total_products=total_products,
                         total_orders=total_orders,
                         pending_orders=pending_orders,
                         recent_orders=recent_orders,
                         low_stock_products=low_stock_products)

@bp.route('/users')
@login_required
@admin_required
def users():
    """User management page"""
    page = request.args.get('page', 1, type=int)
    users = User.query.paginate(
        page=page, per_page=20, error_out=False
    )
    return render_template('admin/users.html', users=users)

@bp.route('/products')
@login_required
@admin_required
def products():
    """Product management page"""
    page = request.args.get('page', 1, type=int)
    products = Product.query.paginate(
        page=page, per_page=20, error_out=False
    )
    return render_template('admin/products.html', products=products)

@bp.route('/orders')
@login_required
@admin_required
def orders():
    """Order management page"""
    page = request.args.get('page', 1, type=int)
    status = request.args.get('status')
    
    query = Order.query
    if status:
        query = query.filter_by(status=status)
    
    orders = query.order_by(Order.created_at.desc()).paginate(
        page=page, per_page=20, error_out=False
    )
    return render_template('admin/orders.html', orders=orders, current_status=status)

@bp.route('/branches')
@login_required
@admin_required
def branches():
    """Branch management page"""
    branches = Branch.query.all()
    return render_template('admin/branches.html', branches=branches)

@bp.route('/categories')
@login_required
@admin_required
def categories():
    """Category management page"""
    categories = Category.query.all()
    return render_template('admin/categories.html', categories=categories)

@bp.route('/product/add', methods=['GET', 'POST'])
@login_required
@admin_required
def add_product():
    """Add new product"""
    if request.method == 'POST':
        title = request.form.get('title')
        description = request.form.get('description')
        brand = request.form.get('brand')
        sku = request.form.get('sku')
        normal_price = float(request.form.get('normal_price', 0))
        reseller_price = float(request.form.get('reseller_price', 0))
        category_id = int(request.form.get('category_id'))

        # Handle file uploads
        main_image = None
        gallery_images = []

        # Main image upload
        if 'main_image' in request.files:
            file = request.files['main_image']
            if file and file.filename:
                from app.utils import save_uploaded_file
                import os
                upload_folder = os.path.join(current_app.instance_path, 'uploads', 'products')
                filename = save_uploaded_file(file, upload_folder)
                if filename:
                    main_image = f'/static/uploads/products/{filename}'

        # Gallery images upload
        if 'gallery_images' in request.files:
            files = request.files.getlist('gallery_images')
            for file in files:
                if file and file.filename:
                    from app.utils import save_uploaded_file
                    import os
                    upload_folder = os.path.join(current_app.instance_path, 'uploads', 'products')
                    filename = save_uploaded_file(file, upload_folder)
                    if filename:
                        gallery_images.append(f'/static/uploads/products/{filename}')

        # Create product
        product = Product(
            title=title,
            description=description,
            brand=brand,
            sku=sku or generate_sku(title),
            normal_price=normal_price,
            reseller_price=reseller_price,
            category_id=category_id,
            main_image=main_image,
            gallery_images=','.join(gallery_images) if gallery_images else None
        )

        db.session.add(product)
        db.session.flush()

        # Add initial stock for all branches
        branches = Branch.query.filter_by(is_active=True).all()
        for branch in branches:
            initial_stock = int(request.form.get(f'stock_{branch.id}', 0))
            stock = ProductStock(
                product_id=product.id,
                branch_id=branch.id,
                quantity=initial_stock,
                min_stock_level=10
            )
            db.session.add(stock)

        db.session.commit()
        flash('Product added successfully!', 'success')
        return redirect(url_for('admin.products'))

    categories = Category.query.filter_by(is_active=True).all()
    branches = Branch.query.filter_by(is_active=True).all()
    return render_template('admin/add_product.html', categories=categories, branches=branches)

def generate_sku(title):
    """Generate SKU from product title"""
    import re
    import random
    import string

    # Clean title and take first 6 characters
    clean_title = re.sub(r'[^A-Za-z0-9]', '', title.upper())[:6]

    # Add random suffix
    suffix = ''.join(random.choices(string.digits, k=4))

    return f"{clean_title}{suffix}"

@bp.route('/product/edit/<int:id>', methods=['GET', 'POST'])
@login_required
@admin_required
def edit_product(id):
    """Edit product"""
    product = Product.query.get_or_404(id)

    if request.method == 'POST':
        product.title = request.form.get('title')
        product.description = request.form.get('description')
        product.brand = request.form.get('brand')
        product.sku = request.form.get('sku')
        product.normal_price = float(request.form.get('normal_price', 0))
        product.reseller_price = float(request.form.get('reseller_price', 0))
        product.category_id = int(request.form.get('category_id'))
        product.is_active = 'is_active' in request.form

        # Update stock for all branches
        branches = Branch.query.filter_by(is_active=True).all()
        for branch in branches:
            stock = ProductStock.query.filter_by(
                product_id=product.id,
                branch_id=branch.id
            ).first()

            new_quantity = int(request.form.get(f'stock_{branch.id}', 0))
            if stock:
                stock.quantity = new_quantity
            else:
                stock = ProductStock(
                    product_id=product.id,
                    branch_id=branch.id,
                    quantity=new_quantity,
                    min_stock_level=10
                )
                db.session.add(stock)

        db.session.commit()
        flash('Product updated successfully!', 'success')
        return redirect(url_for('admin.products'))

    categories = Category.query.filter_by(is_active=True).all()
    branches = Branch.query.filter_by(is_active=True).all()
    return render_template('admin/edit_product.html', product=product, categories=categories, branches=branches)

@bp.route('/user/add', methods=['GET', 'POST'])
@login_required
@admin_required
def add_user():
    """Add new user"""
    if request.method == 'POST':
        username = request.form.get('username')
        email = request.form.get('email')
        first_name = request.form.get('first_name')
        last_name = request.form.get('last_name')
        phone = request.form.get('phone')
        address = request.form.get('address')
        role = request.form.get('role')
        branch_id = request.form.get('branch_id') or None
        password = request.form.get('password')

        # Check if username or email already exists
        if User.query.filter_by(username=username).first():
            flash('Username already exists.', 'error')
            return redirect(url_for('admin.add_user'))

        if User.query.filter_by(email=email).first():
            flash('Email already exists.', 'error')
            return redirect(url_for('admin.add_user'))

        user = User(
            username=username,
            email=email,
            first_name=first_name,
            last_name=last_name,
            phone=phone,
            address=address,
            role=role,
            branch_id=int(branch_id) if branch_id else None
        )
        user.set_password(password)

        db.session.add(user)
        db.session.commit()

        flash('User added successfully!', 'success')
        return redirect(url_for('admin.users'))

    branches = Branch.query.filter_by(is_active=True).all()
    return render_template('admin/add_user.html', branches=branches)

@bp.route('/analytics')
@login_required
@admin_required
def analytics():
    """Analytics dashboard"""
    from datetime import datetime, timedelta
    from sqlalchemy import func, extract

    # Date range for analytics (last 30 days)
    end_date = datetime.utcnow()
    start_date = end_date - timedelta(days=30)

    # Sales Analytics
    total_revenue = db.session.query(func.sum(Order.total_amount))\
                             .filter(Order.status == 'delivered').scalar() or 0

    monthly_revenue = db.session.query(func.sum(Order.total_amount))\
                               .filter(Order.status == 'delivered',
                                      Order.created_at >= start_date).scalar() or 0

    # Order Analytics
    total_orders = Order.query.count()
    monthly_orders = Order.query.filter(Order.created_at >= start_date).count()
    pending_orders = Order.query.filter_by(status='pending').count()
    delivered_orders = Order.query.filter_by(status='delivered').count()

    # Product Analytics
    total_products = Product.query.filter_by(is_active=True).count()
    low_stock_count = len([stock for stock in ProductStock.query.all() if stock.is_low_stock()])

    # Customer Analytics
    total_customers = User.query.filter(User.role.in_(['client', 'reseller'])).count()
    new_customers = User.query.filter(User.role.in_(['client', 'reseller']),
                                     User.created_at >= start_date).count()

    # Top selling products
    top_products = db.session.query(
        Product.title,
        func.sum(OrderItem.quantity).label('total_sold'),
        func.sum(OrderItem.total_price).label('total_revenue')
    ).join(OrderItem).join(Order)\
     .filter(Order.status == 'delivered')\
     .group_by(Product.id, Product.title)\
     .order_by(func.sum(OrderItem.quantity).desc())\
     .limit(10).all()

    # Sales by category
    category_sales = db.session.query(
        Category.name,
        func.sum(OrderItem.total_price).label('total_revenue'),
        func.count(OrderItem.id).label('total_items')
    ).join(Product).join(OrderItem).join(Order)\
     .filter(Order.status == 'delivered')\
     .group_by(Category.id, Category.name)\
     .order_by(func.sum(OrderItem.total_price).desc()).all()

    # Monthly sales data for chart
    monthly_sales = db.session.query(
        extract('month', Order.created_at).label('month'),
        func.sum(Order.total_amount).label('revenue'),
        func.count(Order.id).label('orders')
    ).filter(Order.status == 'delivered',
            Order.created_at >= start_date)\
     .group_by(extract('month', Order.created_at))\
     .order_by(extract('month', Order.created_at)).all()

    # Branch performance
    branch_performance = db.session.query(
        Branch.name,
        func.count(Order.id).label('total_orders'),
        func.sum(Order.total_amount).label('total_revenue')
    ).join(Order).filter(Order.status == 'delivered')\
     .group_by(Branch.id, Branch.name)\
     .order_by(func.sum(Order.total_amount).desc()).all()

    return render_template('admin/analytics.html',
                         total_revenue=total_revenue,
                         monthly_revenue=monthly_revenue,
                         total_orders=total_orders,
                         monthly_orders=monthly_orders,
                         pending_orders=pending_orders,
                         delivered_orders=delivered_orders,
                         total_products=total_products,
                         low_stock_count=low_stock_count,
                         total_customers=total_customers,
                         new_customers=new_customers,
                         top_products=top_products,
                         category_sales=category_sales,
                         monthly_sales=monthly_sales,
                         branch_performance=branch_performance)

@bp.route('/promo-codes')
@login_required
@admin_required
def promo_codes():
    """Promo code management"""
    page = request.args.get('page', 1, type=int)
    promo_codes = PromoCode.query.order_by(PromoCode.created_at.desc())\
                                .paginate(page=page, per_page=20, error_out=False)
    return render_template('admin/promo_codes.html', promo_codes=promo_codes)

@bp.route('/promo-code/add', methods=['GET', 'POST'])
@login_required
@admin_required
def add_promo_code():
    """Add new promo code"""
    if request.method == 'POST':
        from datetime import datetime

        code = request.form.get('code').upper()
        description = request.form.get('description')
        discount_type = request.form.get('discount_type')
        discount_value = float(request.form.get('discount_value'))
        min_order_amount = float(request.form.get('min_order_amount', 0))
        max_discount_amount = request.form.get('max_discount_amount')
        usage_limit = request.form.get('usage_limit')
        valid_until = request.form.get('valid_until')

        # Check if code already exists
        if PromoCode.query.filter_by(code=code).first():
            flash('Promo code already exists.', 'error')
            return redirect(url_for('admin.add_promo_code'))

        promo = PromoCode(
            code=code,
            description=description,
            discount_type=discount_type,
            discount_value=discount_value,
            min_order_amount=min_order_amount,
            max_discount_amount=float(max_discount_amount) if max_discount_amount else None,
            usage_limit=int(usage_limit) if usage_limit else None,
            valid_until=datetime.strptime(valid_until, '%Y-%m-%d') if valid_until else None
        )

        db.session.add(promo)
        db.session.commit()

        flash('Promo code created successfully!', 'success')
        return redirect(url_for('admin.promo_codes'))

    return render_template('admin/add_promo_code.html')

@bp.route('/reports')
@login_required
@admin_required
def reports():
    """Reports dashboard"""
    return render_template('admin/reports.html')

@bp.route('/reports/sales')
@login_required
@admin_required
def sales_report():
    """Generate sales report"""
    from datetime import datetime, timedelta
    from sqlalchemy import func, extract

    # Get date range from request
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    branch_id = request.args.get('branch_id', type=int)

    # Default to last 30 days if no dates provided
    if not start_date or not end_date:
        end_date = datetime.utcnow()
        start_date = end_date - timedelta(days=30)
    else:
        start_date = datetime.strptime(start_date, '%Y-%m-%d')
        end_date = datetime.strptime(end_date, '%Y-%m-%d')

    # Build query
    query = Order.query.filter(
        Order.created_at >= start_date,
        Order.created_at <= end_date,
        Order.status.in_(['confirmed', 'picked', 'out_for_delivery', 'delivered'])
    )

    if branch_id:
        query = query.filter(Order.branch_id == branch_id)

    orders = query.all()

    # Calculate metrics
    total_sales = sum(order.total_amount for order in orders)
    total_orders = len(orders)
    avg_order_value = total_sales / total_orders if total_orders > 0 else 0

    # Sales by day
    daily_sales = db.session.query(
        func.date(Order.created_at).label('date'),
        func.sum(Order.total_amount).label('total'),
        func.count(Order.id).label('orders')
    ).filter(
        Order.created_at >= start_date,
        Order.created_at <= end_date,
        Order.status.in_(['confirmed', 'picked', 'out_for_delivery', 'delivered'])
    )

    if branch_id:
        daily_sales = daily_sales.filter(Order.branch_id == branch_id)

    daily_sales = daily_sales.group_by(func.date(Order.created_at)).all()

    # Top products
    top_products = db.session.query(
        Product.title,
        func.sum(OrderItem.quantity).label('quantity_sold'),
        func.sum(OrderItem.quantity * OrderItem.price).label('revenue')
    ).join(OrderItem).join(Order).filter(
        Order.created_at >= start_date,
        Order.created_at <= end_date,
        Order.status.in_(['confirmed', 'picked', 'out_for_delivery', 'delivered'])
    )

    if branch_id:
        top_products = top_products.filter(Order.branch_id == branch_id)

    top_products = top_products.group_by(Product.id, Product.title)\
                              .order_by(func.sum(OrderItem.quantity).desc())\
                              .limit(10).all()

    # Sales by branch
    branch_sales = db.session.query(
        Branch.name,
        func.sum(Order.total_amount).label('total'),
        func.count(Order.id).label('orders')
    ).join(Order).filter(
        Order.created_at >= start_date,
        Order.created_at <= end_date,
        Order.status.in_(['confirmed', 'picked', 'out_for_delivery', 'delivered'])
    ).group_by(Branch.id, Branch.name).all()

    branches = Branch.query.filter_by(is_active=True).all()

    report_data = {
        'start_date': start_date,
        'end_date': end_date,
        'total_sales': total_sales,
        'total_orders': total_orders,
        'avg_order_value': avg_order_value,
        'daily_sales': daily_sales,
        'top_products': top_products,
        'branch_sales': branch_sales,
        'selected_branch': branch_id
    }

    return render_template('admin/sales_report.html',
                         report=report_data,
                         branches=branches)

@bp.route('/reports/customer')
@login_required
@admin_required
def customer_report():
    """Generate customer report"""
    from datetime import datetime, timedelta
    from sqlalchemy import func, extract

    # Get date range from request
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    customer_type = request.args.get('customer_type')  # client, reseller, all

    # Default to last 30 days if no dates provided
    if not start_date or not end_date:
        end_date = datetime.utcnow()
        start_date = end_date - timedelta(days=30)
    else:
        start_date = datetime.strptime(start_date, '%Y-%m-%d')
        end_date = datetime.strptime(end_date, '%Y-%m-%d')

    # Build customer query
    customer_query = User.query.filter(User.role.in_(['client', 'reseller']))

    if customer_type and customer_type != 'all':
        customer_query = customer_query.filter(User.role == customer_type)

    customers = customer_query.all()

    # Customer analytics
    total_customers = len(customers)
    new_customers = len([c for c in customers if c.created_at >= start_date])
    active_customers = len([c for c in customers if c.orders.filter(
        Order.created_at >= start_date,
        Order.created_at <= end_date
    ).count() > 0])

    # Customer segmentation by order value
    high_value_customers = []
    medium_value_customers = []
    low_value_customers = []

    for customer in customers:
        total_spent = sum(order.total_amount for order in customer.orders.filter(
            Order.created_at >= start_date,
            Order.created_at <= end_date,
            Order.status.in_(['confirmed', 'picked', 'out_for_delivery', 'delivered'])
        ).all())

        customer_data = {
            'customer': customer,
            'total_spent': total_spent,
            'order_count': customer.orders.filter(
                Order.created_at >= start_date,
                Order.created_at <= end_date
            ).count(),
            'avg_order_value': total_spent / max(1, customer.orders.filter(
                Order.created_at >= start_date,
                Order.created_at <= end_date
            ).count())
        }

        if total_spent >= 1000:
            high_value_customers.append(customer_data)
        elif total_spent >= 500:
            medium_value_customers.append(customer_data)
        else:
            low_value_customers.append(customer_data)

    # Sort by total spent
    high_value_customers.sort(key=lambda x: x['total_spent'], reverse=True)
    medium_value_customers.sort(key=lambda x: x['total_spent'], reverse=True)
    low_value_customers.sort(key=lambda x: x['total_spent'], reverse=True)

    # Top customers by orders
    top_customers_by_orders = db.session.query(
        User.id,
        User.username,
        User.first_name,
        User.last_name,
        User.email,
        User.role,
        func.count(Order.id).label('order_count'),
        func.sum(Order.total_amount).label('total_spent')
    ).join(Order).filter(
        Order.created_at >= start_date,
        Order.created_at <= end_date,
        Order.status.in_(['confirmed', 'picked', 'out_for_delivery', 'delivered'])
    ).group_by(User.id).order_by(func.count(Order.id).desc()).limit(10).all()

    # Customer registration trends
    registration_trends = db.session.query(
        func.date(User.created_at).label('date'),
        func.count(User.id).label('registrations')
    ).filter(
        User.created_at >= start_date,
        User.created_at <= end_date,
        User.role.in_(['client', 'reseller'])
    ).group_by(func.date(User.created_at)).all()

    report_data = {
        'start_date': start_date,
        'end_date': end_date,
        'total_customers': total_customers,
        'new_customers': new_customers,
        'active_customers': active_customers,
        'high_value_customers': high_value_customers[:10],
        'medium_value_customers': medium_value_customers[:10],
        'low_value_customers': low_value_customers[:10],
        'top_customers_by_orders': top_customers_by_orders,
        'registration_trends': registration_trends,
        'customer_type': customer_type
    }

    return render_template('admin/customer_report.html', report=report_data)

@bp.route('/reports/branch')
@login_required
@admin_required
def branch_report():
    """Generate branch performance report"""
    from datetime import datetime, timedelta
    from sqlalchemy import func

    # Get date range from request
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')

    # Default to last 30 days if no dates provided
    if not start_date or not end_date:
        end_date = datetime.utcnow()
        start_date = end_date - timedelta(days=30)
    else:
        start_date = datetime.strptime(start_date, '%Y-%m-%d')
        end_date = datetime.strptime(end_date, '%Y-%m-%d')

    branches = Branch.query.filter_by(is_active=True).all()
    branch_performance = []

    for branch in branches:
        # Sales metrics
        branch_orders = Order.query.filter(
            Order.branch_id == branch.id,
            Order.created_at >= start_date,
            Order.created_at <= end_date,
            Order.status.in_(['confirmed', 'picked', 'out_for_delivery', 'delivered'])
        ).all()

        total_sales = sum(order.total_amount for order in branch_orders)
        total_orders = len(branch_orders)
        avg_order_value = total_sales / max(1, total_orders)

        # Inventory metrics
        total_products = ProductStock.query.filter_by(branch_id=branch.id).count()
        low_stock_items = len([stock for stock in ProductStock.query.filter_by(branch_id=branch.id).all()
                              if stock.is_low_stock()])
        out_of_stock_items = ProductStock.query.filter_by(branch_id=branch.id, quantity=0).count()

        # Delivery performance
        delivered_orders = Order.query.filter(
            Order.branch_id == branch.id,
            Order.status == 'delivered',
            Order.created_at >= start_date,
            Order.created_at <= end_date
        ).count()

        delivery_rate = (delivered_orders / max(1, total_orders)) * 100

        branch_performance.append({
            'branch': branch,
            'total_sales': total_sales,
            'total_orders': total_orders,
            'avg_order_value': avg_order_value,
            'total_products': total_products,
            'low_stock_items': low_stock_items,
            'out_of_stock_items': out_of_stock_items,
            'delivery_rate': delivery_rate
        })

    # Sort by total sales
    branch_performance.sort(key=lambda x: x['total_sales'], reverse=True)

    report_data = {
        'start_date': start_date,
        'end_date': end_date,
        'branch_performance': branch_performance
    }

    return render_template('admin/branch_report.html', report=report_data)

@bp.route('/settings')
@login_required
@admin_required
def settings():
    """System settings management"""
    from app.models import SystemSetting

    # Get all settings grouped by category
    settings = SystemSetting.query.all()

    # Group settings by category (based on key prefix)
    grouped_settings = {
        'general': [],
        'email': [],
        'payment': [],
        'inventory': [],
        'security': [],
        'other': []
    }

    for setting in settings:
        if setting.key.startswith('email_'):
            grouped_settings['email'].append(setting)
        elif setting.key.startswith('payment_'):
            grouped_settings['payment'].append(setting)
        elif setting.key.startswith('inventory_'):
            grouped_settings['inventory'].append(setting)
        elif setting.key.startswith('security_'):
            grouped_settings['security'].append(setting)
        elif setting.key.startswith('general_'):
            grouped_settings['general'].append(setting)
        else:
            grouped_settings['other'].append(setting)

    return render_template('admin/settings.html', grouped_settings=grouped_settings)

@bp.route('/settings/update', methods=['POST'])
@login_required
@admin_required
def update_settings():
    """Update system settings"""
    from app.models import SystemSetting

    for key, value in request.form.items():
        if key != 'csrf_token':
            SystemSetting.set_setting(key, value)

    flash('Settings updated successfully!', 'success')
    return redirect(url_for('admin.settings'))

@bp.route('/settings/reset', methods=['POST'])
@login_required
@admin_required
def reset_settings():
    """Reset settings to default values"""
    from app.models import SystemSetting

    # Default settings
    default_settings = {
        'general_site_name': 'YalaOffice',
        'general_site_description': 'Office & School Supplies',
        'general_currency': 'Dh',
        'general_timezone': 'Africa/Casablanca',
        'general_language': 'en',
        'email_smtp_server': 'smtp.gmail.com',
        'email_smtp_port': '587',
        'email_use_tls': 'true',
        'email_from_name': 'YalaOffice',
        'email_from_address': '<EMAIL>',
        'payment_cash_enabled': 'true',
        'payment_check_enabled': 'true',
        'payment_bank_transfer_enabled': 'true',
        'inventory_low_stock_threshold': '10',
        'inventory_auto_reorder': 'false',
        'security_password_min_length': '6',
        'security_session_timeout': '30',
        'security_max_login_attempts': '5'
    }

    for key, value in default_settings.items():
        SystemSetting.set_setting(key, value)

    flash('Settings reset to default values!', 'info')
    return redirect(url_for('admin.settings'))

@bp.route('/bulk_operations')
@login_required
@admin_required
def bulk_operations():
    """Bulk operations dashboard"""
    return render_template('admin/bulk_operations.html')

@bp.route('/bulk_operations/products', methods=['POST'])
@login_required
@admin_required
def bulk_update_products():
    """Bulk update products"""
    action = request.form.get('action')
    product_ids = request.form.getlist('product_ids')

    if not product_ids:
        flash('No products selected.', 'error')
        return redirect(url_for('admin.bulk_operations'))

    products = Product.query.filter(Product.id.in_(product_ids)).all()

    if action == 'activate':
        for product in products:
            product.is_active = True
        flash(f'{len(products)} products activated.', 'success')

    elif action == 'deactivate':
        for product in products:
            product.is_active = False
        flash(f'{len(products)} products deactivated.', 'success')

    elif action == 'delete':
        for product in products:
            db.session.delete(product)
        flash(f'{len(products)} products deleted.', 'success')

    elif action == 'update_category':
        new_category_id = request.form.get('new_category_id')
        if new_category_id:
            for product in products:
                product.category_id = int(new_category_id)
            flash(f'{len(products)} products moved to new category.', 'success')

    elif action == 'update_prices':
        price_change_type = request.form.get('price_change_type')  # percentage, fixed
        price_change_value = float(request.form.get('price_change_value', 0))

        for product in products:
            if price_change_type == 'percentage':
                product.normal_price *= (1 + price_change_value / 100)
                product.reseller_price *= (1 + price_change_value / 100)
            else:
                product.normal_price += price_change_value
                product.reseller_price += price_change_value

        flash(f'Prices updated for {len(products)} products.', 'success')

    db.session.commit()
    return redirect(url_for('admin.bulk_operations'))

@bp.route('/bulk_operations/users', methods=['POST'])
@login_required
@admin_required
def bulk_update_users():
    """Bulk update users"""
    action = request.form.get('action')
    user_ids = request.form.getlist('user_ids')

    if not user_ids:
        flash('No users selected.', 'error')
        return redirect(url_for('admin.bulk_operations'))

    users = User.query.filter(User.id.in_(user_ids)).all()

    if action == 'activate':
        for user in users:
            user.is_active = True
        flash(f'{len(users)} users activated.', 'success')

    elif action == 'deactivate':
        for user in users:
            user.is_active = False
        flash(f'{len(users)} users deactivated.', 'success')

    elif action == 'change_role':
        new_role = request.form.get('new_role')
        if new_role:
            for user in users:
                user.role = new_role
            flash(f'{len(users)} users role changed to {new_role}.', 'success')

    elif action == 'send_notification':
        title = request.form.get('notification_title')
        message = request.form.get('notification_message')

        if title and message:
            from app.models import Notification
            for user in users:
                notification = Notification(
                    user_id=user.id,
                    title=title,
                    message=message,
                    type='info'
                )
                db.session.add(notification)

            flash(f'Notification sent to {len(users)} users.', 'success')

    db.session.commit()
    return redirect(url_for('admin.bulk_operations'))

@bp.route('/export/<data_type>')
@login_required
@admin_required
def export_data(data_type):
    """Export data in various formats"""
    format_type = request.args.get('format', 'csv')

    if data_type == 'products':
        return export_products(format_type)
    elif data_type == 'users':
        return export_users(format_type)
    elif data_type == 'orders':
        return export_orders(format_type)
    elif data_type == 'inventory':
        return export_inventory(format_type)
    else:
        flash('Invalid data type for export.', 'error')
        return redirect(url_for('admin.dashboard'))

def export_products(format_type):
    """Export products data"""
    from flask import make_response
    import csv
    import io

    products = Product.query.all()

    if format_type == 'csv':
        output = io.StringIO()
        writer = csv.writer(output)

        # Write header
        writer.writerow(['ID', 'Title', 'SKU', 'Category', 'Brand', 'Normal Price', 'Reseller Price', 'Status', 'Created At'])

        # Write data
        for product in products:
            writer.writerow([
                product.id,
                product.title,
                product.sku,
                product.category.name if product.category else '',
                product.brand or '',
                product.normal_price,
                product.reseller_price,
                'Active' if product.is_active else 'Inactive',
                product.created_at.strftime('%Y-%m-%d %H:%M:%S') if product.created_at else ''
            ])

        response = make_response(output.getvalue())
        response.headers['Content-Type'] = 'text/csv'
        response.headers['Content-Disposition'] = 'attachment; filename=products_export.csv'
        return response

    elif format_type == 'excel':
        try:
            import pandas as pd
            from io import BytesIO

            # Create DataFrame
            data = []
            for product in products:
                data.append({
                    'ID': product.id,
                    'Title': product.title,
                    'SKU': product.sku,
                    'Category': product.category.name if product.category else '',
                    'Brand': product.brand or '',
                    'Normal Price': product.normal_price,
                    'Reseller Price': product.reseller_price,
                    'Status': 'Active' if product.is_active else 'Inactive',
                    'Created At': product.created_at.strftime('%Y-%m-%d %H:%M:%S') if product.created_at else ''
                })

            df = pd.DataFrame(data)

            # Create Excel file
            output = BytesIO()
            with pd.ExcelWriter(output, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='Products', index=False)

            response = make_response(output.getvalue())
            response.headers['Content-Type'] = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            response.headers['Content-Disposition'] = 'attachment; filename=products_export.xlsx'
            return response

        except ImportError:
            flash('Excel export requires pandas and openpyxl. Please install them.', 'error')
            return redirect(url_for('admin.dashboard'))

    elif format_type == 'pdf':
        try:
            from reportlab.lib.pagesizes import letter, A4
            from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph
            from reportlab.lib.styles import getSampleStyleSheet
            from reportlab.lib import colors
            from io import BytesIO

            buffer = BytesIO()
            doc = SimpleDocTemplate(buffer, pagesize=A4)
            elements = []

            # Title
            styles = getSampleStyleSheet()
            title = Paragraph("Products Export Report", styles['Title'])
            elements.append(title)

            # Table data
            data = [['ID', 'Title', 'Category', 'Price', 'Status']]
            for product in products:
                data.append([
                    str(product.id),
                    product.title[:30] + '...' if len(product.title) > 30 else product.title,
                    product.category.name if product.category else '',
                    f"{product.normal_price} Dh",
                    'Active' if product.is_active else 'Inactive'
                ])

            # Create table
            table = Table(data)
            table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 14),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))

            elements.append(table)
            doc.build(elements)

            response = make_response(buffer.getvalue())
            response.headers['Content-Type'] = 'application/pdf'
            response.headers['Content-Disposition'] = 'attachment; filename=products_export.pdf'
            return response

        except ImportError:
            flash('PDF export requires reportlab. Please install it.', 'error')
            return redirect(url_for('admin.dashboard'))

def export_users(format_type):
    """Export users data"""
    from flask import make_response
    import csv
    import io

    users = User.query.filter(User.role.in_(['client', 'reseller', 'delivery', 'manager'])).all()

    if format_type == 'csv':
        output = io.StringIO()
        writer = csv.writer(output)

        # Write header
        writer.writerow(['ID', 'Username', 'Email', 'First Name', 'Last Name', 'Role', 'Status', 'Created At'])

        # Write data
        for user in users:
            writer.writerow([
                user.id,
                user.username,
                user.email,
                user.first_name or '',
                user.last_name or '',
                user.role,
                'Active' if user.is_active else 'Inactive',
                user.created_at.strftime('%Y-%m-%d %H:%M:%S') if user.created_at else ''
            ])

        response = make_response(output.getvalue())
        response.headers['Content-Type'] = 'text/csv'
        response.headers['Content-Disposition'] = 'attachment; filename=users_export.csv'
        return response

    return redirect(url_for('admin.dashboard'))

def export_orders(format_type):
    """Export orders data"""
    from flask import make_response
    import csv
    import io

    orders = Order.query.all()

    if format_type == 'csv':
        output = io.StringIO()
        writer = csv.writer(output)

        # Write header
        writer.writerow(['Order Number', 'Customer', 'Branch', 'Total Amount', 'Status', 'Created At'])

        # Write data
        for order in orders:
            writer.writerow([
                order.order_number,
                order.user.get_full_name(),
                order.branch.name if order.branch else '',
                order.total_amount,
                order.status,
                order.created_at.strftime('%Y-%m-%d %H:%M:%S') if order.created_at else ''
            ])

        response = make_response(output.getvalue())
        response.headers['Content-Type'] = 'text/csv'
        response.headers['Content-Disposition'] = 'attachment; filename=orders_export.csv'
        return response

    return redirect(url_for('admin.dashboard'))

def export_inventory(format_type):
    """Export inventory data"""
    from flask import make_response
    import csv
    import io

    inventory_items = db.session.query(
        Product.title,
        Product.sku,
        Category.name.label('category'),
        Branch.name.label('branch'),
        ProductStock.quantity,
        ProductStock.min_stock_level,
        Product.normal_price
    ).join(ProductStock).join(Product).join(Category).join(Branch).all()

    if format_type == 'csv':
        output = io.StringIO()
        writer = csv.writer(output)

        # Write header
        writer.writerow(['Product', 'SKU', 'Category', 'Branch', 'Current Stock', 'Min Level', 'Unit Price', 'Total Value'])

        # Write data
        for item in inventory_items:
            total_value = item.quantity * item.normal_price
            writer.writerow([
                item.title,
                item.sku,
                item.category,
                item.branch,
                item.quantity,
                item.min_stock_level,
                item.normal_price,
                total_value
            ])

        response = make_response(output.getvalue())
        response.headers['Content-Type'] = 'text/csv'
        response.headers['Content-Disposition'] = 'attachment; filename=inventory_export.csv'
        return response

    return redirect(url_for('admin.dashboard'))

@bp.route('/performance_monitoring')
@login_required
@admin_required
def performance_monitoring():
    """Advanced performance monitoring dashboard"""
    from datetime import datetime, timedelta
    from sqlalchemy import func, text
    import psutil
    import os

    # System Performance Metrics
    cpu_usage = psutil.cpu_percent(interval=1)
    memory = psutil.virtual_memory()
    disk = psutil.disk_usage('/')

    # Database Performance
    db_stats = {
        'total_tables': db.session.execute(text("SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public'")).scalar(),
        'total_records': 0,
        'database_size': 0
    }

    # Application Performance Metrics
    end_date = datetime.utcnow()
    start_date = end_date - timedelta(days=7)

    # Response time simulation (in real app, this would come from logs)
    avg_response_time = 250  # milliseconds

    # Error rate simulation
    error_rate = 0.5  # percentage

    # User activity metrics
    active_users_today = User.query.filter(
        User.last_login >= datetime.utcnow().date()
    ).count()

    total_users = User.query.count()

    # Order processing metrics
    orders_today = Order.query.filter(
        func.date(Order.created_at) == datetime.utcnow().date()
    ).count()

    avg_order_processing_time = db.session.query(
        func.avg(
            func.extract('epoch', Order.confirmed_at) -
            func.extract('epoch', Order.created_at)
        )
    ).filter(
        Order.confirmed_at.isnot(None),
        Order.created_at >= start_date
    ).scalar() or 0

    # Convert to minutes
    avg_order_processing_time = avg_order_processing_time / 60 if avg_order_processing_time else 0

    # Inventory turnover
    low_stock_items = db.session.query(ProductStock).filter(
        ProductStock.quantity <= ProductStock.min_stock_level
    ).count()

    total_products = Product.query.count()

    # Revenue metrics
    revenue_today = db.session.query(func.sum(Order.total_amount)).filter(
        func.date(Order.created_at) == datetime.utcnow().date(),
        Order.status.in_(['confirmed', 'picked', 'out_for_delivery', 'delivered'])
    ).scalar() or 0

    revenue_week = db.session.query(func.sum(Order.total_amount)).filter(
        Order.created_at >= start_date,
        Order.status.in_(['confirmed', 'picked', 'out_for_delivery', 'delivered'])
    ).scalar() or 0

    # Performance trends (last 7 days)
    daily_metrics = []
    for i in range(7):
        date = (datetime.utcnow() - timedelta(days=i)).date()

        daily_orders = Order.query.filter(func.date(Order.created_at) == date).count()
        daily_revenue = db.session.query(func.sum(Order.total_amount)).filter(
            func.date(Order.created_at) == date,
            Order.status.in_(['confirmed', 'picked', 'out_for_delivery', 'delivered'])
        ).scalar() or 0

        daily_metrics.append({
            'date': date,
            'orders': daily_orders,
            'revenue': float(daily_revenue),
            'avg_response_time': avg_response_time + (i * 10),  # Simulated variation
            'error_rate': max(0, error_rate - (i * 0.1))  # Simulated improvement
        })

    daily_metrics.reverse()  # Show oldest to newest

    performance_data = {
        'system': {
            'cpu_usage': cpu_usage,
            'memory_usage': memory.percent,
            'memory_available': memory.available // (1024**3),  # GB
            'disk_usage': disk.percent,
            'disk_free': disk.free // (1024**3)  # GB
        },
        'database': db_stats,
        'application': {
            'avg_response_time': avg_response_time,
            'error_rate': error_rate,
            'uptime': '99.9%',  # Simulated
            'active_users_today': active_users_today,
            'total_users': total_users,
            'user_activity_rate': (active_users_today / total_users * 100) if total_users > 0 else 0
        },
        'business': {
            'orders_today': orders_today,
            'revenue_today': float(revenue_today),
            'revenue_week': float(revenue_week),
            'avg_order_processing_time': avg_order_processing_time,
            'low_stock_items': low_stock_items,
            'total_products': total_products,
            'inventory_health': ((total_products - low_stock_items) / total_products * 100) if total_products > 0 else 0
        },
        'trends': daily_metrics
    }

    return render_template('admin/performance_monitoring.html', data=performance_data)

@bp.route('/system_health')
@login_required
@admin_required
def system_health():
    """System health check endpoint"""
    from flask import jsonify
    from datetime import datetime
    import psutil

    try:
        # Check database connection
        from sqlalchemy import text
        db.session.execute(text('SELECT 1'))
        db_status = 'healthy'
    except Exception as e:
        db_status = f'error: {str(e)}'

    # Check system resources
    cpu_usage = psutil.cpu_percent()
    memory = psutil.virtual_memory()
    disk = psutil.disk_usage('/')

    health_status = {
        'overall': 'healthy',
        'database': db_status,
        'cpu': {
            'usage': cpu_usage,
            'status': 'healthy' if cpu_usage < 80 else 'warning' if cpu_usage < 95 else 'critical'
        },
        'memory': {
            'usage': memory.percent,
            'status': 'healthy' if memory.percent < 80 else 'warning' if memory.percent < 95 else 'critical'
        },
        'disk': {
            'usage': disk.percent,
            'status': 'healthy' if disk.percent < 80 else 'warning' if disk.percent < 95 else 'critical'
        },
        'timestamp': datetime.utcnow().isoformat()
    }

    # Determine overall status
    statuses = [health_status['cpu']['status'], health_status['memory']['status'], health_status['disk']['status']]
    if 'critical' in statuses:
        health_status['overall'] = 'critical'
    elif 'warning' in statuses:
        health_status['overall'] = 'warning'
    elif db_status != 'healthy':
        health_status['overall'] = 'critical'

    return jsonify(health_status)
