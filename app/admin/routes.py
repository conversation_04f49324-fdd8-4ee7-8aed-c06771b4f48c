from flask import render_template, redirect, url_for, flash, request, jsonify
from flask_login import login_required, current_user
from functools import wraps
from app.admin import bp
from app.models import User, Product, Category, Branch, Order, ProductStock, OrderItem, PromoCode
from app import db

def admin_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated or not current_user.is_admin():
            flash('Access denied. Admin privileges required.', 'error')
            return redirect(url_for('main.index'))
        return f(*args, **kwargs)
    return decorated_function

@bp.route('/dashboard')
@login_required
@admin_required
def dashboard():
    """Admin dashboard with system overview"""
    # Get key statistics
    total_users = User.query.count()
    total_products = Product.query.filter_by(is_active=True).count()
    total_orders = Order.query.count()
    pending_orders = Order.query.filter_by(status='pending').count()
    
    # Recent orders
    recent_orders = Order.query.order_by(Order.created_at.desc()).limit(5).all()
    
    # Low stock products
    low_stock_products = []
    for stock in ProductStock.query.all():
        if stock.is_low_stock():
            low_stock_products.append(stock)
    
    return render_template('admin/dashboard.html',
                         total_users=total_users,
                         total_products=total_products,
                         total_orders=total_orders,
                         pending_orders=pending_orders,
                         recent_orders=recent_orders,
                         low_stock_products=low_stock_products)

@bp.route('/users')
@login_required
@admin_required
def users():
    """User management page"""
    page = request.args.get('page', 1, type=int)
    users = User.query.paginate(
        page=page, per_page=20, error_out=False
    )
    return render_template('admin/users.html', users=users)

@bp.route('/products')
@login_required
@admin_required
def products():
    """Product management page"""
    page = request.args.get('page', 1, type=int)
    products = Product.query.paginate(
        page=page, per_page=20, error_out=False
    )
    return render_template('admin/products.html', products=products)

@bp.route('/orders')
@login_required
@admin_required
def orders():
    """Order management page"""
    page = request.args.get('page', 1, type=int)
    status = request.args.get('status')
    
    query = Order.query
    if status:
        query = query.filter_by(status=status)
    
    orders = query.order_by(Order.created_at.desc()).paginate(
        page=page, per_page=20, error_out=False
    )
    return render_template('admin/orders.html', orders=orders, current_status=status)

@bp.route('/branches')
@login_required
@admin_required
def branches():
    """Branch management page"""
    branches = Branch.query.all()
    return render_template('admin/branches.html', branches=branches)

@bp.route('/categories')
@login_required
@admin_required
def categories():
    """Category management page"""
    categories = Category.query.all()
    return render_template('admin/categories.html', categories=categories)

@bp.route('/product/add', methods=['GET', 'POST'])
@login_required
@admin_required
def add_product():
    """Add new product"""
    if request.method == 'POST':
        title = request.form.get('title')
        description = request.form.get('description')
        brand = request.form.get('brand')
        sku = request.form.get('sku')
        normal_price = float(request.form.get('normal_price', 0))
        reseller_price = float(request.form.get('reseller_price', 0))
        category_id = int(request.form.get('category_id'))

        # Create product
        product = Product(
            title=title,
            description=description,
            brand=brand,
            sku=sku,
            normal_price=normal_price,
            reseller_price=reseller_price,
            category_id=category_id
        )

        db.session.add(product)
        db.session.flush()

        # Add initial stock for all branches
        branches = Branch.query.filter_by(is_active=True).all()
        for branch in branches:
            initial_stock = int(request.form.get(f'stock_{branch.id}', 0))
            stock = ProductStock(
                product_id=product.id,
                branch_id=branch.id,
                quantity=initial_stock,
                min_stock_level=10
            )
            db.session.add(stock)

        db.session.commit()
        flash('Product added successfully!', 'success')
        return redirect(url_for('admin.products'))

    categories = Category.query.filter_by(is_active=True).all()
    branches = Branch.query.filter_by(is_active=True).all()
    return render_template('admin/add_product.html', categories=categories, branches=branches)

@bp.route('/product/edit/<int:id>', methods=['GET', 'POST'])
@login_required
@admin_required
def edit_product(id):
    """Edit product"""
    product = Product.query.get_or_404(id)

    if request.method == 'POST':
        product.title = request.form.get('title')
        product.description = request.form.get('description')
        product.brand = request.form.get('brand')
        product.sku = request.form.get('sku')
        product.normal_price = float(request.form.get('normal_price', 0))
        product.reseller_price = float(request.form.get('reseller_price', 0))
        product.category_id = int(request.form.get('category_id'))
        product.is_active = 'is_active' in request.form

        # Update stock for all branches
        branches = Branch.query.filter_by(is_active=True).all()
        for branch in branches:
            stock = ProductStock.query.filter_by(
                product_id=product.id,
                branch_id=branch.id
            ).first()

            new_quantity = int(request.form.get(f'stock_{branch.id}', 0))
            if stock:
                stock.quantity = new_quantity
            else:
                stock = ProductStock(
                    product_id=product.id,
                    branch_id=branch.id,
                    quantity=new_quantity,
                    min_stock_level=10
                )
                db.session.add(stock)

        db.session.commit()
        flash('Product updated successfully!', 'success')
        return redirect(url_for('admin.products'))

    categories = Category.query.filter_by(is_active=True).all()
    branches = Branch.query.filter_by(is_active=True).all()
    return render_template('admin/edit_product.html', product=product, categories=categories, branches=branches)

@bp.route('/user/add', methods=['GET', 'POST'])
@login_required
@admin_required
def add_user():
    """Add new user"""
    if request.method == 'POST':
        username = request.form.get('username')
        email = request.form.get('email')
        first_name = request.form.get('first_name')
        last_name = request.form.get('last_name')
        phone = request.form.get('phone')
        address = request.form.get('address')
        role = request.form.get('role')
        branch_id = request.form.get('branch_id') or None
        password = request.form.get('password')

        # Check if username or email already exists
        if User.query.filter_by(username=username).first():
            flash('Username already exists.', 'error')
            return redirect(url_for('admin.add_user'))

        if User.query.filter_by(email=email).first():
            flash('Email already exists.', 'error')
            return redirect(url_for('admin.add_user'))

        user = User(
            username=username,
            email=email,
            first_name=first_name,
            last_name=last_name,
            phone=phone,
            address=address,
            role=role,
            branch_id=int(branch_id) if branch_id else None
        )
        user.set_password(password)

        db.session.add(user)
        db.session.commit()

        flash('User added successfully!', 'success')
        return redirect(url_for('admin.users'))

    branches = Branch.query.filter_by(is_active=True).all()
    return render_template('admin/add_user.html', branches=branches)

@bp.route('/analytics')
@login_required
@admin_required
def analytics():
    """Analytics dashboard"""
    from datetime import datetime, timedelta
    from sqlalchemy import func, extract

    # Date range for analytics (last 30 days)
    end_date = datetime.utcnow()
    start_date = end_date - timedelta(days=30)

    # Sales Analytics
    total_revenue = db.session.query(func.sum(Order.total_amount))\
                             .filter(Order.status == 'delivered').scalar() or 0

    monthly_revenue = db.session.query(func.sum(Order.total_amount))\
                               .filter(Order.status == 'delivered',
                                      Order.created_at >= start_date).scalar() or 0

    # Order Analytics
    total_orders = Order.query.count()
    monthly_orders = Order.query.filter(Order.created_at >= start_date).count()
    pending_orders = Order.query.filter_by(status='pending').count()
    delivered_orders = Order.query.filter_by(status='delivered').count()

    # Product Analytics
    total_products = Product.query.filter_by(is_active=True).count()
    low_stock_count = len([stock for stock in ProductStock.query.all() if stock.is_low_stock()])

    # Customer Analytics
    total_customers = User.query.filter(User.role.in_(['client', 'reseller'])).count()
    new_customers = User.query.filter(User.role.in_(['client', 'reseller']),
                                     User.created_at >= start_date).count()

    # Top selling products
    top_products = db.session.query(
        Product.title,
        func.sum(OrderItem.quantity).label('total_sold'),
        func.sum(OrderItem.total_price).label('total_revenue')
    ).join(OrderItem).join(Order)\
     .filter(Order.status == 'delivered')\
     .group_by(Product.id, Product.title)\
     .order_by(func.sum(OrderItem.quantity).desc())\
     .limit(10).all()

    # Sales by category
    category_sales = db.session.query(
        Category.name,
        func.sum(OrderItem.total_price).label('total_revenue'),
        func.count(OrderItem.id).label('total_items')
    ).join(Product).join(OrderItem).join(Order)\
     .filter(Order.status == 'delivered')\
     .group_by(Category.id, Category.name)\
     .order_by(func.sum(OrderItem.total_price).desc()).all()

    # Monthly sales data for chart
    monthly_sales = db.session.query(
        extract('month', Order.created_at).label('month'),
        func.sum(Order.total_amount).label('revenue'),
        func.count(Order.id).label('orders')
    ).filter(Order.status == 'delivered',
            Order.created_at >= start_date)\
     .group_by(extract('month', Order.created_at))\
     .order_by(extract('month', Order.created_at)).all()

    # Branch performance
    branch_performance = db.session.query(
        Branch.name,
        func.count(Order.id).label('total_orders'),
        func.sum(Order.total_amount).label('total_revenue')
    ).join(Order).filter(Order.status == 'delivered')\
     .group_by(Branch.id, Branch.name)\
     .order_by(func.sum(Order.total_amount).desc()).all()

    return render_template('admin/analytics.html',
                         total_revenue=total_revenue,
                         monthly_revenue=monthly_revenue,
                         total_orders=total_orders,
                         monthly_orders=monthly_orders,
                         pending_orders=pending_orders,
                         delivered_orders=delivered_orders,
                         total_products=total_products,
                         low_stock_count=low_stock_count,
                         total_customers=total_customers,
                         new_customers=new_customers,
                         top_products=top_products,
                         category_sales=category_sales,
                         monthly_sales=monthly_sales,
                         branch_performance=branch_performance)

@bp.route('/promo-codes')
@login_required
@admin_required
def promo_codes():
    """Promo code management"""
    page = request.args.get('page', 1, type=int)
    promo_codes = PromoCode.query.order_by(PromoCode.created_at.desc())\
                                .paginate(page=page, per_page=20, error_out=False)
    return render_template('admin/promo_codes.html', promo_codes=promo_codes)

@bp.route('/promo-code/add', methods=['GET', 'POST'])
@login_required
@admin_required
def add_promo_code():
    """Add new promo code"""
    if request.method == 'POST':
        from datetime import datetime

        code = request.form.get('code').upper()
        description = request.form.get('description')
        discount_type = request.form.get('discount_type')
        discount_value = float(request.form.get('discount_value'))
        min_order_amount = float(request.form.get('min_order_amount', 0))
        max_discount_amount = request.form.get('max_discount_amount')
        usage_limit = request.form.get('usage_limit')
        valid_until = request.form.get('valid_until')

        # Check if code already exists
        if PromoCode.query.filter_by(code=code).first():
            flash('Promo code already exists.', 'error')
            return redirect(url_for('admin.add_promo_code'))

        promo = PromoCode(
            code=code,
            description=description,
            discount_type=discount_type,
            discount_value=discount_value,
            min_order_amount=min_order_amount,
            max_discount_amount=float(max_discount_amount) if max_discount_amount else None,
            usage_limit=int(usage_limit) if usage_limit else None,
            valid_until=datetime.strptime(valid_until, '%Y-%m-%d') if valid_until else None
        )

        db.session.add(promo)
        db.session.commit()

        flash('Promo code created successfully!', 'success')
        return redirect(url_for('admin.promo_codes'))

    return render_template('admin/add_promo_code.html')
