from flask import jsonify, request
from flask_login import login_required, current_user
from app.api import bp
from app.models import Product, Category, Order, PromoCode, User, ProductStock, OrderItem
from app import db

@bp.route('/products')
def products():
    """API endpoint for products"""
    category_id = request.args.get('category_id', type=int)
    search = request.args.get('search', '')
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    
    query = Product.query.filter_by(is_active=True)
    
    if category_id:
        query = query.filter_by(category_id=category_id)
    
    if search:
        query = query.filter(Product.title.contains(search))
    
    products = query.paginate(page=page, per_page=per_page, error_out=False)
    
    return jsonify({
        'products': [{
            'id': p.id,
            'title': p.title,
            'description': p.description,
            'normal_price': float(p.normal_price),
            'reseller_price': float(p.reseller_price),
            'main_image': p.main_image,
            'category': p.category.name,
            'brand': p.brand
        } for p in products.items],
        'total': products.total,
        'pages': products.pages,
        'current_page': products.page
    })

@bp.route('/categories')
def categories():
    """API endpoint for categories"""
    categories = Category.query.filter_by(is_active=True).all()
    
    return jsonify({
        'categories': [{
            'id': c.id,
            'name': c.name,
            'description': c.description,
            'image_url': c.image_url
        } for c in categories]
    })

@bp.route('/validate_promo', methods=['POST'])
@login_required
def validate_promo():
    """Validate promo code"""
    code = request.json.get('code')
    order_amount = request.json.get('order_amount', 0)
    
    if not code:
        return jsonify({'valid': False, 'message': 'Promo code is required'})
    
    promo = PromoCode.query.filter_by(code=code.upper()).first()
    
    if not promo:
        return jsonify({'valid': False, 'message': 'Invalid promo code'})
    
    is_valid, message = promo.is_valid(order_amount)
    
    if is_valid:
        discount = promo.calculate_discount(order_amount)
        return jsonify({
            'valid': True,
            'message': 'Promo code applied successfully',
            'discount': float(discount),
            'discount_type': promo.discount_type,
            'discount_value': float(promo.discount_value)
        })
    else:
        return jsonify({'valid': False, 'message': message})

@bp.route('/order_status/<int:order_id>')
@login_required
def order_status(order_id):
    """Get order status"""
    order = Order.query.get_or_404(order_id)
    
    # Check if user has access to this order
    if not (current_user.is_admin() or 
            current_user.is_manager() or 
            order.customer_id == current_user.id or 
            order.delivery_person_id == current_user.id):
        return jsonify({'error': 'Access denied'}), 403
    
    return jsonify({
        'id': order.id,
        'order_number': order.order_number,
        'status': order.status,
        'total_amount': float(order.total_amount),
        'created_at': order.created_at.isoformat() if order.created_at else None,
        'confirmed_at': order.confirmed_at.isoformat() if order.confirmed_at else None,
        'picked_at': order.picked_at.isoformat() if order.picked_at else None,
        'out_for_delivery_at': order.out_for_delivery_at.isoformat() if order.out_for_delivery_at else None,
        'delivered_at': order.delivered_at.isoformat() if order.delivered_at else None,
        'delivery_address': order.delivery_address,
        'customer_name': order.customer.get_full_name(),
        'delivery_person': order.delivery_person.get_full_name() if order.delivery_person else None
    })

@bp.route('/delivery-personnel')
@login_required
def delivery_personnel():
    """Get list of delivery personnel"""
    if current_user.role not in ['admin', 'manager']:
        return jsonify({'error': 'Access denied'}), 403

    personnel = User.query.filter_by(role='delivery', is_active=True).all()

    return jsonify([{
        'id': person.id,
        'name': person.get_full_name(),
        'email': person.email
    } for person in personnel])

@bp.route('/stock-levels')
@login_required
def stock_levels():
    """Get stock levels for products"""
    if current_user.role not in ['admin', 'manager']:
        return jsonify({'error': 'Access denied'}), 403

    branch_id = request.args.get('branch_id', type=int)

    query = ProductStock.query.join(Product)
    if branch_id:
        query = query.filter(ProductStock.branch_id == branch_id)
    elif current_user.branch_id:
        query = query.filter(ProductStock.branch_id == current_user.branch_id)

    stock_items = query.all()

    return jsonify([{
        'product_id': stock.product_id,
        'product_title': stock.product.title,
        'branch_id': stock.branch_id,
        'branch_name': stock.branch.name,
        'quantity': stock.quantity,
        'min_stock_level': stock.min_stock_level,
        'is_low_stock': stock.is_low_stock()
    } for stock in stock_items])

@bp.route('/sales-analytics')
@login_required
def sales_analytics():
    """Get sales analytics data"""
    if current_user.role not in ['admin', 'manager']:
        return jsonify({'error': 'Access denied'}), 403

    from datetime import datetime, timedelta
    from sqlalchemy import func

    # Date range
    days = request.args.get('days', 30, type=int)
    end_date = datetime.utcnow()
    start_date = end_date - timedelta(days=days)

    # Sales by day
    daily_sales = db.session.query(
        func.date(Order.created_at).label('date'),
        func.sum(Order.total_amount).label('revenue'),
        func.count(Order.id).label('orders')
    ).filter(
        Order.status == 'delivered',
        Order.created_at >= start_date
    ).group_by(func.date(Order.created_at)).all()

    # Top products
    top_products = db.session.query(
        Product.title,
        func.sum(OrderItem.quantity).label('total_sold')
    ).join(OrderItem).join(Order)\
     .filter(Order.status == 'delivered', Order.created_at >= start_date)\
     .group_by(Product.id, Product.title)\
     .order_by(func.sum(OrderItem.quantity).desc())\
     .limit(10).all()

    return jsonify({
        'daily_sales': [{
            'date': sale.date.isoformat(),
            'revenue': float(sale.revenue),
            'orders': sale.orders
        } for sale in daily_sales],
        'top_products': [{
            'title': product.title,
            'total_sold': product.total_sold
        } for product in top_products]
    })
