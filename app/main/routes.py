from flask import render_template, redirect, url_for, flash, request, current_app
from flask_login import current_user, login_required
from app.main import bp
from app.models import Product, Category, Branch, Order
from app import db

@bp.route('/')
def index():
    """Home page with product categories and featured products"""
    categories = Category.query.filter_by(is_active=True).all()
    featured_products = Product.query.filter_by(is_active=True).limit(8).all()
    branches = Branch.query.filter_by(is_active=True).all()
    
    return render_template('main/index.html', 
                         categories=categories, 
                         featured_products=featured_products,
                         branches=branches)

@bp.route('/products')
def products():
    """Product catalog page"""
    page = request.args.get('page', 1, type=int)
    category_id = request.args.get('category', type=int)
    search = request.args.get('search', '')
    
    query = Product.query.filter_by(is_active=True)
    
    if category_id:
        query = query.filter_by(category_id=category_id)
    
    if search:
        query = query.filter(Product.title.contains(search))
    
    products = query.paginate(
        page=page, per_page=current_app.config['POSTS_PER_PAGE'],
        error_out=False
    )
    
    categories = Category.query.filter_by(is_active=True).all()
    
    return render_template('main/products.html', 
                         products=products, 
                         categories=categories,
                         current_category=category_id,
                         search=search)

@bp.route('/product/<int:id>')
def product_detail(id):
    """Individual product detail page"""
    product = Product.query.get_or_404(id)
    if not product.is_active:
        flash('Product not found.', 'error')
        return redirect(url_for('main.products'))
    
    # Get related products from same category
    related_products = Product.query.filter(
        Product.category_id == product.category_id,
        Product.id != product.id,
        Product.is_active == True
    ).limit(4).all()
    
    return render_template('main/product_detail.html', 
                         product=product, 
                         related_products=related_products)

@bp.route('/about')
def about():
    """About page"""
    return render_template('main/about.html')

@bp.route('/contact')
def contact():
    """Contact page"""
    return render_template('main/contact.html')

@bp.route('/dashboard')
@login_required
def dashboard():
    """User dashboard - redirects to role-specific dashboard"""
    if current_user.is_admin():
        return redirect(url_for('admin.dashboard'))
    elif current_user.is_manager():
        return redirect(url_for('manager.dashboard'))
    elif current_user.is_delivery():
        return redirect(url_for('delivery.dashboard'))
    else:
        return redirect(url_for('client.dashboard'))

@bp.route('/search')
def search():
    """Advanced search functionality"""
    query = request.args.get('q', '')
    category_id = request.args.get('category', type=int)
    min_price = request.args.get('min_price', type=float)
    max_price = request.args.get('max_price', type=float)
    brand = request.args.get('brand', '')
    sort_by = request.args.get('sort', 'relevance')
    page = request.args.get('page', 1, type=int)

    if not query and not category_id and not brand:
        return redirect(url_for('main.products'))

    # Build query
    products_query = Product.query.filter(Product.is_active == True)

    # Text search
    if query:
        products_query = products_query.filter(
            db.or_(
                Product.title.contains(query),
                Product.description.contains(query),
                Product.brand.contains(query)
            )
        )

    # Category filter
    if category_id:
        products_query = products_query.filter(Product.category_id == category_id)

    # Price range filter
    if min_price is not None:
        products_query = products_query.filter(Product.normal_price >= min_price)
    if max_price is not None:
        products_query = products_query.filter(Product.normal_price <= max_price)

    # Brand filter
    if brand:
        products_query = products_query.filter(Product.brand.contains(brand))

    # Sorting
    if sort_by == 'price_low':
        products_query = products_query.order_by(Product.normal_price.asc())
    elif sort_by == 'price_high':
        products_query = products_query.order_by(Product.normal_price.desc())
    elif sort_by == 'name':
        products_query = products_query.order_by(Product.title.asc())
    elif sort_by == 'newest':
        products_query = products_query.order_by(Product.created_at.desc())
    else:  # relevance (default)
        products_query = products_query.order_by(Product.title.asc())

    # Pagination
    products = products_query.paginate(
        page=page, per_page=current_app.config['POSTS_PER_PAGE'],
        error_out=False
    )

    # Get filter options
    categories = Category.query.filter_by(is_active=True).all()
    brands = db.session.query(Product.brand).filter(
        Product.brand.isnot(None),
        Product.brand != '',
        Product.is_active == True
    ).distinct().all()
    brands = [brand[0] for brand in brands if brand[0]]

    # Price range
    price_range = db.session.query(
        db.func.min(Product.normal_price),
        db.func.max(Product.normal_price)
    ).filter(Product.is_active == True).first()

    return render_template('main/search_results.html',
                         products=products,
                         query=query,
                         categories=categories,
                         brands=brands,
                         price_range=price_range,
                         current_filters={
                             'category': category_id,
                             'min_price': min_price,
                             'max_price': max_price,
                             'brand': brand,
                             'sort': sort_by
                         })

@bp.route('/advanced_search')
def advanced_search():
    """Advanced search form"""
    categories = Category.query.filter_by(is_active=True).all()
    brands = db.session.query(Product.brand).filter(
        Product.brand.isnot(None),
        Product.brand != '',
        Product.is_active == True
    ).distinct().all()
    brands = [brand[0] for brand in brands if brand[0]]

    # Price range
    price_range = db.session.query(
        db.func.min(Product.normal_price),
        db.func.max(Product.normal_price)
    ).filter(Product.is_active == True).first()

    return render_template('main/advanced_search.html',
                         categories=categories,
                         brands=brands,
                         price_range=price_range)
