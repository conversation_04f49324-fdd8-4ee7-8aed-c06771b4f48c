from flask import render_template, redirect, url_for, flash, request, session, jsonify, current_app
from flask_login import login_required, current_user
from functools import wraps
from app.client import bp
from app.models import Product, Category, Order, OrderItem, Branch, ProductStock, PromoCode
from app import db

def client_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated or not current_user.is_client():
            flash('Access denied. Client access required.', 'error')
            return redirect(url_for('main.index'))
        return f(*args, **kwargs)
    return decorated_function

@bp.route('/dashboard')
@login_required
@client_required
def dashboard():
    """Client dashboard"""
    # Get user's recent orders
    recent_orders = Order.query.filter_by(customer_id=current_user.id)\
                              .order_by(Order.created_at.desc()).limit(5).all()
    
    # Get cart items count
    cart_items = session.get('cart', {})
    cart_count = sum(item['quantity'] for item in cart_items.values())
    
    return render_template('client/dashboard.html', 
                         recent_orders=recent_orders,
                         cart_count=cart_count)

@bp.route('/orders')
@login_required
@client_required
def orders():
    """Client's order history"""
    page = request.args.get('page', 1, type=int)
    orders = Order.query.filter_by(customer_id=current_user.id)\
                       .order_by(Order.created_at.desc())\
                       .paginate(page=page, per_page=10, error_out=False)
    
    return render_template('client/orders.html', orders=orders)

@bp.route('/order/<int:id>')
@login_required
@client_required
def order_detail(id):
    """Order detail page"""
    order = Order.query.filter_by(id=id, customer_id=current_user.id).first_or_404()
    return render_template('client/order_detail.html', order=order)

@bp.route('/cart')
@login_required
@client_required
def cart():
    """Shopping cart page"""
    cart_items = session.get('cart', {})
    cart_products = []
    total = 0
    
    for product_id, item in cart_items.items():
        product = Product.query.get(int(product_id))
        if product and product.is_active:
            price = product.get_price_for_user(current_user)
            subtotal = price * item['quantity']
            cart_products.append({
                'product': product,
                'quantity': item['quantity'],
                'price': price,
                'subtotal': subtotal
            })
            total += subtotal
    
    branches = Branch.query.filter_by(is_active=True).all()
    
    return render_template('client/cart.html', 
                         cart_products=cart_products, 
                         total=total,
                         branches=branches)

@bp.route('/add_to_cart/<int:product_id>', methods=['POST'])
@login_required
@client_required
def add_to_cart(product_id):
    """Add product to cart"""
    product = Product.query.get_or_404(product_id)
    if not product.is_active:
        return jsonify({'success': False, 'message': 'Product not available'})
    
    quantity = request.json.get('quantity', 1)
    branch_id = request.json.get('branch_id')
    
    if not branch_id:
        return jsonify({'success': False, 'message': 'Please select a branch'})
    
    # Check stock availability
    stock = ProductStock.query.filter_by(product_id=product_id, branch_id=branch_id).first()
    if not stock or stock.quantity < quantity:
        return jsonify({'success': False, 'message': 'Insufficient stock'})
    
    # Add to session cart
    cart = session.get('cart', {})
    product_key = str(product_id)
    
    if product_key in cart:
        cart[product_key]['quantity'] += quantity
    else:
        cart[product_key] = {
            'quantity': quantity,
            'branch_id': branch_id
        }
    
    session['cart'] = cart
    session.permanent = True
    
    return jsonify({'success': True, 'message': 'Product added to cart'})

@bp.route('/remove_from_cart/<int:product_id>', methods=['POST'])
@login_required
@client_required
def remove_from_cart(product_id):
    """Remove product from cart"""
    cart = session.get('cart', {})
    product_key = str(product_id)
    
    if product_key in cart:
        del cart[product_key]
        session['cart'] = cart
        return jsonify({'success': True, 'message': 'Product removed from cart'})
    
    return jsonify({'success': False, 'message': 'Product not in cart'})

@bp.route('/checkout', methods=['GET', 'POST'])
@login_required
@client_required
def checkout():
    """Checkout process"""
    cart_items = session.get('cart', {})
    if not cart_items:
        flash('Your cart is empty.', 'warning')
        return redirect(url_for('client.cart'))

    # Calculate cart totals
    cart_products = []
    subtotal = 0

    for product_id, item in cart_items.items():
        product = Product.query.get(int(product_id))
        if product and product.is_active:
            price = product.get_price_for_user(current_user)
            item_total = price * item['quantity']
            cart_products.append({
                'product': product,
                'quantity': item['quantity'],
                'price': price,
                'subtotal': item_total,
                'branch_id': item['branch_id']
            })
            subtotal += item_total

    if request.method == 'POST':
        # Process checkout
        delivery_address = request.form.get('delivery_address')
        delivery_phone = request.form.get('delivery_phone')
        delivery_notes = request.form.get('delivery_notes')
        payment_method = request.form.get('payment_method')
        promo_code = request.form.get('promo_code', '').strip()
        branch_id = request.form.get('branch_id')

        # Validate required fields
        if not all([delivery_address, delivery_phone, payment_method, branch_id]):
            flash('Please fill in all required fields.', 'error')
            return redirect(url_for('client.checkout'))

        # Validate branch
        branch = Branch.query.get(branch_id)
        if not branch or not branch.is_active:
            flash('Invalid delivery branch selected.', 'error')
            return redirect(url_for('client.checkout'))

        # Apply promo code if provided
        discount_amount = 0
        promo = None
        if promo_code:
            promo = PromoCode.query.filter_by(code=promo_code.upper()).first()
            if promo:
                is_valid, message = promo.is_valid(subtotal)
                if is_valid:
                    discount_amount = promo.calculate_discount(subtotal)
                else:
                    flash(f'Promo code error: {message}', 'error')
                    return redirect(url_for('client.checkout'))
            else:
                flash('Invalid promo code.', 'error')
                return redirect(url_for('client.checkout'))

        total_amount = subtotal - discount_amount

        # Create order
        from app.utils import generate_order_number
        order = Order(
            order_number=generate_order_number(),
            customer_id=current_user.id,
            branch_id=branch_id,
            subtotal=subtotal,
            discount_amount=discount_amount,
            total_amount=total_amount,
            delivery_address=delivery_address,
            delivery_phone=delivery_phone,
            delivery_notes=delivery_notes,
            payment_method=payment_method,
            promo_code=promo_code if promo else None,
            status='pending'
        )

        db.session.add(order)
        db.session.flush()  # Get order ID

        # Create order items and update stock
        for item in cart_products:
            order_item = OrderItem(
                order_id=order.id,
                product_id=item['product'].id,
                quantity=item['quantity'],
                unit_price=item['price'],
                total_price=item['subtotal']
            )
            db.session.add(order_item)

            # Update stock
            stock = ProductStock.query.filter_by(
                product_id=item['product'].id,
                branch_id=item['branch_id']
            ).first()
            if stock:
                stock.quantity -= item['quantity']

        # Update promo code usage
        if promo:
            promo.used_count += 1

        # Create invoice
        from app.models import Invoice
        from app.utils import generate_invoice_number, generate_invoice_pdf

        invoice = Invoice(
            invoice_number=generate_invoice_number(),
            order_id=order.id
        )
        db.session.add(invoice)
        db.session.flush()

        # Generate PDF invoice
        try:
            pdf_filename = generate_invoice_pdf(order)
            invoice.file_path = pdf_filename
        except Exception as e:
            print(f"Error generating invoice PDF: {e}")

        db.session.commit()

        # Clear cart
        session.pop('cart', None)

        # Send confirmation email (implement later)
        # send_order_confirmation_email(order)

        flash('Order placed successfully!', 'success')
        return redirect(url_for('client.order_detail', id=order.id))

    # GET request - show checkout form
    branches = Branch.query.filter_by(is_active=True).all()

    return render_template('client/checkout.html',
                         cart_products=cart_products,
                         subtotal=subtotal,
                         branches=branches)

@bp.route('/update_cart/<int:product_id>', methods=['POST'])
@login_required
@client_required
def update_cart(product_id):
    """Update cart item quantity"""
    cart = session.get('cart', {})
    product_key = str(product_id)

    if product_key not in cart:
        return jsonify({'success': False, 'message': 'Product not in cart'})

    quantity = request.json.get('quantity', 1)
    if quantity <= 0:
        # Remove item if quantity is 0 or negative
        del cart[product_key]
        session['cart'] = cart
        return jsonify({'success': True, 'message': 'Product removed from cart'})

    # Check stock availability
    product = Product.query.get(product_id)
    if not product:
        return jsonify({'success': False, 'message': 'Product not found'})

    stock = ProductStock.query.filter_by(
        product_id=product_id,
        branch_id=cart[product_key]['branch_id']
    ).first()

    if not stock or stock.quantity < quantity:
        return jsonify({'success': False, 'message': 'Insufficient stock'})

    # Update quantity
    cart[product_key]['quantity'] = quantity
    session['cart'] = cart

    return jsonify({'success': True, 'message': 'Cart updated successfully'})

@bp.route('/download_invoice/<int:order_id>')
@login_required
@client_required
def download_invoice(order_id):
    """Download invoice PDF"""
    from flask import send_file
    import os

    order = Order.query.filter_by(id=order_id, customer_id=current_user.id).first_or_404()

    if not order.invoice or not order.invoice.file_path:
        flash('Invoice not available for this order.', 'error')
        return redirect(url_for('client.order_detail', id=order_id))

    invoice_path = os.path.join(current_app.root_path, 'static', 'uploads', 'invoices', order.invoice.file_path)

    if not os.path.exists(invoice_path):
        flash('Invoice file not found.', 'error')
        return redirect(url_for('client.order_detail', id=order_id))

    return send_file(invoice_path, as_attachment=True, download_name=f"invoice_{order.order_number}.pdf")

@bp.route('/cancel_order/<int:order_id>', methods=['POST'])
@login_required
@client_required
def cancel_order(order_id):
    """Cancel an order"""
    order = Order.query.filter_by(id=order_id, customer_id=current_user.id).first_or_404()

    if order.status != 'pending':
        return jsonify({'success': False, 'message': 'Order cannot be cancelled at this stage'})

    # Restore stock
    for item in order.items:
        stock = ProductStock.query.filter_by(
            product_id=item.product_id,
            branch_id=order.branch_id
        ).first()
        if stock:
            stock.quantity += item.quantity

    # Update order status
    order.status = 'cancelled'
    db.session.commit()

    return jsonify({'success': True, 'message': 'Order cancelled successfully'})
