from flask import render_template, redirect, url_for, flash, request
from flask_login import login_user, logout_user, current_user, login_required
from app.auth import bp
from app.auth.forms import LoginForm, RegistrationForm
from app.models import User, PasswordResetToken, EmailVerificationToken
from app import db
from datetime import datetime

@bp.route('/login', methods=['GET', 'POST'])
def login():
    if current_user.is_authenticated:
        return redirect(url_for('main.dashboard'))
    
    form = LoginForm()
    if form.validate_on_submit():
        user = User.query.filter_by(username=form.username.data).first()
        if user and user.check_password(form.password.data) and user.is_active:
            user.last_login = datetime.utcnow()
            db.session.commit()
            login_user(user, remember=form.remember_me.data)
            
            next_page = request.args.get('next')
            if not next_page or not next_page.startswith('/'):
                next_page = url_for('main.dashboard')
            return redirect(next_page)
        
        flash('Invalid username or password', 'error')
    
    return render_template('auth/login.html', form=form)

@bp.route('/register', methods=['GET', 'POST'])
def register():
    if current_user.is_authenticated:
        return redirect(url_for('main.dashboard'))
    
    form = RegistrationForm()
    if form.validate_on_submit():
        user = User(
            username=form.username.data,
            email=form.email.data,
            first_name=form.first_name.data,
            last_name=form.last_name.data,
            phone=form.phone.data,
            address=form.address.data,
            role='client'  # Default role for new registrations
        )
        user.set_password(form.password.data)
        
        db.session.add(user)
        db.session.commit()

        # Send welcome email
        from app.utils import send_new_user_welcome_email
        send_new_user_welcome_email(user)

        flash('Registration successful! A welcome email has been sent. You can now log in.', 'success')
        return redirect(url_for('auth.login'))
    
    return render_template('auth/register.html', form=form)

@bp.route('/logout')
def logout():
    logout_user()
    flash('You have been logged out.', 'info')
    return redirect(url_for('main.index'))

@bp.route('/register/reseller', methods=['GET', 'POST'])
def register_reseller():
    """Special registration for resellers"""
    if current_user.is_authenticated:
        return redirect(url_for('main.dashboard'))
    
    form = RegistrationForm()
    if form.validate_on_submit():
        user = User(
            username=form.username.data,
            email=form.email.data,
            first_name=form.first_name.data,
            last_name=form.last_name.data,
            phone=form.phone.data,
            address=form.address.data,
            role='reseller'
        )
        user.set_password(form.password.data)
        
        db.session.add(user)
        db.session.commit()
        
        flash('Reseller registration successful! You can now log in with reseller pricing.', 'success')
        return redirect(url_for('auth.login'))
    
    return render_template('auth/register_reseller.html', form=form)

@bp.route('/forgot_password', methods=['GET', 'POST'])
def forgot_password():
    """Password reset request"""
    if current_user.is_authenticated:
        return redirect(url_for('main.dashboard'))

    if request.method == 'POST':
        email = request.form.get('email')
        user = User.query.filter_by(email=email).first()

        if user:
            # Generate reset token
            token = PasswordResetToken.generate_token(user)

            # Send reset email
            from app.utils import send_password_reset_email
            send_password_reset_email(user, token)

        # Always show success message for security
        flash('If an account with that email exists, a password reset link has been sent.', 'info')
        return redirect(url_for('auth.login'))

    return render_template('auth/forgot_password.html')

@bp.route('/reset_password/<token>', methods=['GET', 'POST'])
def reset_password(token):
    """Reset password with token"""
    if current_user.is_authenticated:
        return redirect(url_for('main.dashboard'))

    # Find and validate token
    reset_token = PasswordResetToken.query.filter_by(token=token).first()

    if not reset_token or not reset_token.is_valid():
        flash('Invalid or expired password reset link.', 'error')
        return redirect(url_for('auth.forgot_password'))

    if request.method == 'POST':
        password = request.form.get('password')
        confirm_password = request.form.get('confirm_password')

        if not password or len(password) < 6:
            flash('Password must be at least 6 characters long.', 'error')
            return render_template('auth/reset_password.html', token=token)

        if password != confirm_password:
            flash('Passwords do not match.', 'error')
            return render_template('auth/reset_password.html', token=token)

        # Update password
        user = reset_token.user
        user.set_password(password)
        reset_token.mark_as_used()

        flash('Your password has been reset successfully. You can now log in.', 'success')
        return redirect(url_for('auth.login'))

    return render_template('auth/reset_password.html', token=token)

@bp.route('/verify_email/<token>')
def verify_email(token):
    """Verify email address with token"""
    # Find and validate token
    verification_token = EmailVerificationToken.query.filter_by(token=token).first()

    if not verification_token or not verification_token.is_valid():
        flash('Invalid or expired email verification link.', 'error')
        return redirect(url_for('main.index'))

    # Mark email as verified
    user = verification_token.user
    user.email_verified = True
    verification_token.mark_as_used()

    flash('Your email has been verified successfully!', 'success')

    if current_user.is_authenticated:
        return redirect(url_for('main.dashboard'))
    else:
        return redirect(url_for('auth.login'))

@bp.route('/resend_verification')
@login_required
def resend_verification():
    """Resend email verification"""
    if current_user.email_verified:
        flash('Your email is already verified.', 'info')
        return redirect(url_for('main.dashboard'))

    # Generate new verification token
    token = EmailVerificationToken.generate_token(current_user)

    # Send verification email
    from app.utils import send_email_verification
    send_email_verification(current_user, token)

    flash('A new verification email has been sent.', 'info')
    return redirect(url_for('main.dashboard'))
