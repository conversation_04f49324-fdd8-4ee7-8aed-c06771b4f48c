from datetime import datetime
from flask_sqlalchemy import SQLAlchemy
from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
from app import db, login_manager

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(255), nullable=False)
    first_name = db.Column(db.String(50), nullable=False)
    last_name = db.Column(db.String(50), nullable=False)
    phone = db.Column(db.String(20))
    address = db.Column(db.Text)
    role = db.Column(db.String(20), nullable=False, default='client')  # admin, manager, delivery, client, reseller
    branch_id = db.Column(db.Integer, db.ForeignKey('branch.id'))
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_login = db.Column(db.DateTime)
    
    # Relationships
    branch = db.relationship('Branch', backref='users')
    orders = db.relationship('Order', foreign_keys='Order.customer_id', backref='customer', lazy='dynamic')
    assigned_deliveries = db.relationship('Order', foreign_keys='Order.delivery_person_id', backref='delivery_person', lazy='dynamic')
    
    def set_password(self, password):
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        return check_password_hash(self.password_hash, password)
    
    def is_admin(self):
        return self.role == 'admin'
    
    def is_manager(self):
        return self.role == 'manager'
    
    def is_delivery(self):
        return self.role == 'delivery'
    
    def is_client(self):
        return self.role in ['client', 'reseller']
    
    def is_reseller(self):
        return self.role == 'reseller'
    
    def get_full_name(self):
        return f"{self.first_name} {self.last_name}"
    
    def __repr__(self):
        return f'<User {self.username}>'

class Branch(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    address = db.Column(db.Text, nullable=False)
    phone = db.Column(db.String(20))
    email = db.Column(db.String(120))
    latitude = db.Column(db.Float)
    longitude = db.Column(db.Float)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relationships
    products = db.relationship('ProductStock', backref='branch', lazy='dynamic')
    orders = db.relationship('Order', backref='branch', lazy='dynamic')
    
    def __repr__(self):
        return f'<Branch {self.name}>'

class Category(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, unique=True)
    description = db.Column(db.Text)
    image_url = db.Column(db.String(255))
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # Relationships
    products = db.relationship('Product', backref='category', lazy='dynamic')

    def __repr__(self):
        return f'<Category {self.name}>'

class Product(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text)
    brand = db.Column(db.String(100))
    sku = db.Column(db.String(50), unique=True)
    normal_price = db.Column(db.Numeric(10, 2), nullable=False)
    reseller_price = db.Column(db.Numeric(10, 2), nullable=False)
    main_image = db.Column(db.String(255))
    gallery_images = db.Column(db.Text)  # JSON string of image URLs
    category_id = db.Column(db.Integer, db.ForeignKey('category.id'), nullable=False)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    stock_items = db.relationship('ProductStock', backref='product', lazy='dynamic')
    order_items = db.relationship('OrderItem', backref='product', lazy='dynamic')

    def get_price_for_user(self, user):
        if user and user.is_reseller():
            return self.reseller_price
        return self.normal_price

    def get_total_stock(self):
        return sum(stock.quantity for stock in self.stock_items if stock.branch.is_active)

    def get_stock_for_branch(self, branch_id):
        stock = ProductStock.query.filter_by(product_id=self.id, branch_id=branch_id).first()
        return stock.quantity if stock else 0

    def __repr__(self):
        return f'<Product {self.title}>'

class ProductStock(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    product_id = db.Column(db.Integer, db.ForeignKey('product.id'), nullable=False)
    branch_id = db.Column(db.Integer, db.ForeignKey('branch.id'), nullable=False)
    quantity = db.Column(db.Integer, nullable=False, default=0)
    min_stock_level = db.Column(db.Integer, default=10)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    __table_args__ = (db.UniqueConstraint('product_id', 'branch_id'),)

    def is_low_stock(self):
        return self.quantity <= self.min_stock_level

    def __repr__(self):
        return f'<ProductStock {self.product.title} - {self.branch.name}: {self.quantity}>'

class Order(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    order_number = db.Column(db.String(20), unique=True, nullable=False)
    customer_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    branch_id = db.Column(db.Integer, db.ForeignKey('branch.id'), nullable=False)
    delivery_person_id = db.Column(db.Integer, db.ForeignKey('user.id'))

    # Order details
    subtotal = db.Column(db.Numeric(10, 2), nullable=False)
    discount_amount = db.Column(db.Numeric(10, 2), default=0)
    total_amount = db.Column(db.Numeric(10, 2), nullable=False)

    # Delivery information
    delivery_address = db.Column(db.Text, nullable=False)
    delivery_phone = db.Column(db.String(20))
    delivery_notes = db.Column(db.Text)
    delivery_latitude = db.Column(db.Float)
    delivery_longitude = db.Column(db.Float)

    # Status and payment
    status = db.Column(db.String(20), default='pending')  # pending, confirmed, picked, out_for_delivery, delivered, cancelled
    payment_method = db.Column(db.String(20), nullable=False)  # cash, check, bank_transfer
    payment_status = db.Column(db.String(20), default='pending')  # pending, partial, paid
    paid_amount = db.Column(db.Numeric(10, 2), default=0)

    # Promo code
    promo_code = db.Column(db.String(50))

    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    confirmed_at = db.Column(db.DateTime)
    picked_at = db.Column(db.DateTime)
    out_for_delivery_at = db.Column(db.DateTime)
    delivered_at = db.Column(db.DateTime)

    # Relationships
    items = db.relationship('OrderItem', backref='order', lazy='dynamic', cascade='all, delete-orphan')
    payments = db.relationship('Payment', backref='order', lazy='dynamic')
    invoice = db.relationship('Invoice', backref='order', uselist=False)

    def generate_order_number(self):
        from datetime import datetime
        timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
        return f'YO{timestamp}'

    def get_remaining_amount(self):
        return self.total_amount - self.paid_amount

    def is_fully_paid(self):
        return self.paid_amount >= self.total_amount

    def can_be_delivered(self):
        return self.status in ['confirmed', 'picked'] and self.delivery_person_id is not None

    def __repr__(self):
        return f'<Order {self.order_number}>'

class OrderItem(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    order_id = db.Column(db.Integer, db.ForeignKey('order.id'), nullable=False)
    product_id = db.Column(db.Integer, db.ForeignKey('product.id'), nullable=False)
    quantity = db.Column(db.Integer, nullable=False)
    unit_price = db.Column(db.Numeric(10, 2), nullable=False)
    total_price = db.Column(db.Numeric(10, 2), nullable=False)

    def __repr__(self):
        return f'<OrderItem {self.product.title} x{self.quantity}>'

class Payment(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    order_id = db.Column(db.Integer, db.ForeignKey('order.id'), nullable=False)
    amount = db.Column(db.Numeric(10, 2), nullable=False)
    payment_method = db.Column(db.String(20), nullable=False)
    reference_number = db.Column(db.String(100))
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    created_by = db.Column(db.Integer, db.ForeignKey('user.id'))

    creator = db.relationship('User', foreign_keys=[created_by])

    def __repr__(self):
        return f'<Payment {self.amount} Dh for Order {self.order.order_number}>'

class Invoice(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    invoice_number = db.Column(db.String(20), unique=True, nullable=False)
    order_id = db.Column(db.Integer, db.ForeignKey('order.id'), nullable=False)
    file_path = db.Column(db.String(255))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def generate_invoice_number(self):
        from datetime import datetime
        timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
        return f'INV{timestamp}'

    def __repr__(self):
        return f'<Invoice {self.invoice_number}>'

class PromoCode(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    code = db.Column(db.String(50), unique=True, nullable=False)
    description = db.Column(db.String(200))
    discount_type = db.Column(db.String(20), nullable=False)  # percentage, fixed
    discount_value = db.Column(db.Numeric(10, 2), nullable=False)
    min_order_amount = db.Column(db.Numeric(10, 2), default=0)
    max_discount_amount = db.Column(db.Numeric(10, 2))
    usage_limit = db.Column(db.Integer)
    used_count = db.Column(db.Integer, default=0)
    is_active = db.Column(db.Boolean, default=True)
    valid_from = db.Column(db.DateTime, default=datetime.utcnow)
    valid_until = db.Column(db.DateTime)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def is_valid(self, order_amount=0):
        from datetime import datetime
        now = datetime.utcnow()

        if not self.is_active:
            return False, "Promo code is not active"

        if self.valid_from and now < self.valid_from:
            return False, "Promo code is not yet valid"

        if self.valid_until and now > self.valid_until:
            return False, "Promo code has expired"

        if self.usage_limit and self.used_count >= self.usage_limit:
            return False, "Promo code usage limit reached"

        if order_amount < self.min_order_amount:
            return False, f"Minimum order amount is {self.min_order_amount} Dh"

        return True, "Valid"

    def calculate_discount(self, order_amount):
        if self.discount_type == 'percentage':
            discount = order_amount * (self.discount_value / 100)
        else:  # fixed
            discount = self.discount_value

        if self.max_discount_amount:
            discount = min(discount, self.max_discount_amount)

        return min(discount, order_amount)

    def __repr__(self):
        return f'<PromoCode {self.code}>'
