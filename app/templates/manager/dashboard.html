{% extends "base.html" %}

{% block title %}Manager Dashboard - YalaOffice{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Welcome Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <h2 class="mb-0">
                        <i class="fas fa-user-tie"></i> Manager Dashboard
                    </h2>
                    <p class="mb-0">
                        Welcome, {{ current_user.get_full_name() }}
                        {% if current_user.branch %}
                        - {{ current_user.branch.name }}
                        {% endif %}
                    </p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Key Statistics -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">{{ total_orders }}</h4>
                            <p class="mb-0">Total Orders</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-shopping-cart fa-2x"></i>
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <a href="{{ url_for('manager.orders') }}" class="text-white text-decoration-none">
                        View Details <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">{{ pending_orders }}</h4>
                            <p class="mb-0">Pending Orders</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-clock fa-2x"></i>
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <a href="{{ url_for('manager.orders', status='pending') }}" class="text-white text-decoration-none">
                        View Details <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">{{ confirmed_orders }}</h4>
                            <p class="mb-0">Confirmed Orders</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <a href="{{ url_for('manager.orders', status='confirmed') }}" class="text-white text-decoration-none">
                        View Details <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">{{ low_stock_products|length }}</h4>
                            <p class="mb-0">Low Stock Items</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-exclamation-triangle fa-2x"></i>
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <a href="{{ url_for('manager.inventory') }}" class="text-white text-decoration-none">
                        View Details <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-bolt"></i> Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-2">
                            <a href="{{ url_for('manager.orders') }}" class="btn btn-outline-primary w-100">
                                <i class="fas fa-shopping-cart"></i><br>Manage Orders
                            </a>
                        </div>
                        <div class="col-md-3 mb-2">
                            <a href="{{ url_for('manager.inventory') }}" class="btn btn-outline-success w-100">
                                <i class="fas fa-boxes"></i><br>Inventory
                            </a>
                        </div>
                        <div class="col-md-3 mb-2">
                            <a href="#" class="btn btn-outline-info w-100">
                                <i class="fas fa-users"></i><br>Customers
                            </a>
                        </div>
                        <div class="col-md-3 mb-2">
                            <a href="#" class="btn btn-outline-warning w-100">
                                <i class="fas fa-chart-bar"></i><br>Reports
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <!-- Recent Orders -->
        <div class="col-lg-8 mb-4">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-clock"></i> Recent Orders</h5>
                    <a href="{{ url_for('manager.orders') }}" class="btn btn-sm btn-outline-primary">View All</a>
                </div>
                <div class="card-body">
                    {% if recent_orders %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Order #</th>
                                    <th>Customer</th>
                                    <th>Date</th>
                                    <th>Status</th>
                                    <th>Total</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for order in recent_orders %}
                                <tr>
                                    <td><strong>{{ order.order_number }}</strong></td>
                                    <td>{{ order.customer.get_full_name() }}</td>
                                    <td>{{ order.created_at.strftime('%Y-%m-%d') if order.created_at }}</td>
                                    <td>
                                        {% if order.status == 'pending' %}
                                            <span class="badge bg-warning">Pending</span>
                                        {% elif order.status == 'confirmed' %}
                                            <span class="badge bg-info">Confirmed</span>
                                        {% elif order.status == 'picked' %}
                                            <span class="badge bg-primary">Picked</span>
                                        {% elif order.status == 'out_for_delivery' %}
                                            <span class="badge bg-secondary">Out for Delivery</span>
                                        {% elif order.status == 'delivered' %}
                                            <span class="badge bg-success">Delivered</span>
                                        {% elif order.status == 'cancelled' %}
                                            <span class="badge bg-danger">Cancelled</span>
                                        {% endif %}
                                    </td>
                                    <td><strong>{{ order.total_amount }} Dh</strong></td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ url_for('manager.order_detail', id=order.id) }}" 
                                               class="btn btn-sm btn-outline-primary">View</a>
                                            {% if order.status == 'pending' %}
                                            <button class="btn btn-sm btn-outline-success" 
                                                    onclick="confirmOrder({{ order.id }})">Confirm</button>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <p class="text-muted text-center">No recent orders found.</p>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <!-- Low Stock Alert -->
        <div class="col-lg-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-exclamation-triangle text-warning"></i> Low Stock Alert</h5>
                </div>
                <div class="card-body">
                    {% if low_stock_products %}
                    <div class="list-group list-group-flush">
                        {% for stock in low_stock_products[:5] %}
                        <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                            <div>
                                <h6 class="mb-1">{{ stock.product.title }}</h6>
                                <small class="text-muted">{{ stock.branch.name if stock.branch else 'All Branches' }}</small>
                            </div>
                            <span class="badge bg-warning rounded-pill">{{ stock.quantity }}</span>
                        </div>
                        {% endfor %}
                    </div>
                    {% if low_stock_products|length > 5 %}
                    <div class="text-center mt-3">
                        <small class="text-muted">And {{ low_stock_products|length - 5 }} more items...</small>
                    </div>
                    {% endif %}
                    <div class="text-center mt-3">
                        <a href="{{ url_for('manager.inventory') }}" class="btn btn-sm btn-outline-warning">
                            View All Stock
                        </a>
                    </div>
                    {% else %}
                    <p class="text-muted text-center">All products are well stocked!</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    
    <!-- Today's Summary -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-calendar-day"></i> Today's Summary</h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-3">
                            <h4 class="text-primary">0</h4>
                            <p class="text-muted">New Orders</p>
                        </div>
                        <div class="col-md-3">
                            <h4 class="text-success">0</h4>
                            <p class="text-muted">Orders Delivered</p>
                        </div>
                        <div class="col-md-3">
                            <h4 class="text-info">0 Dh</h4>
                            <p class="text-muted">Revenue</p>
                        </div>
                        <div class="col-md-3">
                            <h4 class="text-warning">0</h4>
                            <p class="text-muted">Pending Actions</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function confirmOrder(orderId) {
    if (confirm('Are you sure you want to confirm this order?')) {
        fetch(`/manager/order/${orderId}/confirm`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Order confirmed successfully');
                location.reload();
            } else {
                alert(data.message || 'Error confirming order');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error confirming order');
        });
    }
}

// Auto-refresh dashboard every 5 minutes
setInterval(function() {
    location.reload();
}, 300000); // 5 minutes
</script>
{% endblock %}
