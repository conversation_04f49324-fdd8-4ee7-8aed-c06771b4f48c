{% extends "base.html" %}

{% block title %}Inventory Management - Manager - YalaOffice{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h2><i class="fas fa-boxes"></i> Inventory Management</h2>
            <p class="text-muted">Monitor and manage stock levels</p>
        </div>
        <div class="col-md-4 text-md-end">
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown">
                    <i class="fas fa-filter"></i> Filter
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="{{ url_for('manager.inventory') }}">All Stock</a></li>
                    <li><a class="dropdown-item" href="{{ url_for('manager.inventory', stock='low') }}">Low Stock</a></li>
                    <li><a class="dropdown-item" href="{{ url_for('manager.inventory', stock='out') }}">Out of Stock</a></li>
                </ul>
            </div>
        </div>
    </div>
    
    <!-- Search and Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" class="row align-items-end">
                <div class="col-md-4 mb-2">
                    <label for="search" class="form-label">Search Products</label>
                    <input type="text" class="form-control" id="search" name="search" 
                           value="{{ search }}" placeholder="Product name, SKU...">
                </div>
                <div class="col-md-3 mb-2">
                    <label for="category" class="form-label">Category</label>
                    <select class="form-select" id="category" name="category">
                        <option value="">All Categories</option>
                        {% for category in categories %}
                        <option value="{{ category.id }}" {{ 'selected' if category_filter == category.id|string }}>
                            {{ category.name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3 mb-2">
                    <label for="stock" class="form-label">Stock Level</label>
                    <select class="form-select" id="stock" name="stock">
                        <option value="">All Levels</option>
                        <option value="low" {{ 'selected' if stock_filter == 'low' }}>Low Stock</option>
                        <option value="out" {{ 'selected' if stock_filter == 'out' }}>Out of Stock</option>
                    </select>
                </div>
                <div class="col-md-2 mb-2">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search"></i> Search
                    </button>
                </div>
            </form>
        </div>
    </div>
    
    <!-- Stock Summary Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <h4>{{ stock_items.total if stock_items else 0 }}</h4>
                    <p class="mb-0">Total Products</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h4>{{ stock_items.items|selectattr('quantity', 'gt', 10)|list|length if stock_items else 0 }}</h4>
                    <p class="mb-0">Well Stocked</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-warning text-white">
                <div class="card-body text-center">
                    <h4>{{ stock_items.items|selectattr('is_low_stock')|list|length if stock_items else 0 }}</h4>
                    <p class="mb-0">Low Stock</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-danger text-white">
                <div class="card-body text-center">
                    <h4>{{ stock_items.items|selectattr('quantity', 'equalto', 0)|list|length if stock_items else 0 }}</h4>
                    <p class="mb-0">Out of Stock</p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Inventory Table -->
    <div class="card">
        <div class="card-body">
            {% if stock_items and stock_items.items %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Product</th>
                            <th>Category</th>
                            <th>Branch</th>
                            <th>Current Stock</th>
                            <th>Min Level</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for stock in stock_items.items %}
                        <tr class="{{ 'table-danger' if stock.quantity == 0 else 'table-warning' if stock.is_low_stock() else '' }}">
                            <td>
                                <div>
                                    <strong>{{ stock.product.title }}</strong><br>
                                    <small class="text-muted">
                                        SKU: {{ stock.product.sku or 'N/A' }}<br>
                                        Brand: {{ stock.product.brand or 'No Brand' }}
                                    </small>
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-secondary">{{ stock.product.category.name }}</span>
                            </td>
                            <td>
                                <strong>{{ stock.branch.name }}</strong><br>
                                <small class="text-muted">{{ stock.branch.address }}</small>
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <span class="me-2" style="font-size: 1.2em; font-weight: bold;">
                                        {{ stock.quantity }}
                                    </span>
                                    <div class="progress" style="width: 100px; height: 8px;">
                                        {% set percentage = (stock.quantity / (stock.min_stock_level * 2) * 100) if stock.min_stock_level > 0 else 100 %}
                                        <div class="progress-bar {{ 'bg-danger' if stock.quantity == 0 else 'bg-warning' if stock.is_low_stock() else 'bg-success' }}" 
                                             style="width: {{ [percentage, 100]|min }}%"></div>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-info">{{ stock.min_stock_level }}</span>
                            </td>
                            <td>
                                {% if stock.quantity == 0 %}
                                    <span class="badge bg-danger">Out of Stock</span>
                                {% elif stock.is_low_stock() %}
                                    <span class="badge bg-warning">Low Stock</span>
                                {% else %}
                                    <span class="badge bg-success">In Stock</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <button class="btn btn-outline-primary btn-sm" 
                                            onclick="adjustStock({{ stock.id }}, '{{ stock.product.title }}', {{ stock.quantity }})">
                                        <i class="fas fa-edit"></i> Adjust
                                    </button>
                                    <button class="btn btn-outline-info btn-sm" 
                                            onclick="viewStockHistory({{ stock.id }})">
                                        <i class="fas fa-history"></i> History
                                    </button>
                                    {% if stock.is_low_stock() %}
                                    <button class="btn btn-outline-warning btn-sm" 
                                            onclick="reorderAlert({{ stock.id }})">
                                        <i class="fas fa-exclamation-triangle"></i> Reorder
                                    </button>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            {% if stock_items.pages > 1 %}
            <nav aria-label="Stock pagination" class="mt-4">
                <ul class="pagination justify-content-center">
                    {% if stock_items.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('manager.inventory', page=stock_items.prev_num, search=search, category=category_filter, stock=stock_filter) }}">
                            Previous
                        </a>
                    </li>
                    {% endif %}
                    
                    {% for page_num in stock_items.iter_pages() %}
                        {% if page_num %}
                            {% if page_num != stock_items.page %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('manager.inventory', page=page_num, search=search, category=category_filter, stock=stock_filter) }}">
                                    {{ page_num }}
                                </a>
                            </li>
                            {% else %}
                            <li class="page-item active">
                                <span class="page-link">{{ page_num }}</span>
                            </li>
                            {% endif %}
                        {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">...</span>
                        </li>
                        {% endif %}
                    {% endfor %}
                    
                    {% if stock_items.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('manager.inventory', page=stock_items.next_num, search=search, category=category_filter, stock=stock_filter) }}">
                            Next
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}
            
            {% else %}
            <div class="text-center py-5">
                <i class="fas fa-boxes fa-5x text-muted mb-4"></i>
                <h4>No inventory found</h4>
                {% if search or category_filter or stock_filter %}
                <p class="text-muted">No products match your current filters.</p>
                <a href="{{ url_for('manager.inventory') }}" class="btn btn-primary">Clear Filters</a>
                {% else %}
                <p class="text-muted">No inventory data available for your branch.</p>
                {% endif %}
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Stock Adjustment Modal -->
<div class="modal fade" id="stockAdjustmentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Adjust Stock Level</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="stockAdjustmentForm">
                    <div class="mb-3">
                        <label class="form-label">Product</label>
                        <p id="productName" class="form-control-plaintext"></p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Current Stock</label>
                        <p id="currentStock" class="form-control-plaintext"></p>
                    </div>
                    <div class="mb-3">
                        <label for="adjustmentType" class="form-label">Adjustment Type</label>
                        <select class="form-select" id="adjustmentType" name="adjustment_type" required>
                            <option value="">Choose type...</option>
                            <option value="add">Add Stock</option>
                            <option value="remove">Remove Stock</option>
                            <option value="set">Set Exact Amount</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="adjustmentQuantity" class="form-label">Quantity</label>
                        <input type="number" class="form-control" id="adjustmentQuantity" 
                               name="quantity" min="0" required>
                    </div>
                    <div class="mb-3">
                        <label for="adjustmentReason" class="form-label">Reason</label>
                        <textarea class="form-control" id="adjustmentReason" name="reason" 
                                  rows="3" placeholder="Reason for stock adjustment..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="submitStockAdjustment()">
                    Apply Adjustment
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentStockId = null;

function adjustStock(stockId, productName, currentQuantity) {
    currentStockId = stockId;
    document.getElementById('productName').textContent = productName;
    document.getElementById('currentStock').textContent = currentQuantity + ' units';
    
    // Reset form
    document.getElementById('stockAdjustmentForm').reset();
    
    new bootstrap.Modal(document.getElementById('stockAdjustmentModal')).show();
}

function submitStockAdjustment() {
    const form = document.getElementById('stockAdjustmentForm');
    const formData = new FormData(form);
    
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }
    
    fetch(`/manager/stock/${currentStockId}/adjust`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            adjustment_type: formData.get('adjustment_type'),
            quantity: parseInt(formData.get('quantity')),
            reason: formData.get('reason')
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Stock adjusted successfully');
            bootstrap.Modal.getInstance(document.getElementById('stockAdjustmentModal')).hide();
            location.reload();
        } else {
            alert(data.message || 'Error adjusting stock');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error adjusting stock');
    });
}

function viewStockHistory(stockId) {
    // This would open a modal or navigate to a stock history page
    alert('Stock history feature coming soon!');
}

function reorderAlert(stockId) {
    if (confirm('Send reorder alert for this product?')) {
        fetch(`/manager/stock/${stockId}/reorder-alert`, {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Reorder alert sent successfully');
            } else {
                alert('Error sending reorder alert');
            }
        });
    }
}

// Auto-refresh every 5 minutes
setInterval(function() {
    location.reload();
}, 300000);
</script>
{% endblock %}
