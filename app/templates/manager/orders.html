{% extends "base.html" %}

{% block title %}Order Management - Manager - YalaOffice{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h2><i class="fas fa-shopping-cart"></i> Order Management</h2>
            <p class="text-muted">Manage and process customer orders</p>
        </div>
        <div class="col-md-4 text-md-end">
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown">
                    <i class="fas fa-filter"></i> Filter by Status
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="{{ url_for('manager.orders') }}">All Orders</a></li>
                    <li><a class="dropdown-item" href="{{ url_for('manager.orders', status='pending') }}">Pending</a></li>
                    <li><a class="dropdown-item" href="{{ url_for('manager.orders', status='confirmed') }}">Confirmed</a></li>
                    <li><a class="dropdown-item" href="{{ url_for('manager.orders', status='picked') }}">Picked</a></li>
                    <li><a class="dropdown-item" href="{{ url_for('manager.orders', status='out_for_delivery') }}">Out for Delivery</a></li>
                    <li><a class="dropdown-item" href="{{ url_for('manager.orders', status='delivered') }}">Delivered</a></li>
                    <li><a class="dropdown-item" href="{{ url_for('manager.orders', status='cancelled') }}">Cancelled</a></li>
                </ul>
            </div>
        </div>
    </div>
    
    <!-- Status Summary Cards -->
    <div class="row mb-4">
        <div class="col-lg-2 col-md-4 col-sm-6 mb-2">
            <div class="card bg-warning text-white">
                <div class="card-body text-center">
                    <h5>{{ orders.items|selectattr('status', 'equalto', 'pending')|list|length }}</h5>
                    <small>Pending</small>
                </div>
            </div>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6 mb-2">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <h5>{{ orders.items|selectattr('status', 'equalto', 'confirmed')|list|length }}</h5>
                    <small>Confirmed</small>
                </div>
            </div>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6 mb-2">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <h5>{{ orders.items|selectattr('status', 'equalto', 'picked')|list|length }}</h5>
                    <small>Picked</small>
                </div>
            </div>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6 mb-2">
            <div class="card bg-secondary text-white">
                <div class="card-body text-center">
                    <h5>{{ orders.items|selectattr('status', 'equalto', 'out_for_delivery')|list|length }}</h5>
                    <small>Out for Delivery</small>
                </div>
            </div>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6 mb-2">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h5>{{ orders.items|selectattr('status', 'equalto', 'delivered')|list|length }}</h5>
                    <small>Delivered</small>
                </div>
            </div>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6 mb-2">
            <div class="card bg-danger text-white">
                <div class="card-body text-center">
                    <h5>{{ orders.items|selectattr('status', 'equalto', 'cancelled')|list|length }}</h5>
                    <small>Cancelled</small>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Orders Table -->
    <div class="card">
        <div class="card-body">
            {% if orders.items %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Order #</th>
                            <th>Customer</th>
                            <th>Date</th>
                            <th>Items</th>
                            <th>Total</th>
                            <th>Status</th>
                            <th>Delivery</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for order in orders.items %}
                        <tr>
                            <td>
                                <strong>{{ order.order_number }}</strong><br>
                                <small class="text-muted">{{ order.payment_method.replace('_', ' ').title() }}</small>
                            </td>
                            <td>
                                <div>
                                    <strong>{{ order.customer.get_full_name() }}</strong><br>
                                    <small class="text-muted">{{ order.customer.email }}</small>
                                </div>
                            </td>
                            <td>
                                {{ order.created_at.strftime('%Y-%m-%d') if order.created_at }}<br>
                                <small class="text-muted">{{ order.created_at.strftime('%I:%M %p') if order.created_at }}</small>
                            </td>
                            <td>
                                <span class="badge bg-secondary">{{ order.items|length }} items</span><br>
                                <small class="text-muted">{{ order.get_total_quantity() }} units</small>
                            </td>
                            <td>
                                <strong>{{ order.total_amount }} Dh</strong>
                                {% if order.discount_amount > 0 %}
                                <br><small class="text-success">-{{ order.discount_amount }} Dh discount</small>
                                {% endif %}
                            </td>
                            <td>
                                {% if order.status == 'pending' %}
                                    <span class="badge bg-warning">Pending</span>
                                {% elif order.status == 'confirmed' %}
                                    <span class="badge bg-info">Confirmed</span>
                                {% elif order.status == 'picked' %}
                                    <span class="badge bg-primary">Picked</span>
                                {% elif order.status == 'out_for_delivery' %}
                                    <span class="badge bg-secondary">Out for Delivery</span>
                                {% elif order.status == 'delivered' %}
                                    <span class="badge bg-success">Delivered</span>
                                {% elif order.status == 'cancelled' %}
                                    <span class="badge bg-danger">Cancelled</span>
                                {% endif %}
                            </td>
                            <td>
                                <div>
                                    <small class="text-muted">{{ order.branch.name }}</small><br>
                                    {% if order.delivery_person %}
                                    <small class="text-info">{{ order.delivery_person.get_full_name() }}</small>
                                    {% else %}
                                    <small class="text-muted">Not assigned</small>
                                    {% endif %}
                                </div>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ url_for('manager.order_detail', id=order.id) }}" 
                                       class="btn btn-outline-info btn-sm">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    
                                    {% if order.status == 'pending' %}
                                    <button class="btn btn-outline-success btn-sm" 
                                            onclick="confirmOrder({{ order.id }})">
                                        <i class="fas fa-check"></i>
                                    </button>
                                    {% endif %}
                                    
                                    {% if order.status in ['confirmed', 'picked'] and not order.delivery_person %}
                                    <button class="btn btn-outline-primary btn-sm" 
                                            onclick="assignDelivery({{ order.id }})">
                                        <i class="fas fa-truck"></i>
                                    </button>
                                    {% endif %}
                                    
                                    <div class="dropdown">
                                        <button class="btn btn-outline-secondary btn-sm dropdown-toggle" 
                                                type="button" data-bs-toggle="dropdown">
                                            <i class="fas fa-ellipsis-v"></i>
                                        </button>
                                        <ul class="dropdown-menu">
                                            <li><a class="dropdown-item" href="#" onclick="printInvoice({{ order.id }})">
                                                <i class="fas fa-print"></i> Print Invoice
                                            </a></li>
                                            <li><a class="dropdown-item" href="#" onclick="sendEmail({{ order.id }})">
                                                <i class="fas fa-envelope"></i> Send Email
                                            </a></li>
                                            {% if order.status not in ['delivered', 'cancelled'] %}
                                            <li><hr class="dropdown-divider"></li>
                                            <li><a class="dropdown-item text-danger" href="#" onclick="cancelOrder({{ order.id }})">
                                                <i class="fas fa-times"></i> Cancel Order
                                            </a></li>
                                            {% endif %}
                                        </ul>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            {% if orders.pages > 1 %}
            <nav aria-label="Orders pagination" class="mt-4">
                <ul class="pagination justify-content-center">
                    {% if orders.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('manager.orders', page=orders.prev_num, status=status_filter) }}">
                            Previous
                        </a>
                    </li>
                    {% endif %}
                    
                    {% for page_num in orders.iter_pages() %}
                        {% if page_num %}
                            {% if page_num != orders.page %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('manager.orders', page=page_num, status=status_filter) }}">
                                    {{ page_num }}
                                </a>
                            </li>
                            {% else %}
                            <li class="page-item active">
                                <span class="page-link">{{ page_num }}</span>
                            </li>
                            {% endif %}
                        {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">...</span>
                        </li>
                        {% endif %}
                    {% endfor %}
                    
                    {% if orders.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('manager.orders', page=orders.next_num, status=status_filter) }}">
                            Next
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}
            
            {% else %}
            <div class="text-center py-5">
                <i class="fas fa-shopping-cart fa-5x text-muted mb-4"></i>
                <h4>No orders found</h4>
                {% if status_filter %}
                <p class="text-muted">No orders with status "{{ status_filter.replace('_', ' ').title() }}" found.</p>
                <a href="{{ url_for('manager.orders') }}" class="btn btn-primary">View All Orders</a>
                {% else %}
                <p class="text-muted">No orders have been placed yet.</p>
                {% endif %}
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Assign Delivery Modal -->
<div class="modal fade" id="assignDeliveryModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Assign Delivery Person</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="assignDeliveryForm">
                    <div class="mb-3">
                        <label for="delivery_person_id" class="form-label">Select Delivery Person</label>
                        <select class="form-select" id="delivery_person_id" name="delivery_person_id" required>
                            <option value="">Choose delivery person...</option>
                            <!-- Options will be loaded dynamically -->
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="submitAssignment()">Assign</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentOrderId = null;

function confirmOrder(orderId) {
    if (confirm('Are you sure you want to confirm this order?')) {
        fetch(`/manager/order/${orderId}/confirm`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Order confirmed successfully');
                location.reload();
            } else {
                alert(data.message || 'Error confirming order');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error confirming order');
        });
    }
}

function assignDelivery(orderId) {
    currentOrderId = orderId;
    
    // Load delivery personnel
    fetch('/api/delivery-personnel')
        .then(response => response.json())
        .then(data => {
            const select = document.getElementById('delivery_person_id');
            select.innerHTML = '<option value="">Choose delivery person...</option>';
            
            data.forEach(person => {
                const option = document.createElement('option');
                option.value = person.id;
                option.textContent = person.name;
                select.appendChild(option);
            });
            
            new bootstrap.Modal(document.getElementById('assignDeliveryModal')).show();
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error loading delivery personnel');
        });
}

function submitAssignment() {
    const deliveryPersonId = document.getElementById('delivery_person_id').value;
    
    if (!deliveryPersonId) {
        alert('Please select a delivery person');
        return;
    }
    
    fetch(`/manager/order/${currentOrderId}/assign-delivery`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            delivery_person_id: deliveryPersonId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Delivery person assigned successfully');
            bootstrap.Modal.getInstance(document.getElementById('assignDeliveryModal')).hide();
            location.reload();
        } else {
            alert(data.message || 'Error assigning delivery person');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error assigning delivery person');
    });
}

function printInvoice(orderId) {
    window.open(`/invoice/${orderId}/pdf`, '_blank');
}

function sendEmail(orderId) {
    if (confirm('Send order confirmation email to customer?')) {
        fetch(`/manager/order/${orderId}/send-email`, {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Email sent successfully');
            } else {
                alert('Error sending email');
            }
        });
    }
}

function cancelOrder(orderId) {
    if (confirm('Are you sure you want to cancel this order? This action cannot be undone.')) {
        fetch(`/manager/order/${orderId}/cancel`, {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Order cancelled successfully');
                location.reload();
            } else {
                alert(data.message || 'Error cancelling order');
            }
        });
    }
}

// Auto-refresh every 2 minutes
setInterval(function() {
    location.reload();
}, 120000);
</script>
{% endblock %}
