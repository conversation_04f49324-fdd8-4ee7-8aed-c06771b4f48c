<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>
      {% block title %}YalaOffice - Office & School Supplies{% endblock %}
    </title>

    <!-- Bootstrap CSS -->
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <!-- Font Awesome -->
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
      rel="stylesheet"
    />

    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#007bff" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />
    <meta name="apple-mobile-web-app-title" content="YalaOffice" />
    <link
      rel="manifest"
      href="{{ url_for('static', filename='manifest.json') }}"
    />

    <!-- Custom CSS -->
    <style>
      .navbar-brand {
        font-weight: bold;
        color: #007bff !important;
      }
      .footer {
        background-color: #f8f9fa;
        margin-top: auto;
      }
      .card-hover:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
      }
      .price-tag {
        font-size: 1.2em;
        font-weight: bold;
        color: #28a745;
      }
      .cart-badge {
        position: absolute;
        top: -8px;
        right: -8px;
        background: #dc3545;
        color: white;
        border-radius: 50%;
        width: 20px;
        height: 20px;
        font-size: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    </style>

    {% block extra_css %}{% endblock %}
  </head>
  <body class="d-flex flex-column min-vh-100">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-light shadow-sm">
      <div class="container">
        <a class="navbar-brand" href="{{ url_for('main.index') }}">
          <i class="fas fa-store"></i> YalaOffice
        </a>

        <button
          class="navbar-toggler"
          type="button"
          data-bs-toggle="collapse"
          data-bs-target="#navbarNav"
        >
          <span class="navbar-toggler-icon"></span>
        </button>

        <div class="collapse navbar-collapse" id="navbarNav">
          <ul class="navbar-nav me-auto">
            <li class="nav-item">
              <a class="nav-link" href="{{ url_for('main.index') }}">Home</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="{{ url_for('main.products') }}"
                >Products</a
              >
            </li>
            <li class="nav-item">
              <a class="nav-link" href="{{ url_for('main.about') }}">About</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="{{ url_for('main.contact') }}"
                >Contact</a
              >
            </li>
          </ul>

          <!-- Search Form -->
          <form
            class="d-flex me-3"
            method="GET"
            action="{{ url_for('main.search') }}"
          >
            <input
              class="form-control me-2"
              type="search"
              name="q"
              placeholder="Search products..."
              value="{{ request.args.get('q', '') }}"
            />
            <button class="btn btn-outline-primary" type="submit">
              <i class="fas fa-search"></i>
            </button>
          </form>

          <ul class="navbar-nav">
            {% if current_user.is_authenticated %} {% if
            current_user.is_client() %}
            <li class="nav-item">
              <a
                class="nav-link position-relative"
                href="{{ url_for('client.wishlist') }}"
              >
                <i class="fas fa-heart"></i> Wishlist
                <span class="cart-badge" id="wishlist-count"
                  >{{ current_user.wishlist_items.count() }}</span
                >
              </a>
            </li>
            <li class="nav-item">
              <a
                class="nav-link position-relative"
                href="{{ url_for('client.cart') }}"
              >
                <i class="fas fa-shopping-cart"></i> Cart
                <span class="cart-badge" id="cart-count">0</span>
              </a>
            </li>
            <li class="nav-item">
              <a
                class="nav-link position-relative"
                href="{{ url_for('client.notifications') }}"
              >
                <i class="fas fa-bell"></i>
                {% set unread_count =
                current_user.notifications.filter_by(is_read=False).count() %}
                {% if unread_count > 0 %}
                <span class="cart-badge bg-warning" id="notification-count"
                  >{{ unread_count }}</span
                >
                {% endif %}
              </a>
            </li>
            {% endif %}
            <li class="nav-item dropdown">
              <a
                class="nav-link dropdown-toggle"
                href="#"
                id="navbarDropdown"
                role="button"
                data-bs-toggle="dropdown"
              >
                <i class="fas fa-user"></i> {{ current_user.get_full_name() }}
              </a>
              <ul class="dropdown-menu">
                <li>
                  <a
                    class="dropdown-item"
                    href="{{ url_for('main.dashboard') }}"
                    >Dashboard</a
                  >
                </li>
                {% if current_user.is_client() %}
                <li>
                  <a
                    class="dropdown-item"
                    href="{{ url_for('client.profile') }}"
                  >
                    <i class="fas fa-user"></i> My Profile
                  </a>
                </li>
                <li>
                  <a
                    class="dropdown-item"
                    href="{{ url_for('client.orders') }}"
                  >
                    <i class="fas fa-shopping-bag"></i> My Orders
                  </a>
                </li>
                <li>
                  <a
                    class="dropdown-item"
                    href="{{ url_for('client.wishlist') }}"
                  >
                    <i class="fas fa-heart"></i> Wishlist
                  </a>
                </li>
                <li>
                  <a
                    class="dropdown-item"
                    href="{{ url_for('client.notifications') }}"
                  >
                    <i class="fas fa-bell"></i> Notifications {% set
                    unread_count =
                    current_user.notifications.filter_by(is_read=False).count()
                    %} {% if unread_count > 0 %}
                    <span class="badge bg-warning ms-2"
                      >{{ unread_count }}</span
                    >
                    {% endif %}
                  </a>
                </li>
                {% endif %}
                <li><hr class="dropdown-divider" /></li>
                <li>
                  <a class="dropdown-item" href="{{ url_for('auth.logout') }}"
                    >Logout</a
                  >
                </li>
              </ul>
            </li>
            {% else %}
            <li class="nav-item">
              <a class="nav-link" href="{{ url_for('auth.login') }}">Login</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="{{ url_for('auth.register') }}"
                >Register</a
              >
            </li>
            {% endif %}
          </ul>
        </div>
      </div>
    </nav>

    <!-- Flash Messages -->
    {% with messages = get_flashed_messages(with_categories=true) %} {% if
    messages %}
    <div class="container mt-3">
      {% for category, message in messages %}
      <div
        class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show"
        role="alert"
      >
        {{ message }}
        <button
          type="button"
          class="btn-close"
          data-bs-dismiss="alert"
        ></button>
      </div>
      {% endfor %}
    </div>
    {% endif %} {% endwith %}

    <!-- Main Content -->
    <main class="flex-grow-1">{% block content %}{% endblock %}</main>

    <!-- Footer -->
    <footer class="footer py-4 mt-5">
      <div class="container">
        <div class="row">
          <div class="col-md-6">
            <h5>YalaOffice</h5>
            <p>
              Your trusted partner for office and school supplies in Morocco.
            </p>
          </div>
          <div class="col-md-3">
            <h6>Quick Links</h6>
            <ul class="list-unstyled">
              <li>
                <a
                  href="{{ url_for('main.products') }}"
                  class="text-decoration-none"
                  >Products</a
                >
              </li>
              <li>
                <a
                  href="{{ url_for('main.about') }}"
                  class="text-decoration-none"
                  >About Us</a
                >
              </li>
              <li>
                <a
                  href="{{ url_for('main.contact') }}"
                  class="text-decoration-none"
                  >Contact</a
                >
              </li>
            </ul>
          </div>
          <div class="col-md-3">
            <h6>Contact Info</h6>
            <p><i class="fas fa-phone"></i> +212 XXX XXX XXX</p>
            <p><i class="fas fa-envelope"></i> <EMAIL></p>
          </div>
        </div>
        <hr />
        <div class="text-center">
          <p>&copy; 2024 YalaOffice. All rights reserved.</p>
        </div>
      </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JS -->
    <script>
      // Update cart count
      function updateCartCount() {
        // This would be implemented to show actual cart count
        // For now, just a placeholder
      }

      // Call on page load
      document.addEventListener("DOMContentLoaded", updateCartCount);
    </script>

    {% block extra_js %}{% endblock %}
  </body>
</html>
