{% extends "base.html" %}

{% block title %}Delivery Orders - YalaOffice{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h2><i class="fas fa-truck"></i> Delivery Orders</h2>
            <p class="text-muted">Manage your assigned delivery orders</p>
        </div>
        <div class="col-md-4 text-md-end">
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown">
                    <i class="fas fa-filter"></i> Filter by Status
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="{{ url_for('delivery.orders') }}">All Orders</a></li>
                    <li><a class="dropdown-item" href="{{ url_for('delivery.orders', status='confirmed') }}">Ready to Pick</a></li>
                    <li><a class="dropdown-item" href="{{ url_for('delivery.orders', status='picked') }}">Picked</a></li>
                    <li><a class="dropdown-item" href="{{ url_for('delivery.orders', status='out_for_delivery') }}">Out for Delivery</a></li>
                    <li><a class="dropdown-item" href="{{ url_for('delivery.orders', status='delivered') }}">Delivered</a></li>
                </ul>
            </div>
        </div>
    </div>
    
    <!-- Status Summary Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <h4>{{ orders.items|selectattr('status', 'equalto', 'confirmed')|list|length if orders else 0 }}</h4>
                    <p class="mb-0">Ready to Pick</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-warning text-white">
                <div class="card-body text-center">
                    <h4>{{ orders.items|selectattr('status', 'equalto', 'picked')|list|length if orders else 0 }}</h4>
                    <p class="mb-0">Picked</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <h4>{{ orders.items|selectattr('status', 'equalto', 'out_for_delivery')|list|length if orders else 0 }}</h4>
                    <p class="mb-0">Out for Delivery</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h4>{{ orders.items|selectattr('status', 'equalto', 'delivered')|list|length if orders else 0 }}</h4>
                    <p class="mb-0">Delivered Today</p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Orders Table -->
    <div class="card">
        <div class="card-body">
            {% if orders and orders.items %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Order #</th>
                            <th>Customer</th>
                            <th>Delivery Address</th>
                            <th>Items</th>
                            <th>Total</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for order in orders.items %}
                        <tr class="{{ 'table-warning' if order.status == 'confirmed' else 'table-info' if order.status == 'picked' else 'table-primary' if order.status == 'out_for_delivery' else '' }}">
                            <td>
                                <strong>{{ order.order_number }}</strong><br>
                                <small class="text-muted">{{ order.created_at.strftime('%m/%d %I:%M %p') if order.created_at }}</small>
                            </td>
                            <td>
                                <div>
                                    <strong>{{ order.customer.get_full_name() }}</strong><br>
                                    <small class="text-muted">
                                        <i class="fas fa-phone"></i> {{ order.delivery_phone }}<br>
                                        <i class="fas fa-envelope"></i> {{ order.customer.email }}
                                    </small>
                                </div>
                            </td>
                            <td>
                                <div>
                                    <i class="fas fa-map-marker-alt"></i> 
                                    {{ order.delivery_address[:50] }}{% if order.delivery_address|length > 50 %}...{% endif %}<br>
                                    <small class="text-muted">Branch: {{ order.branch.name }}</small>
                                    {% if order.delivery_notes %}
                                    <br><small class="text-info"><i class="fas fa-sticky-note"></i> {{ order.delivery_notes }}</small>
                                    {% endif %}
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-secondary">{{ order.items|length }} items</span><br>
                                <small class="text-muted">{{ order.get_total_quantity() }} units</small>
                            </td>
                            <td>
                                <strong>{{ order.total_amount }} Dh</strong><br>
                                <small class="text-muted">{{ order.payment_method.replace('_', ' ').title() }}</small>
                            </td>
                            <td>
                                {% if order.status == 'confirmed' %}
                                    <span class="badge bg-info">Ready to Pick</span>
                                {% elif order.status == 'picked' %}
                                    <span class="badge bg-warning">Picked</span>
                                {% elif order.status == 'out_for_delivery' %}
                                    <span class="badge bg-primary">Out for Delivery</span>
                                {% elif order.status == 'delivered' %}
                                    <span class="badge bg-success">Delivered</span>
                                {% endif %}
                                {% if order.status == 'out_for_delivery' %}
                                <br><small class="text-muted">Started: {{ order.out_for_delivery_at.strftime('%I:%M %p') if order.out_for_delivery_at }}</small>
                                {% elif order.status == 'delivered' %}
                                <br><small class="text-muted">Delivered: {{ order.delivered_at.strftime('%I:%M %p') if order.delivered_at }}</small>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group-vertical" role="group">
                                    <a href="{{ url_for('delivery.order_detail', id=order.id) }}" 
                                       class="btn btn-outline-info btn-sm mb-1">
                                        <i class="fas fa-eye"></i> View
                                    </a>
                                    
                                    {% if order.status == 'confirmed' %}
                                    <button class="btn btn-outline-warning btn-sm mb-1" 
                                            onclick="updateOrderStatus({{ order.id }}, 'picked')">
                                        <i class="fas fa-hand-paper"></i> Pick
                                    </button>
                                    {% elif order.status == 'picked' %}
                                    <button class="btn btn-outline-primary btn-sm mb-1" 
                                            onclick="updateOrderStatus({{ order.id }}, 'out_for_delivery')">
                                        <i class="fas fa-shipping-fast"></i> Start Delivery
                                    </button>
                                    {% elif order.status == 'out_for_delivery' %}
                                    <button class="btn btn-outline-success btn-sm mb-1" 
                                            onclick="updateOrderStatus({{ order.id }}, 'delivered')">
                                        <i class="fas fa-check-circle"></i> Mark Delivered
                                    </button>
                                    {% endif %}
                                    
                                    <div class="dropdown">
                                        <button class="btn btn-outline-secondary btn-sm dropdown-toggle" 
                                                type="button" data-bs-toggle="dropdown">
                                            <i class="fas fa-ellipsis-v"></i>
                                        </button>
                                        <ul class="dropdown-menu">
                                            <li><a class="dropdown-item" href="tel:{{ order.delivery_phone }}">
                                                <i class="fas fa-phone"></i> Call Customer
                                            </a></li>
                                            <li><a class="dropdown-item" href="#" onclick="getDirections('{{ order.delivery_address }}')">
                                                <i class="fas fa-route"></i> Get Directions
                                            </a></li>
                                            <li><a class="dropdown-item" href="#" onclick="reportIssue({{ order.id }})">
                                                <i class="fas fa-exclamation-triangle"></i> Report Issue
                                            </a></li>
                                        </ul>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            {% if orders.pages > 1 %}
            <nav aria-label="Orders pagination" class="mt-4">
                <ul class="pagination justify-content-center">
                    {% if orders.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('delivery.orders', page=orders.prev_num, status=current_status) }}">
                            Previous
                        </a>
                    </li>
                    {% endif %}
                    
                    {% for page_num in orders.iter_pages() %}
                        {% if page_num %}
                            {% if page_num != orders.page %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('delivery.orders', page=page_num, status=current_status) }}">
                                    {{ page_num }}
                                </a>
                            </li>
                            {% else %}
                            <li class="page-item active">
                                <span class="page-link">{{ page_num }}</span>
                            </li>
                            {% endif %}
                        {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">...</span>
                        </li>
                        {% endif %}
                    {% endfor %}
                    
                    {% if orders.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('delivery.orders', page=orders.next_num, status=current_status) }}">
                            Next
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}
            
            {% else %}
            <div class="text-center py-5">
                <i class="fas fa-truck fa-5x text-muted mb-4"></i>
                <h4>No orders assigned</h4>
                {% if current_status %}
                <p class="text-muted">No orders with status "{{ current_status.replace('_', ' ').title() }}" found.</p>
                <a href="{{ url_for('delivery.orders') }}" class="btn btn-primary">View All Orders</a>
                {% else %}
                <p class="text-muted">You don't have any orders assigned for delivery.</p>
                {% endif %}
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Issue Report Modal -->
<div class="modal fade" id="issueReportModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Report Delivery Issue</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="issueReportForm">
                    <div class="mb-3">
                        <label for="issueType" class="form-label">Issue Type</label>
                        <select class="form-select" id="issueType" name="issue_type" required>
                            <option value="">Select issue type...</option>
                            <option value="customer_not_available">Customer Not Available</option>
                            <option value="wrong_address">Wrong Address</option>
                            <option value="customer_refused">Customer Refused Delivery</option>
                            <option value="damaged_package">Damaged Package</option>
                            <option value="other">Other</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="issueDescription" class="form-label">Description</label>
                        <textarea class="form-control" id="issueDescription" name="description" 
                                  rows="4" placeholder="Describe the issue in detail..." required></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-warning" onclick="submitIssueReport()">
                    Report Issue
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentOrderId = null;

function updateOrderStatus(orderId, newStatus) {
    const statusMessages = {
        'picked': 'mark this order as picked',
        'out_for_delivery': 'start delivery for this order',
        'delivered': 'mark this order as delivered'
    };
    
    if (confirm(`Are you sure you want to ${statusMessages[newStatus]}?`)) {
        fetch(`/delivery/update_status/${orderId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ status: newStatus })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Order status updated successfully');
                location.reload();
            } else {
                alert(data.message || 'Error updating order status');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error updating order status');
        });
    }
}

function getDirections(address) {
    // Open Google Maps with directions
    const encodedAddress = encodeURIComponent(address);
    const mapsUrl = `https://www.google.com/maps/dir/?api=1&destination=${encodedAddress}`;
    window.open(mapsUrl, '_blank');
}

function reportIssue(orderId) {
    currentOrderId = orderId;
    document.getElementById('issueReportForm').reset();
    new bootstrap.Modal(document.getElementById('issueReportModal')).show();
}

function submitIssueReport() {
    const form = document.getElementById('issueReportForm');
    const formData = new FormData(form);
    
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }
    
    fetch(`/delivery/report_issue/${currentOrderId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            issue_type: formData.get('issue_type'),
            description: formData.get('description')
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Issue reported successfully');
            bootstrap.Modal.getInstance(document.getElementById('issueReportModal')).hide();
            location.reload();
        } else {
            alert(data.message || 'Error reporting issue');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error reporting issue');
    });
}

// Auto-refresh every 2 minutes
setInterval(function() {
    location.reload();
}, 120000);

// Geolocation tracking (optional)
if (navigator.geolocation) {
    navigator.geolocation.getCurrentPosition(function(position) {
        // This could be used to update delivery person location
        console.log('Current location:', position.coords.latitude, position.coords.longitude);
    });
}
</script>
{% endblock %}
