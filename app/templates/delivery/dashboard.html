{% extends "base.html" %}

{% block title %}Delivery Dashboard - YalaOffice{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Welcome Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <h2 class="mb-0">
                        <i class="fas fa-truck"></i> Delivery Dashboard
                    </h2>
                    <p class="mb-0">Welcome, {{ current_user.get_full_name() }} - Delivery Personnel</p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Key Statistics -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">{{ total_assigned }}</h4>
                            <p class="mb-0">Assigned Orders</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-clipboard-list fa-2x"></i>
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <a href="{{ url_for('delivery.orders') }}" class="text-white text-decoration-none">
                        View Details <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">{{ picked_orders }}</h4>
                            <p class="mb-0">Picked Orders</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-box fa-2x"></i>
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <a href="{{ url_for('delivery.orders', status='picked') }}" class="text-white text-decoration-none">
                        View Details <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-secondary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">{{ out_for_delivery }}</h4>
                            <p class="mb-0">Out for Delivery</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-shipping-fast fa-2x"></i>
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <a href="{{ url_for('delivery.orders', status='out_for_delivery') }}" class="text-white text-decoration-none">
                        View Details <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">{{ recent_deliveries|length }}</h4>
                            <p class="mb-0">Recent Deliveries</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <a href="{{ url_for('delivery.history') }}" class="text-white text-decoration-none">
                        View Details <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-bolt"></i> Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 mb-2">
                            <a href="{{ url_for('delivery.orders') }}" class="btn btn-outline-primary w-100">
                                <i class="fas fa-list"></i><br>View All Orders
                            </a>
                        </div>
                        <div class="col-md-4 mb-2">
                            <a href="{{ url_for('delivery.orders', status='confirmed') }}" class="btn btn-outline-warning w-100">
                                <i class="fas fa-hand-paper"></i><br>Ready to Pick
                            </a>
                        </div>
                        <div class="col-md-4 mb-2">
                            <a href="{{ url_for('delivery.history') }}" class="btn btn-outline-success w-100">
                                <i class="fas fa-history"></i><br>Delivery History
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <!-- Assigned Orders -->
        <div class="col-lg-8 mb-4">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-tasks"></i> Today's Assigned Orders</h5>
                    <a href="{{ url_for('delivery.orders') }}" class="btn btn-sm btn-outline-primary">View All</a>
                </div>
                <div class="card-body">
                    {% if assigned_orders %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Order #</th>
                                    <th>Customer</th>
                                    <th>Address</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for order in assigned_orders[:5] %}
                                <tr>
                                    <td><strong>{{ order.order_number }}</strong></td>
                                    <td>
                                        {{ order.customer.get_full_name() }}<br>
                                        <small class="text-muted">{{ order.delivery_phone }}</small>
                                    </td>
                                    <td>
                                        <small>{{ order.delivery_address[:50] }}{% if order.delivery_address|length > 50 %}...{% endif %}</small>
                                    </td>
                                    <td>
                                        {% if order.status == 'confirmed' %}
                                            <span class="badge bg-info">Ready to Pick</span>
                                        {% elif order.status == 'picked' %}
                                            <span class="badge bg-warning">Picked</span>
                                        {% elif order.status == 'out_for_delivery' %}
                                            <span class="badge bg-primary">Out for Delivery</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ url_for('delivery.order_detail', id=order.id) }}" 
                                               class="btn btn-sm btn-outline-info">View</a>
                                            {% if order.status == 'confirmed' %}
                                            <button class="btn btn-sm btn-outline-warning" 
                                                    onclick="updateOrderStatus({{ order.id }}, 'picked')">Pick</button>
                                            {% elif order.status == 'picked' %}
                                            <button class="btn btn-sm btn-outline-primary" 
                                                    onclick="updateOrderStatus({{ order.id }}, 'out_for_delivery')">Start Delivery</button>
                                            {% elif order.status == 'out_for_delivery' %}
                                            <button class="btn btn-sm btn-outline-success" 
                                                    onclick="updateOrderStatus({{ order.id }}, 'delivered')">Mark Delivered</button>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-clipboard-check fa-3x text-muted mb-3"></i>
                        <h5>No orders assigned</h5>
                        <p class="text-muted">You don't have any orders assigned for delivery today.</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <!-- Recent Deliveries -->
        <div class="col-lg-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-check-circle text-success"></i> Recent Deliveries</h5>
                </div>
                <div class="card-body">
                    {% if recent_deliveries %}
                    <div class="list-group list-group-flush">
                        {% for order in recent_deliveries %}
                        <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                            <div>
                                <h6 class="mb-1">{{ order.order_number }}</h6>
                                <small class="text-muted">
                                    {{ order.customer.get_full_name() }}<br>
                                    {{ order.delivered_at.strftime('%m/%d %I:%M %p') if order.delivered_at }}
                                </small>
                            </div>
                            <span class="badge bg-success">Delivered</span>
                        </div>
                        {% endfor %}
                    </div>
                    <div class="text-center mt-3">
                        <a href="{{ url_for('delivery.history') }}" class="btn btn-sm btn-outline-success">
                            View All Deliveries
                        </a>
                    </div>
                    {% else %}
                    <p class="text-muted text-center">No recent deliveries.</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    
    <!-- Delivery Map (Placeholder) -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-map"></i> Delivery Route Map</h5>
                </div>
                <div class="card-body">
                    <div class="bg-light d-flex align-items-center justify-content-center" style="height: 300px;">
                        <div class="text-center">
                            <i class="fas fa-map fa-3x text-muted mb-3"></i>
                            <h5>Interactive Map</h5>
                            <p class="text-muted">Map integration will show delivery routes and locations</p>
                            <button class="btn btn-primary" onclick="alert('Map integration coming soon!')">
                                <i class="fas fa-route"></i> Plan Route
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function updateOrderStatus(orderId, newStatus) {
    const statusMessages = {
        'picked': 'mark this order as picked',
        'out_for_delivery': 'start delivery for this order',
        'delivered': 'mark this order as delivered'
    };
    
    if (confirm(`Are you sure you want to ${statusMessages[newStatus]}?`)) {
        fetch(`/delivery/update_status/${orderId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ status: newStatus })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Order status updated successfully');
                location.reload();
            } else {
                alert(data.message || 'Error updating order status');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error updating order status');
        });
    }
}

// Auto-refresh dashboard every 2 minutes
setInterval(function() {
    location.reload();
}, 120000); // 2 minutes

// Geolocation for delivery tracking (placeholder)
function getCurrentLocation() {
    if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(function(position) {
            console.log('Current location:', position.coords.latitude, position.coords.longitude);
            // This would be used to update delivery person location
        });
    }
}

// Get location on page load
getCurrentLocation();
</script>
{% endblock %}
