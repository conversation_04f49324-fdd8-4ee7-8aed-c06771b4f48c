{% extends "base.html" %}

{% block title %}Performance Monitoring - YalaOffice Admin{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h2><i class="fas fa-tachometer-alt"></i> Performance Monitoring</h2>
            <p class="text-muted">Real-time system and application performance metrics</p>
        </div>
        <div class="col-md-4 text-md-end">
            <div class="btn-group">
                <button class="btn btn-outline-success" onclick="refreshData()">
                    <i class="fas fa-sync-alt"></i> Refresh
                </button>
                <a href="{{ url_for('admin.system_health') }}" class="btn btn-outline-info" target="_blank">
                    <i class="fas fa-heartbeat"></i> Health Check
                </a>
            </div>
        </div>
    </div>
    
    <!-- System Status Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-{{ 'danger' if data.system.cpu_usage > 80 else 'warning' if data.system.cpu_usage > 60 else 'success' }}">
                <div class="card-body text-center">
                    <i class="fas fa-microchip fa-2x text-{{ 'danger' if data.system.cpu_usage > 80 else 'warning' if data.system.cpu_usage > 60 else 'success' }} mb-2"></i>
                    <h3>{{ "%.1f"|format(data.system.cpu_usage) }}%</h3>
                    <p class="mb-0">CPU Usage</p>
                    <div class="progress mt-2">
                        <div class="progress-bar bg-{{ 'danger' if data.system.cpu_usage > 80 else 'warning' if data.system.cpu_usage > 60 else 'success' }}" 
                             style="width: {{ data.system.cpu_usage }}%"></div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-{{ 'danger' if data.system.memory_usage > 80 else 'warning' if data.system.memory_usage > 60 else 'success' }}">
                <div class="card-body text-center">
                    <i class="fas fa-memory fa-2x text-{{ 'danger' if data.system.memory_usage > 80 else 'warning' if data.system.memory_usage > 60 else 'success' }} mb-2"></i>
                    <h3>{{ "%.1f"|format(data.system.memory_usage) }}%</h3>
                    <p class="mb-0">Memory Usage</p>
                    <small class="text-muted">{{ data.system.memory_available }} GB available</small>
                    <div class="progress mt-2">
                        <div class="progress-bar bg-{{ 'danger' if data.system.memory_usage > 80 else 'warning' if data.system.memory_usage > 60 else 'success' }}" 
                             style="width: {{ data.system.memory_usage }}%"></div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-{{ 'danger' if data.system.disk_usage > 80 else 'warning' if data.system.disk_usage > 60 else 'success' }}">
                <div class="card-body text-center">
                    <i class="fas fa-hdd fa-2x text-{{ 'danger' if data.system.disk_usage > 80 else 'warning' if data.system.disk_usage > 60 else 'success' }} mb-2"></i>
                    <h3>{{ "%.1f"|format(data.system.disk_usage) }}%</h3>
                    <p class="mb-0">Disk Usage</p>
                    <small class="text-muted">{{ data.system.disk_free }} GB free</small>
                    <div class="progress mt-2">
                        <div class="progress-bar bg-{{ 'danger' if data.system.disk_usage > 80 else 'warning' if data.system.disk_usage > 60 else 'success' }}" 
                             style="width: {{ data.system.disk_usage }}%"></div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-{{ 'danger' if data.application.error_rate > 2 else 'warning' if data.application.error_rate > 1 else 'success' }}">
                <div class="card-body text-center">
                    <i class="fas fa-exclamation-triangle fa-2x text-{{ 'danger' if data.application.error_rate > 2 else 'warning' if data.application.error_rate > 1 else 'success' }} mb-2"></i>
                    <h3>{{ "%.1f"|format(data.application.error_rate) }}%</h3>
                    <p class="mb-0">Error Rate</p>
                    <small class="text-muted">Last 24 hours</small>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Application Performance -->
    <div class="row mb-4">
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-rocket"></i> Application Performance</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6 text-center">
                            <h4 class="text-primary">{{ data.application.avg_response_time }} ms</h4>
                            <p class="text-muted">Avg Response Time</p>
                        </div>
                        <div class="col-6 text-center">
                            <h4 class="text-success">{{ data.application.uptime }}</h4>
                            <p class="text-muted">Uptime</p>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <div class="row">
                        <div class="col-6 text-center">
                            <h5>{{ data.application.active_users_today }}</h5>
                            <p class="text-muted small">Active Users Today</p>
                        </div>
                        <div class="col-6 text-center">
                            <h5>{{ "%.1f"|format(data.application.user_activity_rate) }}%</h5>
                            <p class="text-muted small">User Activity Rate</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-chart-line"></i> Business Metrics</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6 text-center">
                            <h4 class="text-success">{{ data.business.orders_today }}</h4>
                            <p class="text-muted">Orders Today</p>
                        </div>
                        <div class="col-6 text-center">
                            <h4 class="text-info">{{ data.business.revenue_today }} Dh</h4>
                            <p class="text-muted">Revenue Today</p>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <div class="row">
                        <div class="col-6 text-center">
                            <h5>{{ "%.1f"|format(data.business.avg_order_processing_time) }} min</h5>
                            <p class="text-muted small">Avg Processing Time</p>
                        </div>
                        <div class="col-6 text-center">
                            <h5>{{ "%.1f"|format(data.business.inventory_health) }}%</h5>
                            <p class="text-muted small">Inventory Health</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Performance Trends -->
    <div class="row mb-4">
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-chart-area"></i> Response Time Trend</h5>
                </div>
                <div class="card-body">
                    <canvas id="responseTimeChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
        
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-chart-bar"></i> Daily Orders & Revenue</h5>
                </div>
                <div class="card-body">
                    <canvas id="businessMetricsChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Detailed Metrics -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-table"></i> Detailed Performance Metrics</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Orders</th>
                                    <th>Revenue</th>
                                    <th>Avg Response Time</th>
                                    <th>Error Rate</th>
                                    <th>Performance Score</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for metric in data.trends %}
                                <tr>
                                    <td>{{ metric.date.strftime('%m/%d/%Y') }}</td>
                                    <td>{{ metric.orders }}</td>
                                    <td>{{ metric.revenue }} Dh</td>
                                    <td>{{ metric.avg_response_time }} ms</td>
                                    <td>
                                        <span class="badge bg-{{ 'danger' if metric.error_rate > 2 else 'warning' if metric.error_rate > 1 else 'success' }}">
                                            {{ "%.1f"|format(metric.error_rate) }}%
                                        </span>
                                    </td>
                                    <td>
                                        {% set score = 100 - (metric.avg_response_time / 10) - (metric.error_rate * 10) %}
                                        {% set score = score if score > 0 else 0 %}
                                        <div class="progress" style="height: 20px;">
                                            <div class="progress-bar bg-{{ 'success' if score >= 80 else 'warning' if score >= 60 else 'danger' }}" 
                                                 style="width: {{ score }}%">
                                                {{ "%.0f"|format(score) }}%
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Alerts and Recommendations -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-lightbulb"></i> Performance Alerts & Recommendations</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% if data.system.cpu_usage > 80 %}
                        <div class="col-md-6 mb-3">
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-triangle"></i>
                                <strong>High CPU Usage!</strong><br>
                                CPU usage is at {{ "%.1f"|format(data.system.cpu_usage) }}%. Consider optimizing processes or upgrading hardware.
                            </div>
                        </div>
                        {% endif %}
                        
                        {% if data.system.memory_usage > 80 %}
                        <div class="col-md-6 mb-3">
                            <div class="alert alert-warning">
                                <i class="fas fa-memory"></i>
                                <strong>High Memory Usage!</strong><br>
                                Memory usage is at {{ "%.1f"|format(data.system.memory_usage) }}%. Monitor for memory leaks.
                            </div>
                        </div>
                        {% endif %}
                        
                        {% if data.business.low_stock_items > 10 %}
                        <div class="col-md-6 mb-3">
                            <div class="alert alert-info">
                                <i class="fas fa-boxes"></i>
                                <strong>Inventory Alert!</strong><br>
                                {{ data.business.low_stock_items }} items are running low on stock.
                            </div>
                        </div>
                        {% endif %}
                        
                        {% if data.application.error_rate > 1 %}
                        <div class="col-md-6 mb-3">
                            <div class="alert alert-warning">
                                <i class="fas fa-bug"></i>
                                <strong>Error Rate Alert!</strong><br>
                                Error rate is {{ "%.1f"|format(data.application.error_rate) }}%. Check application logs.
                            </div>
                        </div>
                        {% endif %}
                        
                        {% if data.business.avg_order_processing_time > 60 %}
                        <div class="col-md-6 mb-3">
                            <div class="alert alert-info">
                                <i class="fas fa-clock"></i>
                                <strong>Processing Time Alert!</strong><br>
                                Average order processing time is {{ "%.1f"|format(data.business.avg_order_processing_time) }} minutes. Consider workflow optimization.
                            </div>
                        </div>
                        {% endif %}
                        
                        {% if not (data.system.cpu_usage > 80 or data.system.memory_usage > 80 or data.business.low_stock_items > 10 or data.application.error_rate > 1 or data.business.avg_order_processing_time > 60) %}
                        <div class="col-12">
                            <div class="alert alert-success text-center">
                                <i class="fas fa-check-circle fa-2x mb-2"></i><br>
                                <strong>All Systems Operating Normally!</strong><br>
                                No performance issues detected. Keep up the great work!
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Response Time Chart
const responseTimeData = {
    labels: [
        {% for metric in data.trends %}
        '{{ metric.date.strftime("%m/%d") }}',
        {% endfor %}
    ],
    datasets: [{
        label: 'Response Time (ms)',
        data: [
            {% for metric in data.trends %}
            {{ metric.avg_response_time }},
            {% endfor %}
        ],
        borderColor: 'rgb(75, 192, 192)',
        backgroundColor: 'rgba(75, 192, 192, 0.2)',
        tension: 0.1
    }]
};

new Chart(document.getElementById('responseTimeChart'), {
    type: 'line',
    data: responseTimeData,
    options: {
        responsive: true,
        plugins: {
            title: {
                display: true,
                text: 'Average Response Time Trend'
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                title: {
                    display: true,
                    text: 'Response Time (ms)'
                }
            }
        }
    }
});

// Business Metrics Chart
const businessData = {
    labels: [
        {% for metric in data.trends %}
        '{{ metric.date.strftime("%m/%d") }}',
        {% endfor %}
    ],
    datasets: [{
        label: 'Orders',
        data: [
            {% for metric in data.trends %}
            {{ metric.orders }},
            {% endfor %}
        ],
        backgroundColor: 'rgba(54, 162, 235, 0.8)',
        yAxisID: 'y'
    }, {
        label: 'Revenue (Dh)',
        data: [
            {% for metric in data.trends %}
            {{ metric.revenue }},
            {% endfor %}
        ],
        backgroundColor: 'rgba(255, 99, 132, 0.8)',
        yAxisID: 'y1'
    }]
};

new Chart(document.getElementById('businessMetricsChart'), {
    type: 'bar',
    data: businessData,
    options: {
        responsive: true,
        plugins: {
            title: {
                display: true,
                text: 'Daily Orders and Revenue'
            }
        },
        scales: {
            y: {
                type: 'linear',
                display: true,
                position: 'left',
                title: {
                    display: true,
                    text: 'Orders'
                }
            },
            y1: {
                type: 'linear',
                display: true,
                position: 'right',
                title: {
                    display: true,
                    text: 'Revenue (Dh)'
                },
                grid: {
                    drawOnChartArea: false,
                }
            }
        }
    }
});

function refreshData() {
    location.reload();
}

// Auto-refresh every 30 seconds
setInterval(refreshData, 30000);

// Real-time updates simulation
function updateMetrics() {
    // In a real implementation, this would fetch live data via AJAX
    console.log('Updating metrics...');
}

setInterval(updateMetrics, 5000);
</script>
{% endblock %}
