{% extends "base.html" %}

{% block title %}Category Management - YalaOffice Admin{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h2><i class="fas fa-tags"></i> Category Management</h2>
            <p class="text-muted">Manage product categories and organization</p>
        </div>
        <div class="col-md-4 text-md-end">
            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCategoryModal">
                <i class="fas fa-plus"></i> Add New Category
            </button>
        </div>
    </div>
    
    <!-- Categories Grid -->
    <div class="row">
        {% if categories %}
        {% for category in categories %}
        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start mb-3">
                        <div class="flex-grow-1">
                            <h5 class="card-title">{{ category.name }}</h5>
                            {% if category.description %}
                            <p class="card-text text-muted">{{ category.description }}</p>
                            {% endif %}
                        </div>
                        <div class="dropdown">
                            <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-toggle="dropdown">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#" onclick="editCategory({{ category.id }})">
                                    <i class="fas fa-edit"></i> Edit
                                </a></li>
                                <li><a class="dropdown-item" href="#" onclick="viewProducts({{ category.id }})">
                                    <i class="fas fa-box"></i> View Products
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item text-danger" href="#" onclick="deleteCategory({{ category.id }})">
                                    <i class="fas fa-trash"></i> Delete
                                </a></li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="row text-center">
                        <div class="col-6">
                            <h4 class="text-primary">{{ category.products|length }}</h4>
                            <small class="text-muted">Products</small>
                        </div>
                        <div class="col-6">
                            <h4 class="text-{{ 'success' if category.is_active else 'danger' }}">
                                {{ 'Active' if category.is_active else 'Inactive' }}
                            </h4>
                            <small class="text-muted">Status</small>
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <div class="d-flex justify-content-between align-items-center">
                        <small class="text-muted">
                            Created: {{ category.created_at.strftime('%m/%d/%Y') if category.created_at }}
                        </small>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary" onclick="editCategory({{ category.id }})">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-outline-{{ 'danger' if category.is_active else 'success' }}" 
                                    onclick="toggleCategoryStatus({{ category.id }}, {{ category.is_active|lower }})">
                                <i class="fas fa-{{ 'ban' if category.is_active else 'check' }}"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
        {% else %}
        <div class="col-12">
            <div class="text-center py-5">
                <i class="fas fa-tags fa-4x text-muted mb-4"></i>
                <h4>No Categories Found</h4>
                <p class="text-muted">Start by creating your first product category.</p>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCategoryModal">
                    <i class="fas fa-plus"></i> Create First Category
                </button>
            </div>
        </div>
        {% endif %}
    </div>
</div>

<!-- Add Category Modal -->
<div class="modal fade" id="addCategoryModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add New Category</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ url_for('admin.add_category') }}">
                <div class="modal-body">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                    
                    <div class="mb-3">
                        <label for="name" class="form-label">Category Name *</label>
                        <input type="text" class="form-control" id="name" name="name" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="is_active" name="is_active" checked>
                            <label class="form-check-label" for="is_active">
                                Active Category
                            </label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Create Category</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Category Modal -->
<div class="modal fade" id="editCategoryModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Category</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="editCategoryForm">
                <div class="modal-body">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                    <input type="hidden" id="edit_category_id" name="category_id">
                    
                    <div class="mb-3">
                        <label for="edit_name" class="form-label">Category Name *</label>
                        <input type="text" class="form-control" id="edit_name" name="name" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="edit_description" class="form-label">Description</label>
                        <textarea class="form-control" id="edit_description" name="description" rows="3"></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="edit_is_active" name="is_active">
                            <label class="form-check-label" for="edit_is_active">
                                Active Category
                            </label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Category</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Category Products Modal -->
<div class="modal fade" id="categoryProductsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Category Products</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="categoryProductsContent">
                <!-- Content will be loaded here -->
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function editCategory(categoryId) {
    fetch(`/admin/categories/${categoryId}/details`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const category = data.category;
                document.getElementById('edit_category_id').value = category.id;
                document.getElementById('edit_name').value = category.name;
                document.getElementById('edit_description').value = category.description || '';
                document.getElementById('edit_is_active').checked = category.is_active;
                
                const modal = new bootstrap.Modal(document.getElementById('editCategoryModal'));
                modal.show();
            } else {
                alert('Error loading category details');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error loading category details');
        });
}

function toggleCategoryStatus(categoryId, isActive) {
    const action = isActive ? 'deactivate' : 'activate';
    
    if (confirm(`Are you sure you want to ${action} this category?`)) {
        fetch(`/admin/categories/${categoryId}/toggle-status`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error updating category status');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error updating category status');
        });
    }
}

function deleteCategory(categoryId) {
    if (confirm('Are you sure you want to delete this category? This action cannot be undone.')) {
        fetch(`/admin/categories/${categoryId}/delete`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error deleting category: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error deleting category');
        });
    }
}

function viewProducts(categoryId) {
    fetch(`/admin/categories/${categoryId}/products`)
        .then(response => response.text())
        .then(html => {
            document.getElementById('categoryProductsContent').innerHTML = html;
            const modal = new bootstrap.Modal(document.getElementById('categoryProductsModal'));
            modal.show();
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error loading category products');
        });
}

// Edit category form submission
document.getElementById('editCategoryForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const categoryId = formData.get('category_id');
    
    fetch(`/admin/categories/${categoryId}/edit`, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Error updating category: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error updating category');
    });
});
</script>
{% endblock %}
