{% extends "base.html" %}

{% block title %}System Settings - YalaOffice Admin{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h2><i class="fas fa-cogs"></i> System Settings</h2>
            <p class="text-muted">Configure system-wide settings and preferences</p>
        </div>
        <div class="col-md-4 text-md-end">
            <div class="btn-group">
                <button type="button" class="btn btn-outline-warning" onclick="resetSettings()">
                    <i class="fas fa-undo"></i> Reset to Defaults
                </button>
                <a href="{{ url_for('admin.dashboard') }}" class="btn btn-outline-primary">
                    <i class="fas fa-arrow-left"></i> Back to Dashboard
                </a>
            </div>
        </div>
    </div>
    
    <form method="POST" action="{{ url_for('admin.update_settings') }}" id="settingsForm">
        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
        
        <div class="row">
            <!-- General Settings -->
            <div class="col-lg-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-globe"></i> General Settings</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="general_site_name" class="form-label">Site Name</label>
                            <input type="text" class="form-control" id="general_site_name" name="general_site_name" 
                                   value="{{ grouped_settings.general | selectattr('key', 'equalto', 'general_site_name') | map(attribute='value') | first or 'YalaOffice' }}">
                        </div>
                        
                        <div class="mb-3">
                            <label for="general_site_description" class="form-label">Site Description</label>
                            <textarea class="form-control" id="general_site_description" name="general_site_description" rows="2">{{ grouped_settings.general | selectattr('key', 'equalto', 'general_site_description') | map(attribute='value') | first or 'Office & School Supplies' }}</textarea>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="general_currency" class="form-label">Currency</label>
                                <select class="form-select" id="general_currency" name="general_currency">
                                    <option value="Dh" selected>Moroccan Dirham (Dh)</option>
                                    <option value="€">Euro (€)</option>
                                    <option value="$">US Dollar ($)</option>
                                </select>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="general_timezone" class="form-label">Timezone</label>
                                <select class="form-select" id="general_timezone" name="general_timezone">
                                    <option value="Africa/Casablanca" selected>Morocco (GMT+1)</option>
                                    <option value="Europe/Paris">Paris (GMT+1)</option>
                                    <option value="UTC">UTC (GMT+0)</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="general_language" class="form-label">Default Language</label>
                            <select class="form-select" id="general_language" name="general_language">
                                <option value="en" selected>English</option>
                                <option value="fr">Français</option>
                                <option value="ar">العربية</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Email Settings -->
            <div class="col-lg-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-envelope"></i> Email Settings</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="email_smtp_server" class="form-label">SMTP Server</label>
                            <input type="text" class="form-control" id="email_smtp_server" name="email_smtp_server" 
                                   value="{{ grouped_settings.email | selectattr('key', 'equalto', 'email_smtp_server') | map(attribute='value') | first or 'smtp.gmail.com' }}">
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="email_smtp_port" class="form-label">SMTP Port</label>
                                <input type="number" class="form-control" id="email_smtp_port" name="email_smtp_port" 
                                       value="{{ grouped_settings.email | selectattr('key', 'equalto', 'email_smtp_port') | map(attribute='value') | first or '587' }}">
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="email_use_tls" class="form-label">Use TLS</label>
                                <select class="form-select" id="email_use_tls" name="email_use_tls">
                                    <option value="true" selected>Yes</option>
                                    <option value="false">No</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="email_from_name" class="form-label">From Name</label>
                            <input type="text" class="form-control" id="email_from_name" name="email_from_name" 
                                   value="{{ grouped_settings.email | selectattr('key', 'equalto', 'email_from_name') | map(attribute='value') | first or 'YalaOffice' }}">
                        </div>
                        
                        <div class="mb-3">
                            <label for="email_from_address" class="form-label">From Email Address</label>
                            <input type="email" class="form-control" id="email_from_address" name="email_from_address" 
                                   value="{{ grouped_settings.email | selectattr('key', 'equalto', 'email_from_address') | map(attribute='value') | first or '<EMAIL>' }}">
                        </div>
                        
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            <strong>Note:</strong> SMTP username and password should be configured via environment variables for security.
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Payment Settings -->
            <div class="col-lg-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-credit-card"></i> Payment Settings</h5>
                    </div>
                    <div class="card-body">
                        <h6>Enabled Payment Methods:</h6>
                        
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" id="payment_cash_enabled" name="payment_cash_enabled" value="true" checked>
                            <label class="form-check-label" for="payment_cash_enabled">
                                <i class="fas fa-money-bill-wave text-success"></i> Cash Payment
                            </label>
                        </div>
                        
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" id="payment_check_enabled" name="payment_check_enabled" value="true" checked>
                            <label class="form-check-label" for="payment_check_enabled">
                                <i class="fas fa-money-check text-primary"></i> Check Payment
                            </label>
                        </div>
                        
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="payment_bank_transfer_enabled" name="payment_bank_transfer_enabled" value="true" checked>
                            <label class="form-check-label" for="payment_bank_transfer_enabled">
                                <i class="fas fa-university text-info"></i> Bank Transfer
                            </label>
                        </div>
                        
                        <div class="mb-3">
                            <label for="payment_bank_details" class="form-label">Bank Transfer Details</label>
                            <textarea class="form-control" id="payment_bank_details" name="payment_bank_details" rows="3" 
                                      placeholder="Bank name, account number, IBAN, etc.">{{ grouped_settings.payment | selectattr('key', 'equalto', 'payment_bank_details') | map(attribute='value') | first or '' }}</textarea>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Inventory Settings -->
            <div class="col-lg-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-boxes"></i> Inventory Settings</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="inventory_low_stock_threshold" class="form-label">Low Stock Threshold</label>
                            <input type="number" class="form-control" id="inventory_low_stock_threshold" name="inventory_low_stock_threshold" 
                                   value="{{ grouped_settings.inventory | selectattr('key', 'equalto', 'inventory_low_stock_threshold') | map(attribute='value') | first or '10' }}" min="1">
                            <div class="form-text">Alert when stock falls below this number</div>
                        </div>
                        
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="inventory_auto_reorder" name="inventory_auto_reorder" value="true">
                            <label class="form-check-label" for="inventory_auto_reorder">
                                Enable Auto-Reorder Notifications
                            </label>
                            <div class="form-text">Send notifications when products need reordering</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="inventory_reorder_point" class="form-label">Auto-Reorder Point</label>
                            <input type="number" class="form-control" id="inventory_reorder_point" name="inventory_reorder_point" 
                                   value="{{ grouped_settings.inventory | selectattr('key', 'equalto', 'inventory_reorder_point') | map(attribute='value') | first or '5' }}" min="1">
                            <div class="form-text">Trigger reorder when stock reaches this level</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Security Settings -->
            <div class="col-lg-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-shield-alt"></i> Security Settings</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="security_password_min_length" class="form-label">Minimum Password Length</label>
                            <input type="number" class="form-control" id="security_password_min_length" name="security_password_min_length" 
                                   value="{{ grouped_settings.security | selectattr('key', 'equalto', 'security_password_min_length') | map(attribute='value') | first or '6' }}" min="6" max="20">
                        </div>
                        
                        <div class="mb-3">
                            <label for="security_session_timeout" class="form-label">Session Timeout (minutes)</label>
                            <input type="number" class="form-control" id="security_session_timeout" name="security_session_timeout" 
                                   value="{{ grouped_settings.security | selectattr('key', 'equalto', 'security_session_timeout') | map(attribute='value') | first or '30' }}" min="5" max="480">
                        </div>
                        
                        <div class="mb-3">
                            <label for="security_max_login_attempts" class="form-label">Max Login Attempts</label>
                            <input type="number" class="form-control" id="security_max_login_attempts" name="security_max_login_attempts" 
                                   value="{{ grouped_settings.security | selectattr('key', 'equalto', 'security_max_login_attempts') | map(attribute='value') | first or '5' }}" min="3" max="10">
                            <div class="form-text">Account will be locked after this many failed attempts</div>
                        </div>
                        
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="security_require_email_verification" name="security_require_email_verification" value="true">
                            <label class="form-check-label" for="security_require_email_verification">
                                Require Email Verification for New Accounts
                            </label>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Notification Settings -->
            <div class="col-lg-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-bell"></i> Notification Settings</h5>
                    </div>
                    <div class="card-body">
                        <h6>Email Notifications:</h6>
                        
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" id="notifications_order_confirmation" name="notifications_order_confirmation" value="true" checked>
                            <label class="form-check-label" for="notifications_order_confirmation">
                                Order Confirmation Emails
                            </label>
                        </div>
                        
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" id="notifications_order_status" name="notifications_order_status" value="true" checked>
                            <label class="form-check-label" for="notifications_order_status">
                                Order Status Update Emails
                            </label>
                        </div>
                        
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" id="notifications_low_stock" name="notifications_low_stock" value="true" checked>
                            <label class="form-check-label" for="notifications_low_stock">
                                Low Stock Alert Emails
                            </label>
                        </div>
                        
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="notifications_welcome" name="notifications_welcome" value="true" checked>
                            <label class="form-check-label" for="notifications_welcome">
                                Welcome Emails for New Users
                            </label>
                        </div>
                        
                        <div class="mb-3">
                            <label for="notifications_admin_email" class="form-label">Admin Notification Email</label>
                            <input type="email" class="form-control" id="notifications_admin_email" name="notifications_admin_email" 
                                   value="{{ grouped_settings.other | selectattr('key', 'equalto', 'notifications_admin_email') | map(attribute='value') | first or '<EMAIL>' }}">
                            <div class="form-text">Email address for system notifications</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Save Button -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body text-center">
                        <button type="submit" class="btn btn-success btn-lg">
                            <i class="fas fa-save"></i> Save All Settings
                        </button>
                        <button type="button" class="btn btn-outline-secondary btn-lg ms-3" onclick="location.reload()">
                            <i class="fas fa-undo"></i> Cancel Changes
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_js %}
<script>
function resetSettings() {
    if (confirm('Are you sure you want to reset all settings to their default values? This action cannot be undone.')) {
        fetch('{{ url_for("admin.reset_settings") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'csrf_token={{ csrf_token() }}'
        })
        .then(response => {
            if (response.ok) {
                location.reload();
            } else {
                alert('Error resetting settings');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error resetting settings');
        });
    }
}

// Form validation
document.getElementById('settingsForm').addEventListener('submit', function(e) {
    const requiredFields = ['general_site_name', 'email_smtp_server', 'email_from_address'];
    let isValid = true;
    
    requiredFields.forEach(fieldId => {
        const field = document.getElementById(fieldId);
        if (!field.value.trim()) {
            field.classList.add('is-invalid');
            isValid = false;
        } else {
            field.classList.remove('is-invalid');
        }
    });
    
    if (!isValid) {
        e.preventDefault();
        alert('Please fill in all required fields');
    }
});

// Auto-save draft (optional)
let autoSaveTimer;
document.querySelectorAll('input, textarea, select').forEach(field => {
    field.addEventListener('change', function() {
        clearTimeout(autoSaveTimer);
        autoSaveTimer = setTimeout(() => {
            // Could implement auto-save to localStorage here
            console.log('Settings changed - consider auto-save');
        }, 2000);
    });
});
</script>
{% endblock %}
