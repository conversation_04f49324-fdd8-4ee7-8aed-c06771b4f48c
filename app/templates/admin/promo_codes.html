{% extends "base.html" %}

{% block title %}Promo Codes Management - Admin - YalaOffice{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h2><i class="fas fa-tags"></i> Promo Codes Management</h2>
            <p class="text-muted">Manage discount codes and promotional offers</p>
        </div>
        <div class="col-md-4 text-md-end">
            <a href="{{ url_for('admin.add_promo_code') }}" class="btn btn-success">
                <i class="fas fa-plus"></i> Add New Promo Code
            </a>
        </div>
    </div>
    
    <!-- Promo Codes Table -->
    <div class="card">
        <div class="card-body">
            {% if promo_codes.items %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Code</th>
                            <th>Description</th>
                            <th>Discount</th>
                            <th>Min Order</th>
                            <th>Usage</th>
                            <th>Valid Until</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for promo in promo_codes.items %}
                        <tr>
                            <td>
                                <strong class="text-primary">{{ promo.code }}</strong>
                            </td>
                            <td>{{ promo.description or 'No description' }}</td>
                            <td>
                                {% if promo.discount_type == 'percentage' %}
                                    <span class="badge bg-info">{{ promo.discount_value }}%</span>
                                {% else %}
                                    <span class="badge bg-success">{{ promo.discount_value }} Dh</span>
                                {% endif %}
                                {% if promo.max_discount_amount %}
                                <br><small class="text-muted">Max: {{ promo.max_discount_amount }} Dh</small>
                                {% endif %}
                            </td>
                            <td>
                                {% if promo.min_order_amount > 0 %}
                                    {{ promo.min_order_amount }} Dh
                                {% else %}
                                    <span class="text-muted">No minimum</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <span>{{ promo.used_count }}</span>
                                    {% if promo.usage_limit %}
                                    <span class="text-muted">/ {{ promo.usage_limit }}</span>
                                    <div class="progress ms-2" style="width: 60px; height: 8px;">
                                        <div class="progress-bar" style="width: {{ (promo.used_count / promo.usage_limit * 100) if promo.usage_limit > 0 else 0 }}%"></div>
                                    </div>
                                    {% else %}
                                    <span class="text-muted">/ ∞</span>
                                    {% endif %}
                                </div>
                            </td>
                            <td>
                                {% if promo.valid_until %}
                                    {{ promo.valid_until.strftime('%Y-%m-%d') }}
                                    {% if promo.valid_until < moment().date() %}
                                    <br><small class="text-danger">Expired</small>
                                    {% endif %}
                                {% else %}
                                    <span class="text-muted">No expiry</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if promo.is_active %}
                                    {% set is_valid, message = promo.is_valid(0) %}
                                    {% if is_valid %}
                                        <span class="badge bg-success">Active</span>
                                    {% else %}
                                        <span class="badge bg-warning">{{ message }}</span>
                                    {% endif %}
                                {% else %}
                                    <span class="badge bg-danger">Inactive</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <button class="btn btn-outline-info btn-sm" 
                                            onclick="viewPromoDetails({{ promo.id }})">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-primary btn-sm" 
                                            onclick="editPromo({{ promo.id }})">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-outline-{{ 'danger' if promo.is_active else 'success' }} btn-sm" 
                                            onclick="togglePromoStatus({{ promo.id }}, {{ promo.is_active|lower }})">
                                        {% if promo.is_active %}
                                        <i class="fas fa-pause"></i>
                                        {% else %}
                                        <i class="fas fa-play"></i>
                                        {% endif %}
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            {% if promo_codes.pages > 1 %}
            <nav aria-label="Promo codes pagination" class="mt-4">
                <ul class="pagination justify-content-center">
                    {% if promo_codes.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('admin.promo_codes', page=promo_codes.prev_num) }}">
                            Previous
                        </a>
                    </li>
                    {% endif %}
                    
                    {% for page_num in promo_codes.iter_pages() %}
                        {% if page_num %}
                            {% if page_num != promo_codes.page %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('admin.promo_codes', page=page_num) }}">
                                    {{ page_num }}
                                </a>
                            </li>
                            {% else %}
                            <li class="page-item active">
                                <span class="page-link">{{ page_num }}</span>
                            </li>
                            {% endif %}
                        {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">...</span>
                        </li>
                        {% endif %}
                    {% endfor %}
                    
                    {% if promo_codes.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('admin.promo_codes', page=promo_codes.next_num) }}">
                            Next
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}
            
            {% else %}
            <div class="text-center py-5">
                <i class="fas fa-tags fa-5x text-muted mb-4"></i>
                <h4>No promo codes found</h4>
                <p class="text-muted">Start by creating your first promotional offer.</p>
                <a href="{{ url_for('admin.add_promo_code') }}" class="btn btn-success">
                    <i class="fas fa-plus"></i> Create First Promo Code
                </a>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Promo Details Modal -->
<div class="modal fade" id="promoDetailsModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Promo Code Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="promoDetailsContent">
                <!-- Content will be loaded dynamically -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function viewPromoDetails(promoId) {
    // Fetch and display promo code details
    fetch(`/admin/promo-code/${promoId}/details`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('promoDetailsContent').innerHTML = data.html;
                new bootstrap.Modal(document.getElementById('promoDetailsModal')).show();
            } else {
                alert('Error loading promo code details');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error loading promo code details');
        });
}

function editPromo(promoId) {
    window.location.href = `/admin/promo-code/${promoId}/edit`;
}

function togglePromoStatus(promoId, currentStatus) {
    const action = currentStatus ? 'deactivate' : 'activate';
    
    if (confirm(`Are you sure you want to ${action} this promo code?`)) {
        fetch(`/admin/promo-code/${promoId}/toggle`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert(data.message || 'Error updating promo code status');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error updating promo code status');
        });
    }
}

// Auto-refresh every 5 minutes
setInterval(function() {
    location.reload();
}, 300000);
</script>
{% endblock %}
