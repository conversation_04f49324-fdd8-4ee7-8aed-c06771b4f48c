{% extends "base.html" %}

{% block title %}Reports Dashboard - YalaOffice Admin{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h2><i class="fas fa-chart-line"></i> Reports Dashboard</h2>
            <p class="text-muted">Generate comprehensive business reports and insights</p>
        </div>
        <div class="col-md-4 text-md-end">
            <a href="{{ url_for('admin.dashboard') }}" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left"></i> Back to Dashboard
            </a>
        </div>
    </div>
    
    <!-- Quick Report Cards -->
    <div class="row mb-5">
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="card border-primary h-100">
                <div class="card-body text-center">
                    <div class="mb-3">
                        <i class="fas fa-chart-bar fa-3x text-primary"></i>
                    </div>
                    <h5 class="card-title">Sales Report</h5>
                    <p class="card-text">Comprehensive sales analytics with revenue tracking, order trends, and performance metrics.</p>
                    <a href="{{ url_for('admin.sales_report') }}" class="btn btn-primary">
                        <i class="fas fa-chart-line"></i> Generate Report
                    </a>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="card border-success h-100">
                <div class="card-body text-center">
                    <div class="mb-3">
                        <i class="fas fa-boxes fa-3x text-success"></i>
                    </div>
                    <h5 class="card-title">Inventory Report</h5>
                    <p class="card-text">Stock levels, low inventory alerts, product valuation, and inventory turnover analysis.</p>
                    <a href="{{ url_for('admin.inventory_report') }}" class="btn btn-success">
                        <i class="fas fa-warehouse"></i> Generate Report
                    </a>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="card border-info h-100">
                <div class="card-body text-center">
                    <div class="mb-3">
                        <i class="fas fa-users fa-3x text-info"></i>
                    </div>
                    <h5 class="card-title">Customer Report</h5>
                    <p class="card-text">Customer analytics, order history, loyalty metrics, and customer segmentation data.</p>
                    <a href="{{ url_for('admin.customer_report') }}" class="btn btn-info">
                        <i class="fas fa-user-chart"></i> Generate Report
                    </a>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="card border-warning h-100">
                <div class="card-body text-center">
                    <div class="mb-3">
                        <i class="fas fa-map-marker-alt fa-3x text-warning"></i>
                    </div>
                    <h5 class="card-title">Branch Report</h5>
                    <p class="card-text">Branch performance comparison, regional sales analysis, and operational efficiency metrics.</p>
                    <a href="{{ url_for('admin.branch_report') }}" class="btn btn-warning">
                        <i class="fas fa-building"></i> Generate Report
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Advanced Reports -->
    <div class="row mb-5">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-cogs"></i> Advanced Reports</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <div class="list-group">
                                <h6 class="list-group-item list-group-item-action active">
                                    <i class="fas fa-money-bill-wave"></i> Financial Reports
                                </h6>
                                <a href="{{ url_for('admin.profit_loss_report') }}" class="list-group-item list-group-item-action">
                                    Profit & Loss Statement
                                </a>
                                <a href="{{ url_for('admin.tax_report') }}" class="list-group-item list-group-item-action">
                                    Tax Summary Report
                                </a>
                                <a href="{{ url_for('admin.payment_methods_report') }}" class="list-group-item list-group-item-action">
                                    Payment Methods Analysis
                                </a>
                            </div>
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <div class="list-group">
                                <h6 class="list-group-item list-group-item-action active">
                                    <i class="fas fa-shopping-cart"></i> Product Reports
                                </h6>
                                <a href="{{ url_for('admin.product_performance_report') }}" class="list-group-item list-group-item-action">
                                    Product Performance
                                </a>
                                <a href="{{ url_for('admin.category_analysis_report') }}" class="list-group-item list-group-item-action">
                                    Category Analysis
                                </a>
                                <a href="{{ url_for('admin.abc_analysis_report') }}" class="list-group-item list-group-item-action">
                                    ABC Analysis
                                </a>
                            </div>
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <div class="list-group">
                                <h6 class="list-group-item list-group-item-action active">
                                    <i class="fas fa-truck"></i> Operations Reports
                                </h6>
                                <a href="{{ url_for('admin.delivery_performance_report') }}" class="list-group-item list-group-item-action">
                                    Delivery Performance
                                </a>
                                <a href="{{ url_for('admin.order_fulfillment_report') }}" class="list-group-item list-group-item-action">
                                    Order Fulfillment
                                </a>
                                <a href="{{ url_for('admin.staff_performance_report') }}" class="list-group-item list-group-item-action">
                                    Staff Performance
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Custom Report Builder -->
    <div class="row mb-5">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-tools"></i> Custom Report Builder</h5>
                </div>
                <div class="card-body">
                    <form id="customReportForm">
                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <label for="reportType" class="form-label">Report Type</label>
                                <select class="form-select" id="reportType" name="report_type">
                                    <option value="">Select Report Type</option>
                                    <option value="sales">Sales Analysis</option>
                                    <option value="inventory">Inventory Analysis</option>
                                    <option value="customer">Customer Analysis</option>
                                    <option value="product">Product Analysis</option>
                                </select>
                            </div>
                            
                            <div class="col-md-3 mb-3">
                                <label for="dateRange" class="form-label">Date Range</label>
                                <select class="form-select" id="dateRange" name="date_range">
                                    <option value="7">Last 7 days</option>
                                    <option value="30" selected>Last 30 days</option>
                                    <option value="90">Last 90 days</option>
                                    <option value="365">Last year</option>
                                    <option value="custom">Custom range</option>
                                </select>
                            </div>
                            
                            <div class="col-md-3 mb-3">
                                <label for="branch" class="form-label">Branch</label>
                                <select class="form-select" id="branch" name="branch_id">
                                    <option value="">All Branches</option>
                                    <!-- Branches would be populated here -->
                                </select>
                            </div>
                            
                            <div class="col-md-3 mb-3">
                                <label for="format" class="form-label">Export Format</label>
                                <select class="form-select" id="format" name="format">
                                    <option value="html">View Online</option>
                                    <option value="pdf">PDF Download</option>
                                    <option value="excel">Excel Download</option>
                                    <option value="csv">CSV Download</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="row" id="customDateRange" style="display: none;">
                            <div class="col-md-6 mb-3">
                                <label for="startDate" class="form-label">Start Date</label>
                                <input type="date" class="form-control" id="startDate" name="start_date">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="endDate" class="form-label">End Date</label>
                                <input type="date" class="form-control" id="endDate" name="end_date">
                            </div>
                        </div>
                        
                        <div class="text-center">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-chart-line"></i> Generate Custom Report
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Scheduled Reports -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-clock"></i> Scheduled Reports</h5>
                    <button class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#scheduleReportModal">
                        <i class="fas fa-plus"></i> Schedule New Report
                    </button>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Report Name</th>
                                    <th>Type</th>
                                    <th>Frequency</th>
                                    <th>Next Run</th>
                                    <th>Recipients</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>Weekly Sales Summary</td>
                                    <td><span class="badge bg-primary">Sales</span></td>
                                    <td>Weekly (Monday)</td>
                                    <td>2024-01-15 09:00</td>
                                    <td><EMAIL></td>
                                    <td><span class="badge bg-success">Active</span></td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-info">Edit</button>
                                        <button class="btn btn-sm btn-outline-danger">Delete</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Monthly Inventory Report</td>
                                    <td><span class="badge bg-success">Inventory</span></td>
                                    <td>Monthly (1st)</td>
                                    <td>2024-02-01 08:00</td>
                                    <td><EMAIL></td>
                                    <td><span class="badge bg-success">Active</span></td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-info">Edit</button>
                                        <button class="btn btn-sm btn-outline-danger">Delete</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Schedule Report Modal -->
<div class="modal fade" id="scheduleReportModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Schedule New Report</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="scheduleReportForm">
                    <div class="mb-3">
                        <label for="reportName" class="form-label">Report Name</label>
                        <input type="text" class="form-control" id="reportName" name="report_name" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="scheduleReportType" class="form-label">Report Type</label>
                        <select class="form-select" id="scheduleReportType" name="report_type" required>
                            <option value="">Select Report Type</option>
                            <option value="sales">Sales Report</option>
                            <option value="inventory">Inventory Report</option>
                            <option value="customer">Customer Report</option>
                            <option value="branch">Branch Report</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="frequency" class="form-label">Frequency</label>
                        <select class="form-select" id="frequency" name="frequency" required>
                            <option value="">Select Frequency</option>
                            <option value="daily">Daily</option>
                            <option value="weekly">Weekly</option>
                            <option value="monthly">Monthly</option>
                            <option value="quarterly">Quarterly</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="recipients" class="form-label">Email Recipients</label>
                        <textarea class="form-control" id="recipients" name="recipients" rows="3" 
                                  placeholder="Enter email addresses separated by commas"></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="scheduleFormat" class="form-label">Format</label>
                        <select class="form-select" id="scheduleFormat" name="format">
                            <option value="pdf">PDF</option>
                            <option value="excel">Excel</option>
                            <option value="csv">CSV</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="scheduleReport()">Schedule Report</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Show/hide custom date range
document.getElementById('dateRange').addEventListener('change', function() {
    const customRange = document.getElementById('customDateRange');
    if (this.value === 'custom') {
        customRange.style.display = 'block';
    } else {
        customRange.style.display = 'none';
    }
});

// Custom report form submission
document.getElementById('customReportForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const reportType = formData.get('report_type');
    
    if (!reportType) {
        alert('Please select a report type');
        return;
    }
    
    // Build URL based on report type
    let url = '';
    switch(reportType) {
        case 'sales':
            url = '{{ url_for("admin.sales_report") }}';
            break;
        case 'inventory':
            url = '{{ url_for("admin.inventory_report") }}';
            break;
        case 'customer':
            url = '{{ url_for("admin.customer_report") }}';
            break;
        case 'product':
            url = '{{ url_for("admin.product_performance_report") }}';
            break;
        default:
            alert('Invalid report type');
            return;
    }
    
    // Add parameters to URL
    const params = new URLSearchParams();
    for (let [key, value] of formData.entries()) {
        if (value) {
            params.append(key, value);
        }
    }
    
    // Redirect to report
    window.open(`${url}?${params.toString()}`, '_blank');
});

function scheduleReport() {
    const form = document.getElementById('scheduleReportForm');
    const formData = new FormData(form);
    
    // Validate form
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }
    
    // Submit to server
    fetch('/admin/schedule_report', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Report scheduled successfully!');
            location.reload();
        } else {
            alert('Error scheduling report: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error scheduling report');
    });
}
</script>
{% endblock %}
