{% extends "base.html" %}

{% block title %}Sales Report - YalaOffice Admin{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h2><i class="fas fa-chart-line"></i> Sales Report</h2>
            <p class="text-muted">
                Sales analytics and performance metrics for 
                {{ report.start_date.strftime('%B %d, %Y') }} - {{ report.end_date.strftime('%B %d, %Y') }}
            </p>
        </div>
        <div class="col-md-4 text-md-end">
            <a href="{{ url_for('admin.reports') }}" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left"></i> Back to Reports
            </a>
        </div>
    </div>
    
    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-3">
                            <label for="start_date" class="form-label">Start Date</label>
                            <input type="date" class="form-control" id="start_date" name="start_date" 
                                   value="{{ request.args.get('start_date', '') }}">
                        </div>
                        <div class="col-md-3">
                            <label for="end_date" class="form-label">End Date</label>
                            <input type="date" class="form-control" id="end_date" name="end_date" 
                                   value="{{ request.args.get('end_date', '') }}">
                        </div>
                        <div class="col-md-3">
                            <label for="branch_id" class="form-label">Branch</label>
                            <select class="form-select" id="branch_id" name="branch_id">
                                <option value="">All Branches</option>
                                {% for branch in branches %}
                                <option value="{{ branch.id }}" {{ 'selected' if report.selected_branch == branch.id }}>
                                    {{ branch.name }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-filter"></i> Apply Filters
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Summary Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-success">
                <div class="card-body text-center">
                    <i class="fas fa-money-bill-wave fa-2x text-success mb-2"></i>
                    <h3 class="text-success">{{ report.total_sales }} Dh</h3>
                    <p class="mb-0">Total Sales</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-primary">
                <div class="card-body text-center">
                    <i class="fas fa-shopping-cart fa-2x text-primary mb-2"></i>
                    <h3 class="text-primary">{{ report.total_orders }}</h3>
                    <p class="mb-0">Total Orders</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-info">
                <div class="card-body text-center">
                    <i class="fas fa-calculator fa-2x text-info mb-2"></i>
                    <h3 class="text-info">{{ "%.2f"|format(report.avg_order_value) }} Dh</h3>
                    <p class="mb-0">Avg Order Value</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-warning">
                <div class="card-body text-center">
                    <i class="fas fa-chart-line fa-2x text-warning mb-2"></i>
                    <h3 class="text-warning">
                        {{ "%.1f"|format((report.total_sales / (report.end_date - report.start_date).days) if (report.end_date - report.start_date).days > 0 else 0) }} Dh
                    </h3>
                    <p class="mb-0">Daily Average</p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Sales Trend Chart -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-chart-area"></i> Sales Trend</h5>
                </div>
                <div class="card-body">
                    <canvas id="salesTrendChart" width="400" height="100"></canvas>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Top Products and Branch Performance -->
    <div class="row mb-4">
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-trophy"></i> Top Selling Products</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Rank</th>
                                    <th>Product</th>
                                    <th>Quantity Sold</th>
                                    <th>Revenue</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for product in report.top_products %}
                                <tr>
                                    <td>
                                        <span class="badge bg-{{ 'success' if loop.index == 1 else 'primary' if loop.index <= 3 else 'secondary' }}">
                                            #{{ loop.index }}
                                        </span>
                                    </td>
                                    <td><strong>{{ product.title }}</strong></td>
                                    <td>{{ product.quantity_sold }}</td>
                                    <td><strong>{{ product.revenue }} Dh</strong></td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-building"></i> Sales by Branch</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Branch</th>
                                    <th>Orders</th>
                                    <th>Total Sales</th>
                                    <th>Share</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for branch in report.branch_sales %}
                                <tr>
                                    <td><strong>{{ branch.name }}</strong></td>
                                    <td>{{ branch.orders }}</td>
                                    <td><strong>{{ branch.total }} Dh</strong></td>
                                    <td>
                                        {% set percentage = (branch.total / report.total_sales * 100) if report.total_sales > 0 else 0 %}
                                        <div class="progress" style="height: 20px;">
                                            <div class="progress-bar" role="progressbar" 
                                                 style="width: {{ percentage }}%">
                                                {{ "%.1f"|format(percentage) }}%
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Daily Sales Breakdown -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-calendar-alt"></i> Daily Sales Breakdown</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Orders</th>
                                    <th>Total Sales</th>
                                    <th>Avg Order Value</th>
                                    <th>Performance</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for day in report.daily_sales %}
                                <tr>
                                    <td>{{ day.date.strftime('%B %d, %Y') if day.date }}</td>
                                    <td>{{ day.orders }}</td>
                                    <td><strong>{{ day.total }} Dh</strong></td>
                                    <td>{{ "%.2f"|format(day.total / day.orders if day.orders > 0 else 0) }} Dh</td>
                                    <td>
                                        {% set daily_avg = report.total_sales / report.daily_sales|length if report.daily_sales|length > 0 else 0 %}
                                        {% if day.total > daily_avg * 1.2 %}
                                        <span class="badge bg-success">Excellent</span>
                                        {% elif day.total > daily_avg * 0.8 %}
                                        <span class="badge bg-primary">Good</span>
                                        {% else %}
                                        <span class="badge bg-warning">Below Average</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Export Options -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-download"></i> Export Report</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-2">
                            <button class="btn btn-outline-danger w-100" onclick="exportReport('pdf')">
                                <i class="fas fa-file-pdf"></i> Export as PDF
                            </button>
                        </div>
                        <div class="col-md-3 mb-2">
                            <button class="btn btn-outline-success w-100" onclick="exportReport('excel')">
                                <i class="fas fa-file-excel"></i> Export as Excel
                            </button>
                        </div>
                        <div class="col-md-3 mb-2">
                            <button class="btn btn-outline-info w-100" onclick="exportReport('csv')">
                                <i class="fas fa-file-csv"></i> Export as CSV
                            </button>
                        </div>
                        <div class="col-md-3 mb-2">
                            <button class="btn btn-outline-primary w-100" onclick="window.print()">
                                <i class="fas fa-print"></i> Print Report
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Sales trend chart
const salesData = {
    labels: [
        {% for day in report.daily_sales %}
        '{{ day.date.strftime("%m/%d") if day.date }}',
        {% endfor %}
    ],
    datasets: [{
        label: 'Daily Sales (Dh)',
        data: [
            {% for day in report.daily_sales %}
            {{ day.total }},
            {% endfor %}
        ],
        borderColor: 'rgb(75, 192, 192)',
        backgroundColor: 'rgba(75, 192, 192, 0.2)',
        tension: 0.1,
        fill: true
    }, {
        label: 'Orders Count',
        data: [
            {% for day in report.daily_sales %}
            {{ day.orders }},
            {% endfor %}
        ],
        borderColor: 'rgb(255, 99, 132)',
        backgroundColor: 'rgba(255, 99, 132, 0.2)',
        tension: 0.1,
        yAxisID: 'y1'
    }]
};

const salesConfig = {
    type: 'line',
    data: salesData,
    options: {
        responsive: true,
        interaction: {
            mode: 'index',
            intersect: false,
        },
        plugins: {
            title: {
                display: true,
                text: 'Daily Sales Performance'
            }
        },
        scales: {
            y: {
                type: 'linear',
                display: true,
                position: 'left',
                title: {
                    display: true,
                    text: 'Sales (Dh)'
                }
            },
            y1: {
                type: 'linear',
                display: true,
                position: 'right',
                title: {
                    display: true,
                    text: 'Orders Count'
                },
                grid: {
                    drawOnChartArea: false,
                },
            }
        }
    }
};

new Chart(document.getElementById('salesTrendChart'), salesConfig);

function exportReport(format) {
    const params = new URLSearchParams(window.location.search);
    params.set('format', format);
    
    const url = `{{ url_for('admin.sales_report') }}?${params.toString()}`;
    window.open(url, '_blank');
}
</script>
{% endblock %}
