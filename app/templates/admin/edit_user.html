{% extends "base.html" %}

{% block title %}Edit User - YalaOffice Admin{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h4><i class="fas fa-user-edit"></i> Edit User: {{ user.username }}</h4>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ url_for('admin.edit_user', user_id=user.id) }}">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="username" class="form-label">Username *</label>
                                <input type="text" class="form-control" id="username" name="username" 
                                       value="{{ user.username }}" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">Email *</label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       value="{{ user.email }}" required>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="first_name" class="form-label">First Name</label>
                                <input type="text" class="form-control" id="first_name" name="first_name" 
                                       value="{{ user.first_name or '' }}">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="last_name" class="form-label">Last Name</label>
                                <input type="text" class="form-control" id="last_name" name="last_name" 
                                       value="{{ user.last_name or '' }}">
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="password" class="form-label">New Password</label>
                                <input type="password" class="form-control" id="password" name="password">
                                <div class="form-text">Leave blank to keep current password</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="role" class="form-label">Role *</label>
                                <select class="form-select" id="role" name="role" required>
                                    <option value="client" {{ 'selected' if user.role == 'client' }}>Client</option>
                                    <option value="reseller" {{ 'selected' if user.role == 'reseller' }}>Reseller</option>
                                    <option value="delivery" {{ 'selected' if user.role == 'delivery' }}>Delivery</option>
                                    <option value="manager" {{ 'selected' if user.role == 'manager' }}>Manager</option>
                                    <option value="admin" {{ 'selected' if user.role == 'admin' }}>Admin</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label">Phone</label>
                                <input type="tel" class="form-control" id="phone" name="phone" 
                                       value="{{ user.phone or '' }}">
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="form-check mt-4">
                                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                           {{ 'checked' if user.is_active }}>
                                    <label class="form-check-label" for="is_active">
                                        Active User
                                    </label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="address" class="form-label">Address</label>
                            <textarea class="form-control" id="address" name="address" rows="3">{{ user.address or '' }}</textarea>
                        </div>
                        
                        <!-- User Statistics -->
                        <div class="row mb-3">
                            <div class="col-12">
                                <h6>User Statistics</h6>
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="text-center">
                                            <h5 class="text-primary">{{ user.orders.count() }}</h5>
                                            <small class="text-muted">Total Orders</small>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="text-center">
                                            <h5 class="text-success">
                                                {{ "%.2f"|format(user.orders.filter_by(status='delivered').with_entities(db.func.sum(Order.total_amount)).scalar() or 0) }} Dh
                                            </h5>
                                            <small class="text-muted">Total Spent</small>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="text-center">
                                            <h5 class="text-info">
                                                {{ user.created_at.strftime('%m/%d/%Y') if user.created_at }}
                                            </h5>
                                            <small class="text-muted">Member Since</small>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="text-center">
                                            <h5 class="text-warning">
                                                {{ user.last_login.strftime('%m/%d/%Y') if user.last_login else 'Never' }}
                                            </h5>
                                            <small class="text-muted">Last Login</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('admin.users') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Back to Users
                            </a>
                            <div>
                                {% if user.id != current_user.id %}
                                <button type="button" class="btn btn-danger me-2" onclick="deleteUser()">
                                    <i class="fas fa-trash"></i> Delete User
                                </button>
                                {% endif %}
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> Update User
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function deleteUser() {
    if (confirm('Are you sure you want to delete this user? This action cannot be undone.')) {
        fetch(`/admin/users/{{ user.id }}/delete`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                window.location.href = '{{ url_for("admin.users") }}';
            } else {
                alert('Error deleting user: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error deleting user');
        });
    }
}

// Form validation
document.querySelector('form').addEventListener('submit', function(e) {
    const username = document.getElementById('username').value;
    const email = document.getElementById('email').value;
    const role = document.getElementById('role').value;
    const password = document.getElementById('password').value;
    
    if (!username || !email || !role) {
        e.preventDefault();
        alert('Please fill in all required fields');
        return;
    }
    
    if (password && password.length < 6) {
        e.preventDefault();
        alert('Password must be at least 6 characters long');
        return;
    }
    
    if (!email.includes('@')) {
        e.preventDefault();
        alert('Please enter a valid email address');
        return;
    }
});
</script>
{% endblock %}
