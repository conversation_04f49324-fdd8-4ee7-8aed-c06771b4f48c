{% extends "base.html" %}

{% block title %}Analytics Dashboard - Admin - YalaOffice{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <h2><i class="fas fa-chart-bar"></i> Analytics Dashboard</h2>
            <p class="text-muted">Comprehensive business insights and performance metrics</p>
        </div>
    </div>
    
    <!-- Key Metrics -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">{{ "%.2f"|format(total_revenue) }} Dh</h4>
                            <p class="mb-0">Total Revenue</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-dollar-sign fa-2x"></i>
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <small>{{ "%.2f"|format(monthly_revenue) }} Dh this month</small>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">{{ total_orders }}</h4>
                            <p class="mb-0">Total Orders</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-shopping-cart fa-2x"></i>
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <small>{{ monthly_orders }} orders this month</small>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">{{ total_customers }}</h4>
                            <p class="mb-0">Total Customers</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-users fa-2x"></i>
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <small>{{ new_customers }} new this month</small>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">{{ total_products }}</h4>
                            <p class="mb-0">Active Products</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-box fa-2x"></i>
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <small>{{ low_stock_count }} low stock items</small>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Charts Row -->
    <div class="row mb-4">
        <!-- Sales Chart -->
        <div class="col-lg-8 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-chart-line"></i> Monthly Sales Trend</h5>
                </div>
                <div class="card-body">
                    <canvas id="salesChart" height="100"></canvas>
                </div>
            </div>
        </div>
        
        <!-- Order Status Pie Chart -->
        <div class="col-lg-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-chart-pie"></i> Order Status</h5>
                </div>
                <div class="card-body">
                    <canvas id="orderStatusChart"></canvas>
                    <div class="mt-3">
                        <div class="d-flex justify-content-between">
                            <span>Pending:</span>
                            <span class="badge bg-warning">{{ pending_orders }}</span>
                        </div>
                        <div class="d-flex justify-content-between">
                            <span>Delivered:</span>
                            <span class="badge bg-success">{{ delivered_orders }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Data Tables Row -->
    <div class="row mb-4">
        <!-- Top Products -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-trophy"></i> Top Selling Products</h5>
                </div>
                <div class="card-body">
                    {% if top_products %}
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Product</th>
                                    <th>Sold</th>
                                    <th>Revenue</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for product in top_products %}
                                <tr>
                                    <td>{{ product.title[:30] }}{% if product.title|length > 30 %}...{% endif %}</td>
                                    <td><span class="badge bg-primary">{{ product.total_sold }}</span></td>
                                    <td><strong>{{ "%.2f"|format(product.total_revenue) }} Dh</strong></td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <p class="text-muted text-center">No sales data available</p>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <!-- Category Performance -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-tags"></i> Category Performance</h5>
                </div>
                <div class="card-body">
                    {% if category_sales %}
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Category</th>
                                    <th>Items</th>
                                    <th>Revenue</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for category in category_sales %}
                                <tr>
                                    <td>{{ category.name }}</td>
                                    <td><span class="badge bg-info">{{ category.total_items }}</span></td>
                                    <td><strong>{{ "%.2f"|format(category.total_revenue) }} Dh</strong></td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <p class="text-muted text-center">No category data available</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    
    <!-- Branch Performance -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-map-marker-alt"></i> Branch Performance</h5>
                </div>
                <div class="card-body">
                    {% if branch_performance %}
                    <div class="row">
                        {% for branch in branch_performance %}
                        <div class="col-lg-4 mb-3">
                            <div class="card border-left-primary">
                                <div class="card-body">
                                    <h6 class="card-title">{{ branch.name }}</h6>
                                    <div class="row">
                                        <div class="col-6">
                                            <div class="text-center">
                                                <h4 class="text-primary">{{ branch.total_orders }}</h4>
                                                <small class="text-muted">Orders</small>
                                            </div>
                                        </div>
                                        <div class="col-6">
                                            <div class="text-center">
                                                <h4 class="text-success">{{ "%.0f"|format(branch.total_revenue) }} Dh</h4>
                                                <small class="text-muted">Revenue</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <p class="text-muted text-center">No branch data available</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Sales Chart
    const salesCtx = document.getElementById('salesChart').getContext('2d');
    const salesData = {
        labels: [{% for sale in monthly_sales %}'Month {{ sale.month }}'{% if not loop.last %},{% endif %}{% endfor %}],
        datasets: [{
            label: 'Revenue (Dh)',
            data: [{% for sale in monthly_sales %}{{ sale.revenue or 0 }}{% if not loop.last %},{% endif %}{% endfor %}],
            borderColor: 'rgb(75, 192, 192)',
            backgroundColor: 'rgba(75, 192, 192, 0.2)',
            tension: 0.1
        }, {
            label: 'Orders',
            data: [{% for sale in monthly_sales %}{{ sale.orders or 0 }}{% if not loop.last %},{% endif %}{% endfor %}],
            borderColor: 'rgb(255, 99, 132)',
            backgroundColor: 'rgba(255, 99, 132, 0.2)',
            yAxisID: 'y1',
            tension: 0.1
        }]
    };
    
    new Chart(salesCtx, {
        type: 'line',
        data: salesData,
        options: {
            responsive: true,
            interaction: {
                mode: 'index',
                intersect: false,
            },
            scales: {
                y: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                },
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    grid: {
                        drawOnChartArea: false,
                    },
                }
            }
        }
    });
    
    // Order Status Pie Chart
    const orderStatusCtx = document.getElementById('orderStatusChart').getContext('2d');
    new Chart(orderStatusCtx, {
        type: 'doughnut',
        data: {
            labels: ['Pending', 'Delivered', 'Others'],
            datasets: [{
                data: [{{ pending_orders }}, {{ delivered_orders }}, {{ total_orders - pending_orders - delivered_orders }}],
                backgroundColor: [
                    '#ffc107',
                    '#28a745',
                    '#6c757d'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
});
</script>
{% endblock %}
