{% extends "base.html" %}

{% block title %}Branch Performance Report - YalaOffice Admin{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h2><i class="fas fa-building"></i> Branch Performance Report</h2>
            <p class="text-muted">
                Branch analytics and performance metrics for 
                {{ report.start_date.strftime('%B %d, %Y') }} - {{ report.end_date.strftime('%B %d, %Y') }}
            </p>
        </div>
        <div class="col-md-4 text-md-end">
            <a href="{{ url_for('admin.reports') }}" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left"></i> Back to Reports
            </a>
        </div>
    </div>
    
    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-4">
                            <label for="start_date" class="form-label">Start Date</label>
                            <input type="date" class="form-control" id="start_date" name="start_date" 
                                   value="{{ request.args.get('start_date', '') }}">
                        </div>
                        <div class="col-md-4">
                            <label for="end_date" class="form-label">End Date</label>
                            <input type="date" class="form-control" id="end_date" name="end_date" 
                                   value="{{ request.args.get('end_date', '') }}">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-filter"></i> Apply Filters
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Branch Performance Cards -->
    <div class="row">
        {% for branch_data in report.branch_performance %}
        <div class="col-lg-6 col-xl-4 mb-4">
            <div class="card h-100 border-{{ 'success' if loop.index == 1 else 'primary' if loop.index <= 3 else 'secondary' }}">
                <div class="card-header bg-{{ 'success' if loop.index == 1 else 'primary' if loop.index <= 3 else 'secondary' }} text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-map-marker-alt"></i> {{ branch_data.branch.name }}
                        </h5>
                        {% if loop.index == 1 %}
                        <span class="badge bg-warning text-dark">
                            <i class="fas fa-crown"></i> Top Performer
                        </span>
                        {% endif %}
                    </div>
                    <small>{{ branch_data.branch.address }}</small>
                </div>
                <div class="card-body">
                    <!-- Sales Metrics -->
                    <div class="row mb-3">
                        <div class="col-6">
                            <div class="text-center">
                                <h4 class="text-success">{{ branch_data.total_sales }} Dh</h4>
                                <small class="text-muted">Total Sales</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center">
                                <h4 class="text-primary">{{ branch_data.total_orders }}</h4>
                                <small class="text-muted">Total Orders</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-12">
                            <div class="text-center">
                                <h5 class="text-info">{{ "%.2f"|format(branch_data.avg_order_value) }} Dh</h5>
                                <small class="text-muted">Average Order Value</small>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Inventory Metrics -->
                    <hr>
                    <h6><i class="fas fa-boxes"></i> Inventory Status</h6>
                    <div class="row mb-3">
                        <div class="col-4">
                            <div class="text-center">
                                <h6 class="text-info">{{ branch_data.total_products }}</h6>
                                <small class="text-muted">Products</small>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="text-center">
                                <h6 class="text-warning">{{ branch_data.low_stock_items }}</h6>
                                <small class="text-muted">Low Stock</small>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="text-center">
                                <h6 class="text-danger">{{ branch_data.out_of_stock_items }}</h6>
                                <small class="text-muted">Out of Stock</small>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Delivery Performance -->
                    <hr>
                    <h6><i class="fas fa-truck"></i> Delivery Performance</h6>
                    <div class="progress mb-2">
                        <div class="progress-bar bg-{{ 'success' if branch_data.delivery_rate >= 90 else 'warning' if branch_data.delivery_rate >= 70 else 'danger' }}" 
                             role="progressbar" 
                             style="width: {{ branch_data.delivery_rate }}%">
                            {{ "%.1f"|format(branch_data.delivery_rate) }}%
                        </div>
                    </div>
                    <small class="text-muted">Delivery Success Rate</small>
                    
                    <!-- Branch Contact Info -->
                    <hr>
                    <div class="small">
                        {% if branch_data.branch.phone %}
                        <p class="mb-1"><i class="fas fa-phone text-primary"></i> {{ branch_data.branch.phone }}</p>
                        {% endif %}
                        {% if branch_data.branch.email %}
                        <p class="mb-1"><i class="fas fa-envelope text-primary"></i> {{ branch_data.branch.email }}</p>
                        {% endif %}
                        {% if branch_data.branch.manager %}
                        <p class="mb-0"><i class="fas fa-user text-primary"></i> Manager: {{ branch_data.branch.manager.get_full_name() }}</p>
                        {% endif %}
                    </div>
                </div>
                <div class="card-footer">
                    <div class="row">
                        <div class="col-6">
                            <button class="btn btn-sm btn-outline-info w-100" onclick="viewBranchDetails({{ branch_data.branch.id }})">
                                <i class="fas fa-eye"></i> Details
                            </button>
                        </div>
                        <div class="col-6">
                            <button class="btn btn-sm btn-outline-primary w-100" onclick="manageBranch({{ branch_data.branch.id }})">
                                <i class="fas fa-cog"></i> Manage
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
    
    <!-- Performance Comparison Chart -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-chart-bar"></i> Branch Performance Comparison</h5>
                </div>
                <div class="card-body">
                    <canvas id="branchComparisonChart" width="400" height="100"></canvas>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Detailed Performance Table -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-table"></i> Detailed Performance Metrics</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Rank</th>
                                    <th>Branch</th>
                                    <th>Total Sales</th>
                                    <th>Orders</th>
                                    <th>Avg Order Value</th>
                                    <th>Delivery Rate</th>
                                    <th>Inventory Issues</th>
                                    <th>Performance</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for branch_data in report.branch_performance %}
                                <tr>
                                    <td>
                                        <span class="badge bg-{{ 'success' if loop.index == 1 else 'primary' if loop.index <= 3 else 'secondary' }}">
                                            #{{ loop.index }}
                                        </span>
                                    </td>
                                    <td>
                                        <strong>{{ branch_data.branch.name }}</strong><br>
                                        <small class="text-muted">{{ branch_data.branch.address }}</small>
                                    </td>
                                    <td><strong>{{ branch_data.total_sales }} Dh</strong></td>
                                    <td>{{ branch_data.total_orders }}</td>
                                    <td>{{ "%.2f"|format(branch_data.avg_order_value) }} Dh</td>
                                    <td>
                                        <span class="badge bg-{{ 'success' if branch_data.delivery_rate >= 90 else 'warning' if branch_data.delivery_rate >= 70 else 'danger' }}">
                                            {{ "%.1f"|format(branch_data.delivery_rate) }}%
                                        </span>
                                    </td>
                                    <td>
                                        <small>
                                            <span class="text-warning">{{ branch_data.low_stock_items }} low</span> | 
                                            <span class="text-danger">{{ branch_data.out_of_stock_items }} out</span>
                                        </small>
                                    </td>
                                    <td>
                                        {% set performance_score = (branch_data.total_sales / 1000 + branch_data.delivery_rate) / 2 %}
                                        <div class="progress" style="height: 20px;">
                                            <div class="progress-bar bg-{{ 'success' if performance_score >= 80 else 'warning' if performance_score >= 60 else 'danger' }}" 
                                                 role="progressbar" 
                                                 style="width: {{ performance_score }}%">
                                                {{ "%.0f"|format(performance_score) }}%
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Export Options -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-download"></i> Export Report</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-2">
                            <button class="btn btn-outline-danger w-100" onclick="exportReport('pdf')">
                                <i class="fas fa-file-pdf"></i> Export as PDF
                            </button>
                        </div>
                        <div class="col-md-3 mb-2">
                            <button class="btn btn-outline-success w-100" onclick="exportReport('excel')">
                                <i class="fas fa-file-excel"></i> Export as Excel
                            </button>
                        </div>
                        <div class="col-md-3 mb-2">
                            <button class="btn btn-outline-info w-100" onclick="exportReport('csv')">
                                <i class="fas fa-file-csv"></i> Export as CSV
                            </button>
                        </div>
                        <div class="col-md-3 mb-2">
                            <button class="btn btn-outline-primary w-100" onclick="window.print()">
                                <i class="fas fa-print"></i> Print Report
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Branch comparison chart
const branchData = {
    labels: [
        {% for branch_data in report.branch_performance %}
        '{{ branch_data.branch.name }}',
        {% endfor %}
    ],
    datasets: [{
        label: 'Total Sales (Dh)',
        data: [
            {% for branch_data in report.branch_performance %}
            {{ branch_data.total_sales }},
            {% endfor %}
        ],
        backgroundColor: [
            'rgba(255, 99, 132, 0.2)',
            'rgba(54, 162, 235, 0.2)',
            'rgba(255, 205, 86, 0.2)',
            'rgba(75, 192, 192, 0.2)',
            'rgba(153, 102, 255, 0.2)',
            'rgba(255, 159, 64, 0.2)'
        ],
        borderColor: [
            'rgba(255, 99, 132, 1)',
            'rgba(54, 162, 235, 1)',
            'rgba(255, 205, 86, 1)',
            'rgba(75, 192, 192, 1)',
            'rgba(153, 102, 255, 1)',
            'rgba(255, 159, 64, 1)'
        ],
        borderWidth: 1
    }]
};

const branchConfig = {
    type: 'bar',
    data: branchData,
    options: {
        responsive: true,
        plugins: {
            title: {
                display: true,
                text: 'Sales Performance by Branch'
            }
        },
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
};

new Chart(document.getElementById('branchComparisonChart'), branchConfig);

function viewBranchDetails(branchId) {
    // Redirect to branch details page
    window.location.href = `/admin/branch/${branchId}`;
}

function manageBranch(branchId) {
    // Redirect to branch management page
    window.location.href = `/admin/branch/${branchId}/edit`;
}

function exportReport(format) {
    const params = new URLSearchParams(window.location.search);
    params.set('format', format);
    
    const url = `{{ url_for('admin.branch_report') }}?${params.toString()}`;
    window.open(url, '_blank');
}
</script>
{% endblock %}
