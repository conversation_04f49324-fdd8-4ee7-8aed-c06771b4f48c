{% extends "base.html" %}

{% block title %}Bulk Operations - YalaOffice Admin{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h2><i class="fas fa-tasks"></i> Bulk Operations</h2>
            <p class="text-muted">Perform bulk operations on products, users, and orders efficiently</p>
        </div>
        <div class="col-md-4 text-md-end">
            <a href="{{ url_for('admin.dashboard') }}" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left"></i> Back to Dashboard
            </a>
        </div>
    </div>
    
    <!-- Operation Type Selector -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="btn-group w-100" role="group" aria-label="Operation types">
                        <button type="button" class="btn btn-outline-primary active" onclick="showOperation('products')">
                            <i class="fas fa-box"></i> Products
                        </button>
                        <button type="button" class="btn btn-outline-success" onclick="showOperation('users')">
                            <i class="fas fa-users"></i> Users
                        </button>
                        <button type="button" class="btn btn-outline-info" onclick="showOperation('orders')">
                            <i class="fas fa-shopping-cart"></i> Orders
                        </button>
                        <button type="button" class="btn btn-outline-warning" onclick="showOperation('inventory')">
                            <i class="fas fa-warehouse"></i> Inventory
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Product Bulk Operations -->
    <div id="products-operations" class="operation-section">
        <div class="row">
            <div class="col-lg-8 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-box"></i> Product Selection</h5>
                    </div>
                    <div class="card-body">
                        <!-- Search and Filter -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <input type="text" class="form-control" id="product-search" placeholder="Search products...">
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="product-category">
                                    <option value="">All Categories</option>
                                    <!-- Categories will be loaded here -->
                                </select>
                            </div>
                            <div class="col-md-3">
                                <button class="btn btn-primary w-100" onclick="loadProducts()">
                                    <i class="fas fa-search"></i> Search
                                </button>
                            </div>
                        </div>
                        
                        <!-- Product List -->
                        <div class="table-responsive" style="max-height: 400px; overflow-y: auto;">
                            <table class="table table-hover">
                                <thead class="sticky-top bg-light">
                                    <tr>
                                        <th>
                                            <input type="checkbox" id="select-all-products" onchange="toggleAllProducts()">
                                        </th>
                                        <th>Product</th>
                                        <th>Category</th>
                                        <th>Price</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody id="products-list">
                                    <!-- Products will be loaded here -->
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="mt-3">
                            <span class="text-muted">Selected: <span id="selected-products-count">0</span> products</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-4 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-cogs"></i> Product Operations</h5>
                    </div>
                    <div class="card-body">
                        <form id="product-bulk-form" method="POST" action="{{ url_for('admin.bulk_update_products') }}">
                            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                            
                            <div class="mb-3">
                                <label for="product-action" class="form-label">Action</label>
                                <select class="form-select" id="product-action" name="action" onchange="showProductActionFields()">
                                    <option value="">Select Action</option>
                                    <option value="activate">Activate Products</option>
                                    <option value="deactivate">Deactivate Products</option>
                                    <option value="delete">Delete Products</option>
                                    <option value="update_category">Change Category</option>
                                    <option value="update_prices">Update Prices</option>
                                </select>
                            </div>
                            
                            <!-- Category Change Fields -->
                            <div id="category-fields" class="mb-3" style="display: none;">
                                <label for="new_category_id" class="form-label">New Category</label>
                                <select class="form-select" name="new_category_id">
                                    <option value="">Select Category</option>
                                    <!-- Categories will be loaded here -->
                                </select>
                            </div>
                            
                            <!-- Price Update Fields -->
                            <div id="price-fields" style="display: none;">
                                <div class="mb-3">
                                    <label for="price_change_type" class="form-label">Price Change Type</label>
                                    <select class="form-select" name="price_change_type">
                                        <option value="percentage">Percentage</option>
                                        <option value="fixed">Fixed Amount</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label for="price_change_value" class="form-label">Value</label>
                                    <input type="number" class="form-control" name="price_change_value" step="0.01" placeholder="e.g., 10 for 10% or 10 Dh">
                                </div>
                            </div>
                            
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary" id="execute-product-action" disabled>
                                    <i class="fas fa-play"></i> Execute Action
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- User Bulk Operations -->
    <div id="users-operations" class="operation-section" style="display: none;">
        <div class="row">
            <div class="col-lg-8 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-users"></i> User Selection</h5>
                    </div>
                    <div class="card-body">
                        <!-- Search and Filter -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <input type="text" class="form-control" id="user-search" placeholder="Search users...">
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="user-role">
                                    <option value="">All Roles</option>
                                    <option value="client">Clients</option>
                                    <option value="reseller">Resellers</option>
                                    <option value="delivery">Delivery</option>
                                    <option value="manager">Managers</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <button class="btn btn-primary w-100" onclick="loadUsers()">
                                    <i class="fas fa-search"></i> Search
                                </button>
                            </div>
                        </div>
                        
                        <!-- User List -->
                        <div class="table-responsive" style="max-height: 400px; overflow-y: auto;">
                            <table class="table table-hover">
                                <thead class="sticky-top bg-light">
                                    <tr>
                                        <th>
                                            <input type="checkbox" id="select-all-users" onchange="toggleAllUsers()">
                                        </th>
                                        <th>User</th>
                                        <th>Email</th>
                                        <th>Role</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody id="users-list">
                                    <!-- Users will be loaded here -->
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="mt-3">
                            <span class="text-muted">Selected: <span id="selected-users-count">0</span> users</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-4 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-cogs"></i> User Operations</h5>
                    </div>
                    <div class="card-body">
                        <form id="user-bulk-form" method="POST" action="{{ url_for('admin.bulk_update_users') }}">
                            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                            
                            <div class="mb-3">
                                <label for="user-action" class="form-label">Action</label>
                                <select class="form-select" id="user-action" name="action" onchange="showUserActionFields()">
                                    <option value="">Select Action</option>
                                    <option value="activate">Activate Users</option>
                                    <option value="deactivate">Deactivate Users</option>
                                    <option value="change_role">Change Role</option>
                                    <option value="send_notification">Send Notification</option>
                                </select>
                            </div>
                            
                            <!-- Role Change Fields -->
                            <div id="role-fields" class="mb-3" style="display: none;">
                                <label for="new_role" class="form-label">New Role</label>
                                <select class="form-select" name="new_role">
                                    <option value="">Select Role</option>
                                    <option value="client">Client</option>
                                    <option value="reseller">Reseller</option>
                                    <option value="delivery">Delivery</option>
                                    <option value="manager">Manager</option>
                                </select>
                            </div>
                            
                            <!-- Notification Fields -->
                            <div id="notification-fields" style="display: none;">
                                <div class="mb-3">
                                    <label for="notification_title" class="form-label">Notification Title</label>
                                    <input type="text" class="form-control" name="notification_title" placeholder="Enter notification title">
                                </div>
                                <div class="mb-3">
                                    <label for="notification_message" class="form-label">Message</label>
                                    <textarea class="form-control" name="notification_message" rows="3" placeholder="Enter notification message"></textarea>
                                </div>
                            </div>
                            
                            <div class="d-grid">
                                <button type="submit" class="btn btn-success" id="execute-user-action" disabled>
                                    <i class="fas fa-play"></i> Execute Action
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Order Bulk Operations -->
    <div id="orders-operations" class="operation-section" style="display: none;">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-shopping-cart"></i> Order Bulk Operations</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <div class="card border-primary">
                                    <div class="card-body text-center">
                                        <i class="fas fa-check-circle fa-2x text-primary mb-2"></i>
                                        <h5>Bulk Confirm Orders</h5>
                                        <p class="text-muted">Confirm multiple pending orders at once</p>
                                        <button class="btn btn-primary" onclick="bulkConfirmOrders()">
                                            Confirm Orders
                                        </button>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <div class="card border-success">
                                    <div class="card-body text-center">
                                        <i class="fas fa-truck fa-2x text-success mb-2"></i>
                                        <h5>Assign Delivery</h5>
                                        <p class="text-muted">Assign delivery personnel to multiple orders</p>
                                        <button class="btn btn-success" onclick="bulkAssignDelivery()">
                                            Assign Delivery
                                        </button>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <div class="card border-info">
                                    <div class="card-body text-center">
                                        <i class="fas fa-file-export fa-2x text-info mb-2"></i>
                                        <h5>Export Orders</h5>
                                        <p class="text-muted">Export order data in various formats</p>
                                        <button class="btn btn-info" onclick="exportOrders()">
                                            Export Data
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Inventory Bulk Operations -->
    <div id="inventory-operations" class="operation-section" style="display: none;">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-warehouse"></i> Inventory Bulk Operations</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <div class="card border-warning">
                                    <div class="card-body text-center">
                                        <i class="fas fa-plus-circle fa-2x text-warning mb-2"></i>
                                        <h5>Bulk Stock Adjustment</h5>
                                        <p class="text-muted">Adjust stock levels for multiple products</p>
                                        <button class="btn btn-warning" onclick="bulkStockAdjustment()">
                                            Adjust Stock
                                        </button>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <div class="card border-danger">
                                    <div class="card-body text-center">
                                        <i class="fas fa-exclamation-triangle fa-2x text-danger mb-2"></i>
                                        <h5>Low Stock Alert</h5>
                                        <p class="text-muted">Send alerts for low stock items</p>
                                        <button class="btn btn-danger" onclick="sendLowStockAlerts()">
                                            Send Alerts
                                        </button>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <div class="card border-secondary">
                                    <div class="card-body text-center">
                                        <i class="fas fa-sync-alt fa-2x text-secondary mb-2"></i>
                                        <h5>Sync Inventory</h5>
                                        <p class="text-muted">Synchronize inventory across branches</p>
                                        <button class="btn btn-secondary" onclick="syncInventory()">
                                            Sync Now
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Progress Modal -->
    <div class="modal fade" id="progressModal" tabindex="-1" data-bs-backdrop="static">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Processing...</h5>
                </div>
                <div class="modal-body">
                    <div class="progress mb-3">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" 
                             role="progressbar" style="width: 0%" id="progress-bar"></div>
                    </div>
                    <p id="progress-text">Initializing...</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let selectedProducts = [];
let selectedUsers = [];

function showOperation(type) {
    // Hide all operation sections
    document.querySelectorAll('.operation-section').forEach(section => {
        section.style.display = 'none';
    });
    
    // Show selected operation section
    document.getElementById(type + '-operations').style.display = 'block';
    
    // Update button states
    document.querySelectorAll('.btn-group button').forEach(btn => {
        btn.classList.remove('active');
    });
    event.target.classList.add('active');
    
    // Load data for the selected operation
    if (type === 'products') {
        loadProducts();
    } else if (type === 'users') {
        loadUsers();
    }
}

function loadProducts() {
    // Simulate loading products
    const productsList = document.getElementById('products-list');
    productsList.innerHTML = '<tr><td colspan="5" class="text-center">Loading products...</td></tr>';
    
    // In a real implementation, this would fetch from the API
    setTimeout(() => {
        productsList.innerHTML = `
            <tr>
                <td><input type="checkbox" class="product-checkbox" value="1"></td>
                <td>Sample Product 1</td>
                <td>Office Supplies</td>
                <td>25.00 Dh</td>
                <td><span class="badge bg-success">Active</span></td>
            </tr>
            <tr>
                <td><input type="checkbox" class="product-checkbox" value="2"></td>
                <td>Sample Product 2</td>
                <td>School Supplies</td>
                <td>15.50 Dh</td>
                <td><span class="badge bg-warning">Inactive</span></td>
            </tr>
        `;
        
        // Add event listeners to checkboxes
        document.querySelectorAll('.product-checkbox').forEach(checkbox => {
            checkbox.addEventListener('change', updateSelectedProducts);
        });
    }, 1000);
}

function loadUsers() {
    // Simulate loading users
    const usersList = document.getElementById('users-list');
    usersList.innerHTML = '<tr><td colspan="5" class="text-center">Loading users...</td></tr>';
    
    // In a real implementation, this would fetch from the API
    setTimeout(() => {
        usersList.innerHTML = `
            <tr>
                <td><input type="checkbox" class="user-checkbox" value="1"></td>
                <td>John Doe</td>
                <td><EMAIL></td>
                <td><span class="badge bg-primary">Client</span></td>
                <td><span class="badge bg-success">Active</span></td>
            </tr>
            <tr>
                <td><input type="checkbox" class="user-checkbox" value="2"></td>
                <td>Jane Smith</td>
                <td><EMAIL></td>
                <td><span class="badge bg-success">Reseller</span></td>
                <td><span class="badge bg-success">Active</span></td>
            </tr>
        `;
        
        // Add event listeners to checkboxes
        document.querySelectorAll('.user-checkbox').forEach(checkbox => {
            checkbox.addEventListener('change', updateSelectedUsers);
        });
    }, 1000);
}

function toggleAllProducts() {
    const selectAll = document.getElementById('select-all-products');
    const checkboxes = document.querySelectorAll('.product-checkbox');
    
    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });
    
    updateSelectedProducts();
}

function toggleAllUsers() {
    const selectAll = document.getElementById('select-all-users');
    const checkboxes = document.querySelectorAll('.user-checkbox');
    
    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });
    
    updateSelectedUsers();
}

function updateSelectedProducts() {
    selectedProducts = Array.from(document.querySelectorAll('.product-checkbox:checked')).map(cb => cb.value);
    document.getElementById('selected-products-count').textContent = selectedProducts.length;
    document.getElementById('execute-product-action').disabled = selectedProducts.length === 0;
}

function updateSelectedUsers() {
    selectedUsers = Array.from(document.querySelectorAll('.user-checkbox:checked')).map(cb => cb.value);
    document.getElementById('selected-users-count').textContent = selectedUsers.length;
    document.getElementById('execute-user-action').disabled = selectedUsers.length === 0;
}

function showProductActionFields() {
    const action = document.getElementById('product-action').value;
    
    // Hide all action fields
    document.getElementById('category-fields').style.display = 'none';
    document.getElementById('price-fields').style.display = 'none';
    
    // Show relevant fields
    if (action === 'update_category') {
        document.getElementById('category-fields').style.display = 'block';
    } else if (action === 'update_prices') {
        document.getElementById('price-fields').style.display = 'block';
    }
}

function showUserActionFields() {
    const action = document.getElementById('user-action').value;
    
    // Hide all action fields
    document.getElementById('role-fields').style.display = 'none';
    document.getElementById('notification-fields').style.display = 'none';
    
    // Show relevant fields
    if (action === 'change_role') {
        document.getElementById('role-fields').style.display = 'block';
    } else if (action === 'send_notification') {
        document.getElementById('notification-fields').style.display = 'block';
    }
}

// Bulk operation functions
function bulkConfirmOrders() {
    showProgress('Confirming orders...', 100);
}

function bulkAssignDelivery() {
    showProgress('Assigning delivery personnel...', 100);
}

function exportOrders() {
    showProgress('Exporting order data...', 100);
}

function bulkStockAdjustment() {
    showProgress('Adjusting stock levels...', 100);
}

function sendLowStockAlerts() {
    showProgress('Sending low stock alerts...', 100);
}

function syncInventory() {
    showProgress('Synchronizing inventory...', 100);
}

function showProgress(text, duration) {
    document.getElementById('progress-text').textContent = text;
    const modal = new bootstrap.Modal(document.getElementById('progressModal'));
    modal.show();
    
    let progress = 0;
    const interval = setInterval(() => {
        progress += 10;
        document.getElementById('progress-bar').style.width = progress + '%';
        
        if (progress >= 100) {
            clearInterval(interval);
            setTimeout(() => {
                modal.hide();
                alert('Operation completed successfully!');
            }, 500);
        }
    }, duration);
}

// Form submissions
document.getElementById('product-bulk-form').addEventListener('submit', function(e) {
    e.preventDefault();
    
    if (selectedProducts.length === 0) {
        alert('Please select at least one product');
        return;
    }
    
    // Add selected product IDs to form
    selectedProducts.forEach(id => {
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = 'product_ids';
        input.value = id;
        this.appendChild(input);
    });
    
    showProgress('Processing products...', 200);
    
    // Submit form after progress
    setTimeout(() => {
        this.submit();
    }, 2000);
});

document.getElementById('user-bulk-form').addEventListener('submit', function(e) {
    e.preventDefault();
    
    if (selectedUsers.length === 0) {
        alert('Please select at least one user');
        return;
    }
    
    // Add selected user IDs to form
    selectedUsers.forEach(id => {
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = 'user_ids';
        input.value = id;
        this.appendChild(input);
    });
    
    showProgress('Processing users...', 200);
    
    // Submit form after progress
    setTimeout(() => {
        this.submit();
    }, 2000);
});

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    loadProducts();
});
</script>
{% endblock %}
