{% extends "base.html" %}

{% block title %}Add Promo Code - Admin - YalaOffice{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ url_for('admin.dashboard') }}">Admin Dashboard</a></li>
            <li class="breadcrumb-item"><a href="{{ url_for('admin.promo_codes') }}">Promo Codes</a></li>
            <li class="breadcrumb-item active">Add Promo Code</li>
        </ol>
    </nav>
    
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-plus"></i> Create New Promo Code</h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        {{ csrf_token() }}
                        
                        <!-- Basic Information -->
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="code" class="form-label">Promo Code *</label>
                                <input type="text" class="form-control text-uppercase" id="code" name="code" 
                                       required maxlength="20" placeholder="e.g., SAVE20">
                                <div class="form-text">Code will be automatically converted to uppercase</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="description" class="form-label">Description</label>
                                <input type="text" class="form-control" id="description" name="description" 
                                       placeholder="Brief description of the offer">
                            </div>
                        </div>
                        
                        <!-- Discount Configuration -->
                        <h6 class="mb-3 mt-4">Discount Configuration</h6>
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="discount_type" class="form-label">Discount Type *</label>
                                <select class="form-select" id="discount_type" name="discount_type" required>
                                    <option value="">Choose type...</option>
                                    <option value="percentage">Percentage (%)</option>
                                    <option value="fixed">Fixed Amount (Dh)</option>
                                </select>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="discount_value" class="form-label">Discount Value *</label>
                                <input type="number" class="form-control" id="discount_value" name="discount_value" 
                                       step="0.01" min="0" required>
                                <div class="form-text" id="discount-help">Enter the discount amount</div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="max_discount_amount" class="form-label">Max Discount (Dh)</label>
                                <input type="number" class="form-control" id="max_discount_amount" name="max_discount_amount" 
                                       step="0.01" min="0" placeholder="Optional">
                                <div class="form-text">Maximum discount amount (for percentage discounts)</div>
                            </div>
                        </div>
                        
                        <!-- Usage Restrictions -->
                        <h6 class="mb-3 mt-4">Usage Restrictions</h6>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="min_order_amount" class="form-label">Minimum Order Amount (Dh)</label>
                                <input type="number" class="form-control" id="min_order_amount" name="min_order_amount" 
                                       step="0.01" min="0" value="0">
                                <div class="form-text">Minimum order value to use this code</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="usage_limit" class="form-label">Usage Limit</label>
                                <input type="number" class="form-control" id="usage_limit" name="usage_limit" 
                                       min="1" placeholder="Unlimited">
                                <div class="form-text">Maximum number of times this code can be used</div>
                            </div>
                        </div>
                        
                        <!-- Validity Period -->
                        <h6 class="mb-3 mt-4">Validity Period</h6>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="valid_until" class="form-label">Valid Until</label>
                                <input type="date" class="form-control" id="valid_until" name="valid_until">
                                <div class="form-text">Leave empty for no expiry date</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="form-check mt-4">
                                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active" checked>
                                    <label class="form-check-label" for="is_active">
                                        Active (available for use)
                                    </label>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Preview -->
                        <div class="card bg-light mt-4">
                            <div class="card-body">
                                <h6 class="card-title">Preview</h6>
                                <div id="promo-preview">
                                    <p class="text-muted">Fill in the form to see a preview of your promo code</p>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Submit Buttons -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <hr>
                                <div class="d-flex justify-content-between">
                                    <a href="{{ url_for('admin.promo_codes') }}" class="btn btn-outline-secondary">
                                        <i class="fas fa-arrow-left"></i> Cancel
                                    </a>
                                    <button type="submit" class="btn btn-success">
                                        <i class="fas fa-save"></i> Create Promo Code
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const codeInput = document.getElementById('code');
    const discountTypeSelect = document.getElementById('discount_type');
    const discountValueInput = document.getElementById('discount_value');
    const maxDiscountInput = document.getElementById('max_discount_amount');
    const minOrderInput = document.getElementById('min_order_amount');
    const usageLimitInput = document.getElementById('usage_limit');
    const validUntilInput = document.getElementById('valid_until');
    const discountHelp = document.getElementById('discount-help');
    const previewDiv = document.getElementById('promo-preview');
    
    // Auto-uppercase code input
    codeInput.addEventListener('input', function() {
        this.value = this.value.toUpperCase().replace(/[^A-Z0-9]/g, '');
        updatePreview();
    });
    
    // Update help text based on discount type
    discountTypeSelect.addEventListener('change', function() {
        if (this.value === 'percentage') {
            discountHelp.textContent = 'Enter percentage (e.g., 20 for 20%)';
            discountValueInput.max = '100';
            maxDiscountInput.disabled = false;
        } else if (this.value === 'fixed') {
            discountHelp.textContent = 'Enter fixed amount in Dirhams';
            discountValueInput.removeAttribute('max');
            maxDiscountInput.disabled = true;
            maxDiscountInput.value = '';
        }
        updatePreview();
    });
    
    // Update preview on input changes
    [discountValueInput, maxDiscountInput, minOrderInput, usageLimitInput, validUntilInput].forEach(input => {
        input.addEventListener('input', updatePreview);
    });
    
    function updatePreview() {
        const code = codeInput.value;
        const discountType = discountTypeSelect.value;
        const discountValue = discountValueInput.value;
        const maxDiscount = maxDiscountInput.value;
        const minOrder = minOrderInput.value;
        const usageLimit = usageLimitInput.value;
        const validUntil = validUntilInput.value;
        
        if (!code || !discountType || !discountValue) {
            previewDiv.innerHTML = '<p class="text-muted">Fill in the required fields to see a preview</p>';
            return;
        }
        
        let discountText = '';
        if (discountType === 'percentage') {
            discountText = `${discountValue}% off`;
            if (maxDiscount) {
                discountText += ` (max ${maxDiscount} Dh)`;
            }
        } else {
            discountText = `${discountValue} Dh off`;
        }
        
        let conditionsText = '';
        if (minOrder && minOrder > 0) {
            conditionsText += `Minimum order: ${minOrder} Dh. `;
        }
        if (usageLimit) {
            conditionsText += `Limited to ${usageLimit} uses. `;
        }
        if (validUntil) {
            conditionsText += `Valid until ${validUntil}. `;
        }
        
        previewDiv.innerHTML = `
            <div class="border rounded p-3" style="background: linear-gradient(45deg, #007bff, #0056b3); color: white;">
                <h5 class="mb-2">🎉 ${code}</h5>
                <p class="mb-1"><strong>${discountText}</strong></p>
                ${conditionsText ? `<small>${conditionsText}</small>` : ''}
            </div>
        `;
    }
    
    // Set minimum date to today
    const today = new Date().toISOString().split('T')[0];
    validUntilInput.min = today;
    
    // Form validation
    const form = document.querySelector('form');
    form.addEventListener('submit', function(e) {
        const discountValue = parseFloat(discountValueInput.value);
        const discountType = discountTypeSelect.value;
        
        if (discountType === 'percentage' && discountValue > 100) {
            e.preventDefault();
            alert('Percentage discount cannot be more than 100%');
            return false;
        }
        
        if (discountValue <= 0) {
            e.preventDefault();
            alert('Discount value must be greater than 0');
            return false;
        }
    });
});
</script>
{% endblock %}
