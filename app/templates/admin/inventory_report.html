{% extends "base.html" %}

{% block title %}Inventory Report - YalaOffice Admin{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h2><i class="fas fa-boxes"></i> Inventory Report</h2>
            <p class="text-muted">
                Comprehensive inventory analysis and stock management overview
            </p>
        </div>
        <div class="col-md-4 text-md-end">
            <a href="{{ url_for('admin.reports') }}" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left"></i> Back to Reports
            </a>
        </div>
    </div>
    
    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-4">
                            <label for="branch_id" class="form-label">Branch</label>
                            <select class="form-select" id="branch_id" name="branch_id">
                                <option value="">All Branches</option>
                                {% for branch in branches %}
                                <option value="{{ branch.id }}" {{ 'selected' if report.selected_branch == branch.id }}>
                                    {{ branch.name }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="low_stock" class="form-label">Stock Filter</label>
                            <select class="form-select" id="low_stock" name="low_stock">
                                <option value="">All Items</option>
                                <option value="1" {{ 'selected' if report.low_stock_only }}>Low Stock Only</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-filter"></i> Apply Filters
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Summary Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-primary">
                <div class="card-body text-center">
                    <i class="fas fa-cube fa-2x text-primary mb-2"></i>
                    <h3 class="text-primary">{{ report.total_products }}</h3>
                    <p class="mb-0">Total Products</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-success">
                <div class="card-body text-center">
                    <i class="fas fa-money-bill-wave fa-2x text-success mb-2"></i>
                    <h3 class="text-success">{{ "%.2f"|format(report.total_value) }} Dh</h3>
                    <p class="mb-0">Total Inventory Value</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-warning">
                <div class="card-body text-center">
                    <i class="fas fa-exclamation-triangle fa-2x text-warning mb-2"></i>
                    <h3 class="text-warning">{{ report.low_stock_count }}</h3>
                    <p class="mb-0">Low Stock Items</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-danger">
                <div class="card-body text-center">
                    <i class="fas fa-ban fa-2x text-danger mb-2"></i>
                    <h3 class="text-danger">{{ report.out_of_stock_count }}</h3>
                    <p class="mb-0">Out of Stock</p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Stock Status Overview -->
    <div class="row mb-4">
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-chart-pie"></i> Stock Status Distribution</h5>
                </div>
                <div class="card-body">
                    <canvas id="stockStatusChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
        
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-chart-doughnut"></i> Inventory Value by Category</h5>
                </div>
                <div class="card-body">
                    <canvas id="categoryValueChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Critical Stock Alerts -->
    {% if report.low_stock_count > 0 or report.out_of_stock_count > 0 %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-warning">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0"><i class="fas fa-exclamation-triangle"></i> Critical Stock Alerts</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% if report.out_of_stock_count > 0 %}
                        <div class="col-md-6">
                            <div class="alert alert-danger">
                                <h6><i class="fas fa-ban"></i> Out of Stock Items ({{ report.out_of_stock_count }})</h6>
                                <p class="mb-0">Immediate attention required - customers cannot order these items.</p>
                                <button class="btn btn-sm btn-outline-danger mt-2" onclick="showOutOfStockItems()">
                                    View Details
                                </button>
                            </div>
                        </div>
                        {% endif %}
                        
                        {% if report.low_stock_count > 0 %}
                        <div class="col-md-6">
                            <div class="alert alert-warning">
                                <h6><i class="fas fa-exclamation-triangle"></i> Low Stock Items ({{ report.low_stock_count }})</h6>
                                <p class="mb-0">Consider reordering soon to avoid stockouts.</p>
                                <button class="btn btn-sm btn-outline-warning mt-2" onclick="showLowStockItems()">
                                    View Details
                                </button>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
    
    <!-- Detailed Inventory Table -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-table"></i> Detailed Inventory</h5>
                    <div class="btn-group">
                        <button class="btn btn-sm btn-outline-primary" onclick="toggleView('table')">
                            <i class="fas fa-table"></i> Table View
                        </button>
                        <button class="btn btn-sm btn-outline-primary" onclick="toggleView('cards')">
                            <i class="fas fa-th"></i> Card View
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Table View -->
                    <div id="tableView" class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Product</th>
                                    <th>SKU</th>
                                    <th>Category</th>
                                    <th>Branch</th>
                                    <th>Current Stock</th>
                                    <th>Min Level</th>
                                    <th>Unit Price</th>
                                    <th>Total Value</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in report.inventory_items %}
                                <tr class="{{ 'table-danger' if item.quantity == 0 else 'table-warning' if item.quantity <= item.min_stock_level else '' }}">
                                    <td>
                                        <strong>{{ item.title }}</strong>
                                    </td>
                                    <td><code>{{ item.sku }}</code></td>
                                    <td>{{ item.category }}</td>
                                    <td>{{ item.branch }}</td>
                                    <td>
                                        <span class="badge bg-{{ 'danger' if item.quantity == 0 else 'warning' if item.quantity <= item.min_stock_level else 'success' }}">
                                            {{ item.quantity }}
                                        </span>
                                    </td>
                                    <td>{{ item.min_stock_level }}</td>
                                    <td>{{ item.normal_price }} Dh</td>
                                    <td><strong>{{ "%.2f"|format(item.quantity * item.normal_price) }} Dh</strong></td>
                                    <td>
                                        {% if item.quantity == 0 %}
                                        <span class="badge bg-danger">Out of Stock</span>
                                        {% elif item.quantity <= item.min_stock_level %}
                                        <span class="badge bg-warning">Low Stock</span>
                                        {% else %}
                                        <span class="badge bg-success">In Stock</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-primary" onclick="adjustStock('{{ item.sku }}', '{{ item.branch }}')">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-outline-info" onclick="viewHistory('{{ item.sku }}', '{{ item.branch }}')">
                                                <i class="fas fa-history"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Card View (Hidden by default) -->
                    <div id="cardView" class="row" style="display: none;">
                        {% for item in report.inventory_items %}
                        <div class="col-lg-4 col-md-6 mb-3">
                            <div class="card border-{{ 'danger' if item.quantity == 0 else 'warning' if item.quantity <= item.min_stock_level else 'success' }}">
                                <div class="card-body">
                                    <h6 class="card-title">{{ item.title }}</h6>
                                    <p class="card-text">
                                        <small class="text-muted">{{ item.category }} | {{ item.branch }}</small><br>
                                        <strong>SKU:</strong> {{ item.sku }}<br>
                                        <strong>Stock:</strong> {{ item.quantity }} / {{ item.min_stock_level }}<br>
                                        <strong>Value:</strong> {{ "%.2f"|format(item.quantity * item.normal_price) }} Dh
                                    </p>
                                    <div class="d-flex justify-content-between align-items-center">
                                        {% if item.quantity == 0 %}
                                        <span class="badge bg-danger">Out of Stock</span>
                                        {% elif item.quantity <= item.min_stock_level %}
                                        <span class="badge bg-warning">Low Stock</span>
                                        {% else %}
                                        <span class="badge bg-success">In Stock</span>
                                        {% endif %}
                                        
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-primary" onclick="adjustStock('{{ item.sku }}', '{{ item.branch }}')">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-outline-info" onclick="viewHistory('{{ item.sku }}', '{{ item.branch }}')">
                                                <i class="fas fa-history"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Export Options -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-download"></i> Export Report</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-2">
                            <button class="btn btn-outline-danger w-100" onclick="exportReport('pdf')">
                                <i class="fas fa-file-pdf"></i> Export as PDF
                            </button>
                        </div>
                        <div class="col-md-3 mb-2">
                            <button class="btn btn-outline-success w-100" onclick="exportReport('excel')">
                                <i class="fas fa-file-excel"></i> Export as Excel
                            </button>
                        </div>
                        <div class="col-md-3 mb-2">
                            <button class="btn btn-outline-info w-100" onclick="exportReport('csv')">
                                <i class="fas fa-file-csv"></i> Export as CSV
                            </button>
                        </div>
                        <div class="col-md-3 mb-2">
                            <button class="btn btn-outline-primary w-100" onclick="window.print()">
                                <i class="fas fa-print"></i> Print Report
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Stock status chart
const stockStatusData = {
    labels: ['In Stock', 'Low Stock', 'Out of Stock'],
    datasets: [{
        data: [
            {{ report.total_products - report.low_stock_count - report.out_of_stock_count }},
            {{ report.low_stock_count }},
            {{ report.out_of_stock_count }}
        ],
        backgroundColor: [
            'rgba(40, 167, 69, 0.8)',
            'rgba(255, 193, 7, 0.8)',
            'rgba(220, 53, 69, 0.8)'
        ],
        borderColor: [
            'rgba(40, 167, 69, 1)',
            'rgba(255, 193, 7, 1)',
            'rgba(220, 53, 69, 1)'
        ],
        borderWidth: 1
    }]
};

new Chart(document.getElementById('stockStatusChart'), {
    type: 'pie',
    data: stockStatusData,
    options: {
        responsive: true,
        plugins: {
            title: {
                display: true,
                text: 'Stock Status Overview'
            }
        }
    }
});

function toggleView(viewType) {
    const tableView = document.getElementById('tableView');
    const cardView = document.getElementById('cardView');
    
    if (viewType === 'table') {
        tableView.style.display = 'block';
        cardView.style.display = 'none';
    } else {
        tableView.style.display = 'none';
        cardView.style.display = 'block';
    }
}

function adjustStock(sku, branch) {
    // Implement stock adjustment functionality
    alert(`Adjust stock for ${sku} at ${branch}`);
}

function viewHistory(sku, branch) {
    // Implement stock history viewing
    alert(`View history for ${sku} at ${branch}`);
}

function showOutOfStockItems() {
    // Filter table to show only out of stock items
    const rows = document.querySelectorAll('tbody tr');
    rows.forEach(row => {
        const stockBadge = row.querySelector('.badge');
        if (stockBadge && stockBadge.textContent.trim() === '0') {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
}

function showLowStockItems() {
    // Filter table to show only low stock items
    const rows = document.querySelectorAll('tbody tr');
    rows.forEach(row => {
        if (row.classList.contains('table-warning')) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
}

function exportReport(format) {
    const params = new URLSearchParams(window.location.search);
    params.set('format', format);
    
    const url = `{{ url_for('admin.inventory_report') }}?${params.toString()}`;
    window.open(url, '_blank');
}
</script>
{% endblock %}
