{% extends "base.html" %}

{% block title %}Add Product - Admin - YalaOffice{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ url_for('admin.dashboard') }}">Admin Dashboard</a></li>
            <li class="breadcrumb-item"><a href="{{ url_for('admin.products') }}">Products</a></li>
            <li class="breadcrumb-item active">Add Product</li>
        </ol>
    </nav>
    
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-plus"></i> Add New Product</h5>
                </div>
                <div class="card-body">
                    <form method="POST" enctype="multipart/form-data">
                        {{ csrf_token() }}
                        
                        <div class="row">
                            <!-- Basic Information -->
                            <div class="col-lg-8">
                                <h6 class="mb-3">Basic Information</h6>
                                
                                <div class="row">
                                    <div class="col-md-8 mb-3">
                                        <label for="title" class="form-label">Product Title *</label>
                                        <input type="text" class="form-control" id="title" name="title" required>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label for="sku" class="form-label">SKU</label>
                                        <input type="text" class="form-control" id="sku" name="sku" placeholder="Auto-generated if empty">
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="description" class="form-label">Description</label>
                                    <textarea class="form-control" id="description" name="description" rows="4"></textarea>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="brand" class="form-label">Brand</label>
                                        <input type="text" class="form-control" id="brand" name="brand">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="category_id" class="form-label">Category *</label>
                                        <select class="form-select" id="category_id" name="category_id" required>
                                            <option value="">Choose category...</option>
                                            {% for category in categories %}
                                            <option value="{{ category.id }}">{{ category.name }}</option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                </div>
                                
                                <!-- Pricing -->
                                <h6 class="mb-3 mt-4">Pricing</h6>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="normal_price" class="form-label">Normal Price (Dh) *</label>
                                        <input type="number" class="form-control" id="normal_price" name="normal_price" 
                                               step="0.01" min="0" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="reseller_price" class="form-label">Reseller Price (Dh) *</label>
                                        <input type="number" class="form-control" id="reseller_price" name="reseller_price" 
                                               step="0.01" min="0" required>
                                    </div>
                                </div>
                                
                                <!-- Images -->
                                <h6 class="mb-3 mt-4">Images</h6>
                                <div class="mb-3">
                                    <label for="main_image" class="form-label">Main Image</label>
                                    <input type="file" class="form-control" id="main_image" name="main_image" accept="image/*">
                                    <div class="form-text">Recommended size: 800x800px</div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="gallery_images" class="form-label">Gallery Images</label>
                                    <input type="file" class="form-control" id="gallery_images" name="gallery_images" 
                                           accept="image/*" multiple>
                                    <div class="form-text">You can select multiple images</div>
                                </div>
                            </div>
                            
                            <!-- Stock Information -->
                            <div class="col-lg-4">
                                <h6 class="mb-3">Initial Stock by Branch</h6>
                                
                                {% for branch in branches %}
                                <div class="card mb-3">
                                    <div class="card-body">
                                        <h6 class="card-title">{{ branch.name }}</h6>
                                        <p class="card-text small text-muted">{{ branch.address }}</p>
                                        
                                        <div class="mb-2">
                                            <label for="stock_{{ branch.id }}" class="form-label">Initial Quantity</label>
                                            <input type="number" class="form-control" id="stock_{{ branch.id }}" 
                                                   name="stock_{{ branch.id }}" value="0" min="0">
                                        </div>
                                    </div>
                                </div>
                                {% endfor %}
                                
                                <!-- Product Status -->
                                <div class="card">
                                    <div class="card-body">
                                        <h6 class="card-title">Product Status</h6>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="is_active" 
                                                   name="is_active" checked>
                                            <label class="form-check-label" for="is_active">
                                                Active (visible to customers)
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Submit Buttons -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <hr>
                                <div class="d-flex justify-content-between">
                                    <a href="{{ url_for('admin.products') }}" class="btn btn-outline-secondary">
                                        <i class="fas fa-arrow-left"></i> Cancel
                                    </a>
                                    <div>
                                        <button type="submit" class="btn btn-success">
                                            <i class="fas fa-save"></i> Add Product
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-generate SKU based on title
    const titleInput = document.getElementById('title');
    const skuInput = document.getElementById('sku');
    
    titleInput.addEventListener('input', function() {
        if (!skuInput.value) {
            const sku = this.value
                .toUpperCase()
                .replace(/[^A-Z0-9]/g, '')
                .substring(0, 10);
            skuInput.value = sku;
        }
    });
    
    // Auto-calculate reseller price (80% of normal price)
    const normalPriceInput = document.getElementById('normal_price');
    const resellerPriceInput = document.getElementById('reseller_price');
    
    normalPriceInput.addEventListener('input', function() {
        if (!resellerPriceInput.value || resellerPriceInput.value == 0) {
            const resellerPrice = (parseFloat(this.value) * 0.8).toFixed(2);
            resellerPriceInput.value = resellerPrice;
        }
    });
    
    // Form validation
    const form = document.querySelector('form');
    form.addEventListener('submit', function(e) {
        const normalPrice = parseFloat(normalPriceInput.value);
        const resellerPrice = parseFloat(resellerPriceInput.value);
        
        if (resellerPrice >= normalPrice) {
            e.preventDefault();
            alert('Reseller price must be less than normal price');
            return false;
        }
    });
});
</script>
{% endblock %}
