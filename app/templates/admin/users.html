{% extends "base.html" %}

{% block title %}User Management - YalaOffice Admin{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h2><i class="fas fa-users"></i> User Management</h2>
            <p class="text-muted">Manage system users and their roles</p>
        </div>
        <div class="col-md-4 text-md-end">
            <a href="{{ url_for('admin.add_user') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> Add New User
            </a>
        </div>
    </div>
    
    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-3">
                            <input type="text" class="form-control" name="search" 
                                   placeholder="Search users..." value="{{ request.args.get('search', '') }}">
                        </div>
                        <div class="col-md-3">
                            <select class="form-select" name="role">
                                <option value="">All Roles</option>
                                <option value="admin" {{ 'selected' if request.args.get('role') == 'admin' }}>Admin</option>
                                <option value="manager" {{ 'selected' if request.args.get('role') == 'manager' }}>Manager</option>
                                <option value="delivery" {{ 'selected' if request.args.get('role') == 'delivery' }}>Delivery</option>
                                <option value="client" {{ 'selected' if request.args.get('role') == 'client' }}>Client</option>
                                <option value="reseller" {{ 'selected' if request.args.get('role') == 'reseller' }}>Reseller</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <select class="form-select" name="status">
                                <option value="">All Status</option>
                                <option value="active" {{ 'selected' if request.args.get('status') == 'active' }}>Active</option>
                                <option value="inactive" {{ 'selected' if request.args.get('status') == 'inactive' }}>Inactive</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-search"></i> Filter
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Users Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Users ({{ users.total if users else 0 }})</h5>
                    <div class="btn-group">
                        <button class="btn btn-sm btn-outline-primary" onclick="selectAll()">
                            <i class="fas fa-check-square"></i> Select All
                        </button>
                        <button class="btn btn-sm btn-outline-warning" onclick="bulkAction()">
                            <i class="fas fa-cogs"></i> Bulk Actions
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    {% if users and users.items %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>
                                        <input type="checkbox" id="select-all" onchange="toggleAll()">
                                    </th>
                                    <th>User</th>
                                    <th>Email</th>
                                    <th>Role</th>
                                    <th>Status</th>
                                    <th>Last Login</th>
                                    <th>Created</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for user in users.items %}
                                <tr>
                                    <td>
                                        <input type="checkbox" class="user-checkbox" value="{{ user.id }}">
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-sm me-2">
                                                <div class="avatar-title bg-{{ 'success' if user.role == 'admin' else 'primary' if user.role == 'manager' else 'info' if user.role == 'delivery' else 'warning' if user.role == 'reseller' else 'secondary' }} rounded-circle">
                                                    {{ user.first_name[0] if user.first_name else user.username[0] }}
                                                </div>
                                            </div>
                                            <div>
                                                <h6 class="mb-0">{{ user.get_full_name() }}</h6>
                                                <small class="text-muted">@{{ user.username }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>{{ user.email }}</td>
                                    <td>
                                        <span class="badge bg-{{ 'success' if user.role == 'admin' else 'primary' if user.role == 'manager' else 'info' if user.role == 'delivery' else 'warning' if user.role == 'reseller' else 'secondary' }}">
                                            {{ user.role.title() }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-{{ 'success' if user.is_active else 'danger' }}">
                                            {{ 'Active' if user.is_active else 'Inactive' }}
                                        </span>
                                    </td>
                                    <td>
                                        {% if user.last_login %}
                                            {{ user.last_login.strftime('%m/%d/%Y') }}
                                        {% else %}
                                            <span class="text-muted">Never</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ user.created_at.strftime('%m/%d/%Y') if user.created_at }}</td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ url_for('admin.edit_user', id=user.id) }}" 
                                               class="btn btn-outline-primary" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            {% if user.id != current_user.id %}
                                            <button class="btn btn-outline-{{ 'danger' if user.is_active else 'success' }}" 
                                                    onclick="toggleUserStatus({{ user.id }}, {{ user.is_active|lower }})"
                                                    title="{{ 'Deactivate' if user.is_active else 'Activate' }}">
                                                <i class="fas fa-{{ 'ban' if user.is_active else 'check' }}"></i>
                                            </button>
                                            {% endif %}
                                            <button class="btn btn-outline-info" 
                                                    onclick="viewUserDetails({{ user.id }})" title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    {% if users.pages > 1 %}
                    <nav aria-label="Users pagination">
                        <ul class="pagination justify-content-center">
                            {% if users.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('admin.users', page=users.prev_num, **request.args) }}">Previous</a>
                            </li>
                            {% endif %}
                            
                            {% for page_num in users.iter_pages() %}
                                {% if page_num %}
                                    {% if page_num != users.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('admin.users', page=page_num, **request.args) }}">{{ page_num }}</a>
                                    </li>
                                    {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                    {% endif %}
                                {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if users.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('admin.users', page=users.next_num, **request.args) }}">Next</a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                    
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-users fa-3x text-muted mb-3"></i>
                        <h5>No users found</h5>
                        <p class="text-muted">No users match your current filters.</p>
                        <a href="{{ url_for('admin.add_user') }}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Add First User
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- User Details Modal -->
<div class="modal fade" id="userDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">User Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="userDetailsContent">
                <!-- Content will be loaded here -->
            </div>
        </div>
    </div>
</div>

<!-- Bulk Actions Modal -->
<div class="modal fade" id="bulkActionsModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Bulk Actions</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="bulkActionForm">
                    <div class="mb-3">
                        <label for="bulkAction" class="form-label">Select Action</label>
                        <select class="form-select" id="bulkAction" required>
                            <option value="">Choose action...</option>
                            <option value="activate">Activate Users</option>
                            <option value="deactivate">Deactivate Users</option>
                            <option value="change_role">Change Role</option>
                            <option value="send_notification">Send Notification</option>
                        </select>
                    </div>
                    
                    <div id="roleSelection" style="display: none;">
                        <div class="mb-3">
                            <label for="newRole" class="form-label">New Role</label>
                            <select class="form-select" id="newRole">
                                <option value="client">Client</option>
                                <option value="reseller">Reseller</option>
                                <option value="delivery">Delivery</option>
                                <option value="manager">Manager</option>
                            </select>
                        </div>
                    </div>
                    
                    <div id="notificationFields" style="display: none;">
                        <div class="mb-3">
                            <label for="notificationTitle" class="form-label">Notification Title</label>
                            <input type="text" class="form-control" id="notificationTitle">
                        </div>
                        <div class="mb-3">
                            <label for="notificationMessage" class="form-label">Message</label>
                            <textarea class="form-control" id="notificationMessage" rows="3"></textarea>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="executeBulkAction()">Execute</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.avatar-sm {
    width: 32px;
    height: 32px;
}

.avatar-title {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    color: white;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
function toggleAll() {
    const selectAll = document.getElementById('select-all');
    const checkboxes = document.querySelectorAll('.user-checkbox');
    
    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });
}

function selectAll() {
    const checkboxes = document.querySelectorAll('.user-checkbox');
    const allChecked = Array.from(checkboxes).every(cb => cb.checked);
    
    checkboxes.forEach(checkbox => {
        checkbox.checked = !allChecked;
    });
    
    document.getElementById('select-all').checked = !allChecked;
}

function bulkAction() {
    const selectedUsers = Array.from(document.querySelectorAll('.user-checkbox:checked'));
    
    if (selectedUsers.length === 0) {
        alert('Please select at least one user');
        return;
    }
    
    const modal = new bootstrap.Modal(document.getElementById('bulkActionsModal'));
    modal.show();
}

function toggleUserStatus(userId, isActive) {
    const action = isActive ? 'deactivate' : 'activate';
    const message = isActive ? 'deactivate' : 'activate';
    
    if (confirm(`Are you sure you want to ${message} this user?`)) {
        fetch(`/admin/users/${userId}/toggle-status`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error updating user status');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error updating user status');
        });
    }
}

function viewUserDetails(userId) {
    fetch(`/admin/users/${userId}/details`)
        .then(response => response.text())
        .then(html => {
            document.getElementById('userDetailsContent').innerHTML = html;
            const modal = new bootstrap.Modal(document.getElementById('userDetailsModal'));
            modal.show();
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error loading user details');
        });
}

// Bulk action form handling
document.getElementById('bulkAction').addEventListener('change', function() {
    const action = this.value;
    const roleSelection = document.getElementById('roleSelection');
    const notificationFields = document.getElementById('notificationFields');
    
    // Hide all conditional fields
    roleSelection.style.display = 'none';
    notificationFields.style.display = 'none';
    
    // Show relevant fields
    if (action === 'change_role') {
        roleSelection.style.display = 'block';
    } else if (action === 'send_notification') {
        notificationFields.style.display = 'block';
    }
});

function executeBulkAction() {
    const selectedUsers = Array.from(document.querySelectorAll('.user-checkbox:checked')).map(cb => cb.value);
    const action = document.getElementById('bulkAction').value;
    
    if (!action) {
        alert('Please select an action');
        return;
    }
    
    const data = {
        user_ids: selectedUsers,
        action: action
    };
    
    if (action === 'change_role') {
        data.new_role = document.getElementById('newRole').value;
    } else if (action === 'send_notification') {
        data.title = document.getElementById('notificationTitle').value;
        data.message = document.getElementById('notificationMessage').value;
        
        if (!data.title || !data.message) {
            alert('Please fill in notification title and message');
            return;
        }
    }
    
    fetch('/admin/bulk_update_users', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Error executing bulk action: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error executing bulk action');
    });
}
</script>
{% endblock %}
