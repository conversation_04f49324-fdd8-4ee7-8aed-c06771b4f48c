{% extends "base.html" %}

{% block title %}Branch Management - YalaOffice Admin{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h2><i class="fas fa-building"></i> Branch Management</h2>
            <p class="text-muted">Manage store branches and locations</p>
        </div>
        <div class="col-md-4 text-md-end">
            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addBranchModal">
                <i class="fas fa-plus"></i> Add New Branch
            </button>
        </div>
    </div>
    
    <!-- Branches Grid -->
    <div class="row">
        {% if branches %}
        {% for branch in branches %}
        <div class="col-lg-6 col-xl-4 mb-4">
            <div class="card h-100 border-{{ 'success' if branch.is_active else 'secondary' }}">
                <div class="card-header bg-{{ 'success' if branch.is_active else 'secondary' }} text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-map-marker-alt"></i> {{ branch.name }}
                        </h5>
                        <span class="badge bg-{{ 'light text-dark' if branch.is_active else 'dark' }}">
                            {{ 'Active' if branch.is_active else 'Inactive' }}
                        </span>
                    </div>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <h6><i class="fas fa-map-marker-alt text-primary"></i> Address</h6>
                        <p class="text-muted mb-0">{{ branch.address }}</p>
                        {% if branch.city %}
                        <small class="text-muted">{{ branch.city }}</small>
                        {% endif %}
                    </div>
                    
                    {% if branch.phone or branch.email %}
                    <div class="mb-3">
                        <h6><i class="fas fa-phone text-primary"></i> Contact</h6>
                        {% if branch.phone %}
                        <p class="mb-1">
                            <i class="fas fa-phone text-muted"></i> 
                            <a href="tel:{{ branch.phone }}">{{ branch.phone }}</a>
                        </p>
                        {% endif %}
                        {% if branch.email %}
                        <p class="mb-0">
                            <i class="fas fa-envelope text-muted"></i> 
                            <a href="mailto:{{ branch.email }}">{{ branch.email }}</a>
                        </p>
                        {% endif %}
                    </div>
                    {% endif %}
                    
                    {% if branch.manager %}
                    <div class="mb-3">
                        <h6><i class="fas fa-user-tie text-primary"></i> Manager</h6>
                        <p class="mb-0">{{ branch.manager.get_full_name() }}</p>
                        <small class="text-muted">{{ branch.manager.email }}</small>
                    </div>
                    {% endif %}
                    
                    <!-- Branch Statistics -->
                    <div class="row text-center">
                        <div class="col-4">
                            <h5 class="text-info">{{ branch.products|length if branch.products else 0 }}</h5>
                            <small class="text-muted">Products</small>
                        </div>
                        <div class="col-4">
                            <h5 class="text-success">{{ branch.orders|length if branch.orders else 0 }}</h5>
                            <small class="text-muted">Orders</small>
                        </div>
                        <div class="col-4">
                            <h5 class="text-warning">{{ branch.stock_items|length if branch.stock_items else 0 }}</h5>
                            <small class="text-muted">Stock Items</small>
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <div class="d-flex justify-content-between align-items-center">
                        <small class="text-muted">
                            Created: {{ branch.created_at.strftime('%m/%d/%Y') if branch.created_at }}
                        </small>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary" onclick="editBranch({{ branch.id }})" title="Edit">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-outline-info" onclick="viewBranchDetails({{ branch.id }})" title="Details">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-outline-{{ 'danger' if branch.is_active else 'success' }}" 
                                    onclick="toggleBranchStatus({{ branch.id }}, {{ branch.is_active|lower }})"
                                    title="{{ 'Deactivate' if branch.is_active else 'Activate' }}">
                                <i class="fas fa-{{ 'ban' if branch.is_active else 'check' }}"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
        {% else %}
        <div class="col-12">
            <div class="text-center py-5">
                <i class="fas fa-building fa-4x text-muted mb-4"></i>
                <h4>No Branches Found</h4>
                <p class="text-muted">Start by creating your first branch location.</p>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addBranchModal">
                    <i class="fas fa-plus"></i> Create First Branch
                </button>
            </div>
        </div>
        {% endif %}
    </div>
</div>

<!-- Add Branch Modal -->
<div class="modal fade" id="addBranchModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add New Branch</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ url_for('admin.add_branch') }}">
                <div class="modal-body">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">Branch Name *</label>
                            <input type="text" class="form-control" id="name" name="name" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="city" class="form-label">City</label>
                            <input type="text" class="form-control" id="city" name="city">
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="address" class="form-label">Address *</label>
                        <textarea class="form-control" id="address" name="address" rows="3" required></textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="phone" class="form-label">Phone</label>
                            <input type="tel" class="form-control" id="phone" name="phone">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">Email</label>
                            <input type="email" class="form-control" id="email" name="email">
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="manager_id" class="form-label">Branch Manager</label>
                        <select class="form-select" id="manager_id" name="manager_id">
                            <option value="">Select Manager (Optional)</option>
                            {% for manager in managers %}
                            <option value="{{ manager.id }}">{{ manager.get_full_name() }} ({{ manager.email }})</option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="is_active" name="is_active" checked>
                            <label class="form-check-label" for="is_active">
                                Active Branch
                            </label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Create Branch</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Branch Modal -->
<div class="modal fade" id="editBranchModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Branch</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="editBranchForm">
                <div class="modal-body">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                    <input type="hidden" id="edit_branch_id" name="branch_id">
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="edit_name" class="form-label">Branch Name *</label>
                            <input type="text" class="form-control" id="edit_name" name="name" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="edit_city" class="form-label">City</label>
                            <input type="text" class="form-control" id="edit_city" name="city">
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="edit_address" class="form-label">Address *</label>
                        <textarea class="form-control" id="edit_address" name="address" rows="3" required></textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="edit_phone" class="form-label">Phone</label>
                            <input type="tel" class="form-control" id="edit_phone" name="phone">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="edit_email" class="form-label">Email</label>
                            <input type="email" class="form-control" id="edit_email" name="email">
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="edit_manager_id" class="form-label">Branch Manager</label>
                        <select class="form-select" id="edit_manager_id" name="manager_id">
                            <option value="">Select Manager (Optional)</option>
                            {% for manager in managers %}
                            <option value="{{ manager.id }}">{{ manager.get_full_name() }} ({{ manager.email }})</option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="edit_is_active" name="is_active">
                            <label class="form-check-label" for="edit_is_active">
                                Active Branch
                            </label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Branch</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Branch Details Modal -->
<div class="modal fade" id="branchDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Branch Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="branchDetailsContent">
                <!-- Content will be loaded here -->
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function editBranch(branchId) {
    fetch(`/admin/branches/${branchId}/details`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const branch = data.branch;
                document.getElementById('edit_branch_id').value = branch.id;
                document.getElementById('edit_name').value = branch.name;
                document.getElementById('edit_city').value = branch.city || '';
                document.getElementById('edit_address').value = branch.address;
                document.getElementById('edit_phone').value = branch.phone || '';
                document.getElementById('edit_email').value = branch.email || '';
                document.getElementById('edit_manager_id').value = branch.manager_id || '';
                document.getElementById('edit_is_active').checked = branch.is_active;
                
                const modal = new bootstrap.Modal(document.getElementById('editBranchModal'));
                modal.show();
            } else {
                alert('Error loading branch details');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error loading branch details');
        });
}

function toggleBranchStatus(branchId, isActive) {
    const action = isActive ? 'deactivate' : 'activate';
    
    if (confirm(`Are you sure you want to ${action} this branch?`)) {
        fetch(`/admin/branches/${branchId}/toggle-status`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error updating branch status');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error updating branch status');
        });
    }
}

function viewBranchDetails(branchId) {
    fetch(`/admin/branches/${branchId}/full-details`)
        .then(response => response.text())
        .then(html => {
            document.getElementById('branchDetailsContent').innerHTML = html;
            const modal = new bootstrap.Modal(document.getElementById('branchDetailsModal'));
            modal.show();
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error loading branch details');
        });
}

// Edit branch form submission
document.getElementById('editBranchForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const branchId = formData.get('branch_id');
    
    fetch(`/admin/branches/${branchId}/edit`, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Error updating branch: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error updating branch');
    });
});
</script>
{% endblock %}
