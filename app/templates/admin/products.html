{% extends "base.html" %}

{% block title %}Products Management - Admin - YalaOffice{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h2><i class="fas fa-box"></i> Products Management</h2>
            <p class="text-muted">Manage your product catalog</p>
        </div>
        <div class="col-md-4 text-md-end">
            <a href="{{ url_for('admin.add_product') }}" class="btn btn-success">
                <i class="fas fa-plus"></i> Add New Product
            </a>
        </div>
    </div>
    
    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" class="row align-items-end">
                <div class="col-md-3 mb-2">
                    <label for="search" class="form-label">Search</label>
                    <input type="text" class="form-control" id="search" name="search" 
                           value="{{ request.args.get('search', '') }}" placeholder="Product name, SKU, brand...">
                </div>
                <div class="col-md-2 mb-2">
                    <label for="category" class="form-label">Category</label>
                    <select class="form-select" id="category" name="category">
                        <option value="">All Categories</option>
                        <!-- Categories will be populated -->
                    </select>
                </div>
                <div class="col-md-2 mb-2">
                    <label for="status" class="form-label">Status</label>
                    <select class="form-select" id="status" name="status">
                        <option value="">All Status</option>
                        <option value="active" {{ 'selected' if request.args.get('status') == 'active' }}>Active</option>
                        <option value="inactive" {{ 'selected' if request.args.get('status') == 'inactive' }}>Inactive</option>
                    </select>
                </div>
                <div class="col-md-2 mb-2">
                    <label for="stock" class="form-label">Stock</label>
                    <select class="form-select" id="stock" name="stock">
                        <option value="">All Stock</option>
                        <option value="low" {{ 'selected' if request.args.get('stock') == 'low' }}>Low Stock</option>
                        <option value="out" {{ 'selected' if request.args.get('stock') == 'out' }}>Out of Stock</option>
                    </select>
                </div>
                <div class="col-md-3 mb-2">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="fas fa-search"></i> Filter
                    </button>
                    <a href="{{ url_for('admin.products') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-times"></i> Clear
                    </a>
                </div>
            </form>
        </div>
    </div>
    
    <!-- Products Table -->
    <div class="card">
        <div class="card-body">
            {% if products.items %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Image</th>
                            <th>Product</th>
                            <th>Category</th>
                            <th>Pricing</th>
                            <th>Stock</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for product in products.items %}
                        <tr>
                            <td>
                                {% if product.main_image %}
                                <img src="{{ product.main_image }}" class="img-thumbnail" 
                                     style="width: 50px; height: 50px; object-fit: cover;" alt="{{ product.title }}">
                                {% else %}
                                <div class="bg-light d-flex align-items-center justify-content-center" 
                                     style="width: 50px; height: 50px;">
                                    <i class="fas fa-image text-muted"></i>
                                </div>
                                {% endif %}
                            </td>
                            <td>
                                <h6 class="mb-1">{{ product.title }}</h6>
                                <small class="text-muted">
                                    SKU: {{ product.sku or 'N/A' }}<br>
                                    Brand: {{ product.brand or 'No Brand' }}
                                </small>
                            </td>
                            <td>
                                <span class="badge bg-secondary">{{ product.category.name }}</span>
                            </td>
                            <td>
                                <div>
                                    <strong>{{ product.normal_price }} Dh</strong><br>
                                    <small class="text-success">Reseller: {{ product.reseller_price }} Dh</small>
                                </div>
                            </td>
                            <td>
                                {% set total_stock = product.get_total_stock() %}
                                <span class="badge {{ 'bg-success' if total_stock > 20 else 'bg-warning' if total_stock > 0 else 'bg-danger' }}">
                                    {{ total_stock }} units
                                </span>
                                <br>
                                <small class="text-muted">
                                    {% for stock in product.stock_items %}
                                        {% if stock.branch.is_active %}
                                        {{ stock.branch.name }}: {{ stock.quantity }}<br>
                                        {% endif %}
                                    {% endfor %}
                                </small>
                            </td>
                            <td>
                                {% if product.is_active %}
                                <span class="badge bg-success">Active</span>
                                {% else %}
                                <span class="badge bg-danger">Inactive</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ url_for('main.product_detail', id=product.id) }}" 
                                       class="btn btn-outline-info btn-sm" target="_blank">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ url_for('admin.edit_product', id=product.id) }}" 
                                       class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button class="btn btn-outline-danger btn-sm" 
                                            onclick="toggleProductStatus({{ product.id }}, {{ product.is_active|lower }})">
                                        {% if product.is_active %}
                                        <i class="fas fa-eye-slash"></i>
                                        {% else %}
                                        <i class="fas fa-eye"></i>
                                        {% endif %}
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            {% if products.pages > 1 %}
            <nav aria-label="Products pagination" class="mt-4">
                <ul class="pagination justify-content-center">
                    {% if products.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('admin.products', page=products.prev_num, **request.args) }}">
                            Previous
                        </a>
                    </li>
                    {% endif %}
                    
                    {% for page_num in products.iter_pages() %}
                        {% if page_num %}
                            {% if page_num != products.page %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('admin.products', page=page_num, **request.args) }}">
                                    {{ page_num }}
                                </a>
                            </li>
                            {% else %}
                            <li class="page-item active">
                                <span class="page-link">{{ page_num }}</span>
                            </li>
                            {% endif %}
                        {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">...</span>
                        </li>
                        {% endif %}
                    {% endfor %}
                    
                    {% if products.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('admin.products', page=products.next_num, **request.args) }}">
                            Next
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}
            
            {% else %}
            <div class="text-center py-5">
                <i class="fas fa-box-open fa-5x text-muted mb-4"></i>
                <h4>No products found</h4>
                <p class="text-muted">Start by adding your first product to the catalog.</p>
                <a href="{{ url_for('admin.add_product') }}" class="btn btn-success">
                    <i class="fas fa-plus"></i> Add First Product
                </a>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function toggleProductStatus(productId, currentStatus) {
    const action = currentStatus ? 'deactivate' : 'activate';
    const message = currentStatus ? 'deactivate' : 'activate';
    
    if (confirm(`Are you sure you want to ${message} this product?`)) {
        fetch(`/admin/product/${productId}/toggle`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert(data.message || 'Error updating product status');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error updating product status');
        });
    }
}

// Bulk actions
function selectAll() {
    const checkboxes = document.querySelectorAll('input[name="selected_products"]');
    const selectAllCheckbox = document.getElementById('select-all');
    
    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAllCheckbox.checked;
    });
}

function bulkAction() {
    const selectedProducts = document.querySelectorAll('input[name="selected_products"]:checked');
    const action = document.getElementById('bulk-action').value;
    
    if (selectedProducts.length === 0) {
        alert('Please select at least one product');
        return;
    }
    
    if (!action) {
        alert('Please select an action');
        return;
    }
    
    if (confirm(`Are you sure you want to ${action} ${selectedProducts.length} product(s)?`)) {
        const productIds = Array.from(selectedProducts).map(cb => cb.value);
        
        fetch('/admin/products/bulk', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: action,
                product_ids: productIds
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert(data.message || 'Error performing bulk action');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error performing bulk action');
        });
    }
}
</script>
{% endblock %}
