{% extends "base.html" %}

{% block title %}Forgot Password - YalaOffice{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-5">
            <div class="card shadow">
                <div class="card-header bg-primary text-white text-center">
                    <h4 class="mb-0">
                        <i class="fas fa-key"></i> Forgot Password
                    </h4>
                </div>
                <div class="card-body p-4">
                    <!-- Header Info -->
                    <div class="text-center mb-4">
                        <i class="fas fa-lock fa-3x text-muted mb-3"></i>
                        <h5>Reset Your Password</h5>
                        <p class="text-muted">
                            Enter your email address and we'll send you a link to reset your password.
                        </p>
                    </div>
                    
                    <!-- Form -->
                    <form method="POST">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                        
                        <div class="mb-3">
                            <label for="email" class="form-label">
                                <i class="fas fa-envelope"></i> Email Address
                            </label>
                            <input type="email" 
                                   class="form-control form-control-lg" 
                                   id="email" 
                                   name="email" 
                                   placeholder="Enter your email address"
                                   required>
                            <div class="form-text">
                                We'll send a password reset link to this email address.
                            </div>
                        </div>
                        
                        <div class="d-grid mb-3">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-paper-plane"></i> Send Reset Link
                            </button>
                        </div>
                    </form>
                    
                    <!-- Additional Info -->
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        <strong>Security Note:</strong> For your security, we'll only send the reset link if an account with that email exists.
                    </div>
                    
                    <!-- Back to Login -->
                    <div class="text-center">
                        <p class="mb-0">
                            Remember your password? 
                            <a href="{{ url_for('auth.login') }}" class="text-decoration-none">
                                <i class="fas fa-sign-in-alt"></i> Back to Login
                            </a>
                        </p>
                    </div>
                </div>
            </div>
            
            <!-- Help Section -->
            <div class="card mt-4">
                <div class="card-body">
                    <h6 class="card-title">
                        <i class="fas fa-question-circle text-info"></i> Need Help?
                    </h6>
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Common Issues:</h6>
                            <ul class="list-unstyled small">
                                <li><i class="fas fa-check text-success"></i> Check your spam/junk folder</li>
                                <li><i class="fas fa-check text-success"></i> Make sure you entered the correct email</li>
                                <li><i class="fas fa-check text-success"></i> Wait a few minutes for the email to arrive</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>Still Having Trouble?</h6>
                            <p class="small mb-2">
                                <i class="fas fa-envelope text-primary"></i> 
                                Email: <EMAIL>
                            </p>
                            <p class="small mb-2">
                                <i class="fas fa-phone text-primary"></i> 
                                Phone: +212 XXX XXX XXX
                            </p>
                            <p class="small mb-0">
                                <i class="fas fa-clock text-primary"></i> 
                                Support Hours: 9 AM - 6 PM (Morocco Time)
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Security Tips -->
            <div class="card mt-4 border-warning">
                <div class="card-header bg-warning text-dark">
                    <h6 class="mb-0">
                        <i class="fas fa-shield-alt"></i> Security Tips
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Password Best Practices:</h6>
                            <ul class="list-unstyled small">
                                <li><i class="fas fa-check text-success"></i> Use at least 8 characters</li>
                                <li><i class="fas fa-check text-success"></i> Include uppercase & lowercase letters</li>
                                <li><i class="fas fa-check text-success"></i> Add numbers and symbols</li>
                                <li><i class="fas fa-check text-success"></i> Avoid common words or patterns</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>Account Security:</h6>
                            <ul class="list-unstyled small">
                                <li><i class="fas fa-check text-success"></i> Never share your password</li>
                                <li><i class="fas fa-check text-success"></i> Use unique passwords for each account</li>
                                <li><i class="fas fa-check text-success"></i> Log out from shared computers</li>
                                <li><i class="fas fa-check text-success"></i> Update your password regularly</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');
    const submitBtn = form.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    
    form.addEventListener('submit', function() {
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Sending...';
        submitBtn.disabled = true;
        
        // Re-enable after 10 seconds in case of issues
        setTimeout(function() {
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }, 10000);
    });
    
    // Email validation
    const emailInput = document.getElementById('email');
    emailInput.addEventListener('input', function() {
        const email = this.value;
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        
        if (email && !emailRegex.test(email)) {
            this.setCustomValidity('Please enter a valid email address');
        } else {
            this.setCustomValidity('');
        }
    });
});
</script>
{% endblock %}
