{% extends "base.html" %}

{% block title %}Reseller Registration - YalaOffice{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <div class="card shadow-sm mt-5">
                <div class="card-body">
                    <div class="text-center mb-4">
                        <h3><i class="fas fa-handshake text-success"></i> Reseller Registration</h3>
                        <p class="text-muted">Join our reseller program and enjoy special pricing</p>
                    </div>
                    
                    <!-- Benefits Section -->
                    <div class="alert alert-success mb-4">
                        <h6><i class="fas fa-star"></i> Reseller Benefits:</h6>
                        <ul class="mb-0">
                            <li>Special wholesale pricing on all products</li>
                            <li>Bulk order discounts</li>
                            <li>Priority customer support</li>
                            <li>Exclusive product access</li>
                        </ul>
                    </div>
                    
                    <form method="POST">
                        {{ form.hidden_tag() }}
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                {{ form.first_name.label(class="form-label") }}
                                {{ form.first_name(class="form-control" + (" is-invalid" if form.first_name.errors else "")) }}
                                {% if form.first_name.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.first_name.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                {{ form.last_name.label(class="form-label") }}
                                {{ form.last_name(class="form-control" + (" is-invalid" if form.last_name.errors else "")) }}
                                {% if form.last_name.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.last_name.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            {{ form.username.label(class="form-label") }}
                            {{ form.username(class="form-control" + (" is-invalid" if form.username.errors else "")) }}
                            {% if form.username.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.username.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            {{ form.email.label(class="form-label") }}
                            {{ form.email(class="form-control" + (" is-invalid" if form.email.errors else "")) }}
                            {% if form.email.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.email.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            {{ form.phone.label(class="form-label") }}
                            {{ form.phone(class="form-control" + (" is-invalid" if form.phone.errors else "")) }}
                            {% if form.phone.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.phone.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text">Required for reseller verification</div>
                        </div>
                        
                        <div class="mb-3">
                            {{ form.address.label(class="form-label") }}
                            {{ form.address(class="form-control" + (" is-invalid" if form.address.errors else ""), rows="3") }}
                            {% if form.address.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.address.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text">Business address for verification</div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                {{ form.password.label(class="form-label") }}
                                {{ form.password(class="form-control" + (" is-invalid" if form.password.errors else "")) }}
                                {% if form.password.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.password.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                {{ form.password2.label(class="form-label") }}
                                {{ form.password2(class="form-control" + (" is-invalid" if form.password2.errors else "")) }}
                                {% if form.password2.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.password2.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <!-- Terms and Conditions -->
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="terms" required>
                            <label class="form-check-label" for="terms">
                                I agree to the <a href="#" data-bs-toggle="modal" data-bs-target="#termsModal">Terms and Conditions</a> 
                                and <a href="#" data-bs-toggle="modal" data-bs-target="#privacyModal">Privacy Policy</a>
                            </label>
                        </div>
                        
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="newsletter">
                            <label class="form-check-label" for="newsletter">
                                Subscribe to our newsletter for reseller updates and special offers
                            </label>
                        </div>
                        
                        <div class="d-grid">
                            {{ form.submit(class="btn btn-success btn-lg", value="Register as Reseller") }}
                        </div>
                    </form>
                    
                    <hr>
                    
                    <div class="text-center">
                        <p>Already have an account? <a href="{{ url_for('auth.login') }}">Sign in here</a></p>
                        <p>Want a regular account? <a href="{{ url_for('auth.register') }}">Register as Client</a></p>
                    </div>
                    
                    <!-- Additional Information -->
                    <div class="mt-4">
                        <h6>Reseller Program Information:</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <ul class="list-unstyled small">
                                    <li><i class="fas fa-check text-success me-1"></i> Minimum order quantities apply</li>
                                    <li><i class="fas fa-check text-success me-1"></i> Account verification required</li>
                                    <li><i class="fas fa-check text-success me-1"></i> Business license may be requested</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <ul class="list-unstyled small">
                                    <li><i class="fas fa-check text-success me-1"></i> Dedicated account manager</li>
                                    <li><i class="fas fa-check text-success me-1"></i> Flexible payment terms</li>
                                    <li><i class="fas fa-check text-success me-1"></i> Marketing support available</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Terms Modal -->
<div class="modal fade" id="termsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Terms and Conditions</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <h6>Reseller Agreement Terms</h6>
                <p>By registering as a reseller, you agree to the following terms:</p>
                <ul>
                    <li>Maintain accurate business information</li>
                    <li>Comply with minimum order requirements</li>
                    <li>Use products for legitimate business purposes</li>
                    <li>Maintain professional standards in customer service</li>
                    <li>Respect intellectual property rights</li>
                </ul>
                <p>Full terms and conditions are available upon account approval.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Privacy Modal -->
<div class="modal fade" id="privacyModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Privacy Policy</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <h6>Data Protection</h6>
                <p>We are committed to protecting your privacy and personal information:</p>
                <ul>
                    <li>Personal data is used only for account management and order processing</li>
                    <li>Information is not shared with third parties without consent</li>
                    <li>Data is stored securely and protected against unauthorized access</li>
                    <li>You have the right to access, modify, or delete your personal data</li>
                </ul>
                <p>For complete privacy policy details, please contact our support team.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}
