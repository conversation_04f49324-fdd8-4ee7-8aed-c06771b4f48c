{% extends "base.html" %}

{% block title %}Reset Password - YalaOffice{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-5">
            <div class="card shadow">
                <div class="card-header bg-success text-white text-center">
                    <h4 class="mb-0">
                        <i class="fas fa-lock"></i> Reset Password
                    </h4>
                </div>
                <div class="card-body p-4">
                    <!-- Header Info -->
                    <div class="text-center mb-4">
                        <i class="fas fa-key fa-3x text-success mb-3"></i>
                        <h5>Create New Password</h5>
                        <p class="text-muted">
                            Please enter your new password below. Make sure it's strong and secure.
                        </p>
                    </div>
                    
                    <!-- Form -->
                    <form method="POST" id="resetPasswordForm">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                        
                        <div class="mb-3">
                            <label for="password" class="form-label">
                                <i class="fas fa-lock"></i> New Password
                            </label>
                            <div class="input-group">
                                <input type="password" 
                                       class="form-control form-control-lg" 
                                       id="password" 
                                       name="password" 
                                       placeholder="Enter new password"
                                       minlength="6"
                                       required>
                                <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('password')">
                                    <i class="fas fa-eye" id="password-eye"></i>
                                </button>
                            </div>
                            <div class="form-text">
                                Password must be at least 6 characters long.
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="confirm_password" class="form-label">
                                <i class="fas fa-lock"></i> Confirm New Password
                            </label>
                            <div class="input-group">
                                <input type="password" 
                                       class="form-control form-control-lg" 
                                       id="confirm_password" 
                                       name="confirm_password" 
                                       placeholder="Confirm new password"
                                       minlength="6"
                                       required>
                                <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('confirm_password')">
                                    <i class="fas fa-eye" id="confirm_password-eye"></i>
                                </button>
                            </div>
                            <div class="invalid-feedback" id="password-mismatch">
                                Passwords do not match.
                            </div>
                        </div>
                        
                        <!-- Password Strength Indicator -->
                        <div class="mb-3">
                            <label class="form-label">Password Strength:</label>
                            <div class="progress" style="height: 8px;">
                                <div class="progress-bar" id="password-strength" role="progressbar" style="width: 0%"></div>
                            </div>
                            <small class="form-text" id="strength-text">Enter a password to see strength</small>
                        </div>
                        
                        <div class="d-grid mb-3">
                            <button type="submit" class="btn btn-success btn-lg" id="submitBtn">
                                <i class="fas fa-check"></i> Reset Password
                            </button>
                        </div>
                    </form>
                    
                    <!-- Security Info -->
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        <strong>Security:</strong> After resetting your password, you'll be automatically logged out from all devices for security.
                    </div>
                </div>
            </div>
            
            <!-- Password Requirements -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-shield-alt text-primary"></i> Password Requirements
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Required:</h6>
                            <ul class="list-unstyled small">
                                <li id="req-length"><i class="fas fa-times text-danger"></i> At least 6 characters</li>
                                <li id="req-upper"><i class="fas fa-times text-danger"></i> One uppercase letter</li>
                                <li id="req-lower"><i class="fas fa-times text-danger"></i> One lowercase letter</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>Recommended:</h6>
                            <ul class="list-unstyled small">
                                <li id="req-number"><i class="fas fa-times text-warning"></i> At least one number</li>
                                <li id="req-special"><i class="fas fa-times text-warning"></i> Special character (!@#$%)</li>
                                <li id="req-unique"><i class="fas fa-times text-warning"></i> Unique password</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Help Section -->
            <div class="card mt-4">
                <div class="card-body text-center">
                    <h6>Need Help?</h6>
                    <p class="small mb-2">
                        <i class="fas fa-envelope text-primary"></i> 
                        Email: <EMAIL>
                    </p>
                    <p class="small mb-0">
                        <i class="fas fa-phone text-primary"></i> 
                        Phone: +212 XXX XXX XXX
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const eye = document.getElementById(fieldId + '-eye');
    
    if (field.type === 'password') {
        field.type = 'text';
        eye.classList.remove('fa-eye');
        eye.classList.add('fa-eye-slash');
    } else {
        field.type = 'password';
        eye.classList.remove('fa-eye-slash');
        eye.classList.add('fa-eye');
    }
}

function checkPasswordStrength(password) {
    let strength = 0;
    let feedback = [];
    
    // Length check
    if (password.length >= 6) {
        strength += 20;
        document.getElementById('req-length').innerHTML = '<i class="fas fa-check text-success"></i> At least 6 characters';
    } else {
        document.getElementById('req-length').innerHTML = '<i class="fas fa-times text-danger"></i> At least 6 characters';
    }
    
    // Uppercase check
    if (/[A-Z]/.test(password)) {
        strength += 20;
        document.getElementById('req-upper').innerHTML = '<i class="fas fa-check text-success"></i> One uppercase letter';
    } else {
        document.getElementById('req-upper').innerHTML = '<i class="fas fa-times text-danger"></i> One uppercase letter';
    }
    
    // Lowercase check
    if (/[a-z]/.test(password)) {
        strength += 20;
        document.getElementById('req-lower').innerHTML = '<i class="fas fa-check text-success"></i> One lowercase letter';
    } else {
        document.getElementById('req-lower').innerHTML = '<i class="fas fa-times text-danger"></i> One lowercase letter';
    }
    
    // Number check
    if (/[0-9]/.test(password)) {
        strength += 20;
        document.getElementById('req-number').innerHTML = '<i class="fas fa-check text-success"></i> At least one number';
    } else {
        document.getElementById('req-number').innerHTML = '<i class="fas fa-times text-warning"></i> At least one number';
    }
    
    // Special character check
    if (/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
        strength += 20;
        document.getElementById('req-special').innerHTML = '<i class="fas fa-check text-success"></i> Special character (!@#$%)';
    } else {
        document.getElementById('req-special').innerHTML = '<i class="fas fa-times text-warning"></i> Special character (!@#$%)';
    }
    
    // Update progress bar
    const progressBar = document.getElementById('password-strength');
    const strengthText = document.getElementById('strength-text');
    
    progressBar.style.width = strength + '%';
    
    if (strength < 40) {
        progressBar.className = 'progress-bar bg-danger';
        strengthText.textContent = 'Weak';
    } else if (strength < 80) {
        progressBar.className = 'progress-bar bg-warning';
        strengthText.textContent = 'Medium';
    } else {
        progressBar.className = 'progress-bar bg-success';
        strengthText.textContent = 'Strong';
    }
    
    return strength;
}

function validatePasswords() {
    const password = document.getElementById('password').value;
    const confirmPassword = document.getElementById('confirm_password').value;
    const mismatchDiv = document.getElementById('password-mismatch');
    const confirmField = document.getElementById('confirm_password');
    
    if (password && confirmPassword && password !== confirmPassword) {
        confirmField.classList.add('is-invalid');
        mismatchDiv.style.display = 'block';
        return false;
    } else {
        confirmField.classList.remove('is-invalid');
        mismatchDiv.style.display = 'none';
        return true;
    }
}

document.addEventListener('DOMContentLoaded', function() {
    const passwordField = document.getElementById('password');
    const confirmPasswordField = document.getElementById('confirm_password');
    const form = document.getElementById('resetPasswordForm');
    const submitBtn = document.getElementById('submitBtn');
    
    // Password strength checking
    passwordField.addEventListener('input', function() {
        checkPasswordStrength(this.value);
        validatePasswords();
    });
    
    // Password confirmation checking
    confirmPasswordField.addEventListener('input', validatePasswords);
    
    // Form submission
    form.addEventListener('submit', function(e) {
        if (!validatePasswords()) {
            e.preventDefault();
            return false;
        }
        
        const password = passwordField.value;
        if (password.length < 6) {
            e.preventDefault();
            alert('Password must be at least 6 characters long.');
            return false;
        }
        
        // Show loading state
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Resetting...';
        submitBtn.disabled = true;
    });
});
</script>
{% endblock %}
