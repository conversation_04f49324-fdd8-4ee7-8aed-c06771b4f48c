<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Order Confirmation - YalaOffice</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background-color: #007bff;
            color: white;
            padding: 20px;
            text-align: center;
            border-radius: 8px 8px 0 0;
        }
        .content {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 0 0 8px 8px;
        }
        .order-details {
            background-color: white;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .item-row {
            display: flex;
            justify-content: space-between;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }
        .total-row {
            font-weight: bold;
            font-size: 1.1em;
            color: #007bff;
        }
        .button {
            display: inline-block;
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 5px;
            margin: 10px 0;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            color: #666;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🎉 Order Confirmation</h1>
        <p>Thank you for your order!</p>
    </div>
    
    <div class="content">
        <h2>Hello {{ customer.get_full_name() }},</h2>
        
        <p>We're excited to confirm that we've received your order and it's being processed. Here are the details:</p>
        
        <div class="order-details">
            <h3>Order Information</h3>
            <p><strong>Order Number:</strong> {{ order.order_number }}</p>
            <p><strong>Order Date:</strong> {{ order.created_at.strftime('%B %d, %Y at %I:%M %p') if order.created_at }}</p>
            <p><strong>Status:</strong> {{ order.status.title() }}</p>
            <p><strong>Payment Method:</strong> {{ order.payment_method.replace('_', ' ').title() }}</p>
        </div>
        
        <div class="order-details">
            <h3>Delivery Information</h3>
            <p><strong>Branch:</strong> {{ order.branch.name }}</p>
            <p><strong>Address:</strong> {{ order.delivery_address }}</p>
            <p><strong>Phone:</strong> {{ order.delivery_phone }}</p>
            {% if order.delivery_notes %}
            <p><strong>Notes:</strong> {{ order.delivery_notes }}</p>
            {% endif %}
        </div>
        
        <div class="order-details">
            <h3>Order Items</h3>
            {% for item in order.items %}
            <div class="item-row">
                <div>
                    <strong>{{ item.product.title }}</strong><br>
                    <small>Quantity: {{ item.quantity }} × {{ item.unit_price }} Dh</small>
                </div>
                <div>{{ item.total_price }} Dh</div>
            </div>
            {% endfor %}
            
            <div class="item-row">
                <div><strong>Subtotal:</strong></div>
                <div>{{ order.subtotal }} Dh</div>
            </div>
            
            {% if order.discount_amount > 0 %}
            <div class="item-row" style="color: #28a745;">
                <div><strong>Discount:</strong></div>
                <div>-{{ order.discount_amount }} Dh</div>
            </div>
            {% endif %}
            
            <div class="item-row total-row">
                <div><strong>Total:</strong></div>
                <div>{{ order.total_amount }} Dh</div>
            </div>
        </div>
        
        <div style="text-align: center;">
            <a href="#" class="button">Track Your Order</a>
        </div>
        
        <h3>What's Next?</h3>
        <ul>
            <li>We'll confirm your order and prepare it for delivery</li>
            <li>You'll receive updates via email as your order progresses</li>
            <li>Our delivery team will contact you before delivery</li>
            <li>Payment will be collected upon delivery (if cash on delivery)</li>
        </ul>
        
        <p>If you have any questions about your order, please don't hesitate to contact us:</p>
        <ul>
            <li>📞 Phone: +212 XXX XXX XXX</li>
            <li>📧 Email: <EMAIL></li>
            <li>🌐 Website: www.yalaoffice.ma</li>
        </ul>
    </div>
    
    <div class="footer">
        <p>Thank you for choosing YalaOffice!</p>
        <p>This is an automated email. Please do not reply to this message.</p>
        <p>&copy; 2024 YalaOffice. All rights reserved.</p>
    </div>
</body>
</html>
