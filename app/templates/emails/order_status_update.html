<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Order Status Update - YalaOffice</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background-color: #28a745;
            color: white;
            padding: 20px;
            text-align: center;
            border-radius: 8px 8px 0 0;
        }
        .content {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 0 0 8px 8px;
        }
        .status-update {
            background-color: white;
            padding: 20px;
            border-radius: 5px;
            margin: 15px 0;
            border-left: 4px solid #28a745;
        }
        .progress-bar {
            display: flex;
            justify-content: space-between;
            margin: 20px 0;
            position: relative;
        }
        .progress-step {
            flex: 1;
            text-align: center;
            position: relative;
        }
        .progress-step.active {
            color: #28a745;
            font-weight: bold;
        }
        .progress-step.completed {
            color: #6c757d;
        }
        .progress-icon {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background-color: #e9ecef;
            margin: 0 auto 5px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .progress-step.active .progress-icon {
            background-color: #28a745;
            color: white;
        }
        .progress-step.completed .progress-icon {
            background-color: #6c757d;
            color: white;
        }
        .button {
            display: inline-block;
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 5px;
            margin: 10px 0;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            color: #666;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>📦 Order Status Update</h1>
        <p>Your order {{ order.order_number }} has been updated</p>
    </div>
    
    <div class="content">
        <h2>Hello {{ customer.get_full_name() }},</h2>
        
        <div class="status-update">
            <h3>🎉 Great News!</h3>
            <p><strong>{{ status_message }}</strong></p>
            <p>Order Number: <strong>{{ order.order_number }}</strong></p>
            <p>Status: <strong>{{ new_status.replace('_', ' ').title() }}</strong></p>
        </div>
        
        <h3>Order Progress</h3>
        <div class="progress-bar">
            <div class="progress-step {{ 'completed' if order.status in ['confirmed', 'picked', 'out_for_delivery', 'delivered'] else 'active' if order.status == 'pending' else '' }}">
                <div class="progress-icon">📝</div>
                <div>Placed</div>
            </div>
            <div class="progress-step {{ 'completed' if order.status in ['picked', 'out_for_delivery', 'delivered'] else 'active' if order.status == 'confirmed' else '' }}">
                <div class="progress-icon">✅</div>
                <div>Confirmed</div>
            </div>
            <div class="progress-step {{ 'completed' if order.status in ['out_for_delivery', 'delivered'] else 'active' if order.status == 'picked' else '' }}">
                <div class="progress-icon">📦</div>
                <div>Picked</div>
            </div>
            <div class="progress-step {{ 'completed' if order.status == 'delivered' else 'active' if order.status == 'out_for_delivery' else '' }}">
                <div class="progress-icon">🚚</div>
                <div>Out for Delivery</div>
            </div>
            <div class="progress-step {{ 'active' if order.status == 'delivered' else '' }}">
                <div class="progress-icon">🏠</div>
                <div>Delivered</div>
            </div>
        </div>
        
        {% if new_status == 'confirmed' %}
        <div class="status-update">
            <h4>What's Next?</h4>
            <ul>
                <li>Your order is being prepared for delivery</li>
                <li>We'll notify you when it's picked and ready for delivery</li>
                <li>Our delivery team will contact you before delivery</li>
            </ul>
        </div>
        {% elif new_status == 'picked' %}
        <div class="status-update">
            <h4>What's Next?</h4>
            <ul>
                <li>Your order has been picked and is ready for delivery</li>
                <li>Our delivery person will be in touch soon</li>
                <li>Please ensure someone is available to receive the order</li>
            </ul>
        </div>
        {% elif new_status == 'out_for_delivery' %}
        <div class="status-update">
            <h4>Your Order is On Its Way! 🚚</h4>
            <ul>
                <li>Your order is currently out for delivery</li>
                <li>Expected delivery: Today</li>
                <li>Please be available at the delivery address</li>
                {% if order.delivery_person %}
                <li>Delivery person: {{ order.delivery_person.get_full_name() }}</li>
                {% endif %}
            </ul>
        </div>
        {% elif new_status == 'delivered' %}
        <div class="status-update">
            <h4>Order Delivered Successfully! 🎉</h4>
            <ul>
                <li>Your order has been delivered</li>
                <li>Thank you for choosing YalaOffice</li>
                <li>We hope you're satisfied with your purchase</li>
            </ul>
        </div>
        {% elif new_status == 'cancelled' %}
        <div class="status-update" style="border-left-color: #dc3545;">
            <h4>Order Cancelled</h4>
            <p>Your order has been cancelled. If you have any questions, please contact our support team.</p>
        </div>
        {% endif %}
        
        <div style="text-align: center;">
            <a href="#" class="button">View Order Details</a>
        </div>
        
        <h3>Delivery Information</h3>
        <p><strong>Address:</strong> {{ order.delivery_address }}</p>
        <p><strong>Phone:</strong> {{ order.delivery_phone }}</p>
        <p><strong>Branch:</strong> {{ order.branch.name }}</p>
        
        <p>If you have any questions about your order, please contact us:</p>
        <ul>
            <li>📞 Phone: +212 XXX XXX XXX</li>
            <li>📧 Email: <EMAIL></li>
        </ul>
    </div>
    
    <div class="footer">
        <p>Thank you for choosing YalaOffice!</p>
        <p>This is an automated email. Please do not reply to this message.</p>
        <p>&copy; 2024 YalaOffice. All rights reserved.</p>
    </div>
</body>
</html>
