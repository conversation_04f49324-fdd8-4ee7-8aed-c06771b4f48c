<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Low Stock Alert - YalaOffice</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background-color: #dc3545;
            color: white;
            padding: 20px;
            text-align: center;
            border-radius: 8px 8px 0 0;
        }
        .content {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 0 0 8px 8px;
        }
        .alert-box {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        .product-info {
            background-color: white;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
            border-left: 4px solid #dc3545;
        }
        .stock-level {
            font-size: 1.2em;
            font-weight: bold;
            color: #dc3545;
        }
        .button {
            display: inline-block;
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 5px;
            margin: 10px 0;
        }
        .urgent {
            background-color: #dc3545;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            color: #666;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>⚠️ Low Stock Alert</h1>
        <p>Immediate attention required</p>
    </div>
    
    <div class="content">
        <h2>Hello {{ manager.get_full_name() }},</h2>
        
        <div class="alert-box">
            <h3>🚨 Stock Level Warning</h3>
            <p>One of your products is running low on stock and requires immediate attention.</p>
        </div>
        
        <div class="product-info">
            <h3>Product Information</h3>
            <p><strong>Product:</strong> {{ product_stock.product.title }}</p>
            <p><strong>SKU:</strong> {{ product_stock.product.sku or 'N/A' }}</p>
            <p><strong>Brand:</strong> {{ product_stock.product.brand or 'No Brand' }}</p>
            <p><strong>Category:</strong> {{ product_stock.product.category.name }}</p>
            <p><strong>Branch:</strong> {{ product_stock.branch.name }}</p>
            
            <hr>
            
            <div class="stock-level">
                <p>Current Stock: {{ product_stock.quantity }} units</p>
                <p>Minimum Level: {{ product_stock.min_stock_level }} units</p>
            </div>
            
            {% if product_stock.quantity == 0 %}
            <div style="background-color: #f8d7da; color: #721c24; padding: 10px; border-radius: 5px; margin-top: 10px;">
                <strong>⚠️ OUT OF STOCK</strong> - This product is completely out of stock!
            </div>
            {% elif product_stock.quantity <= 5 %}
            <div style="background-color: #fff3cd; color: #856404; padding: 10px; border-radius: 5px; margin-top: 10px;">
                <strong>🔴 CRITICAL LEVEL</strong> - Only {{ product_stock.quantity }} units remaining!
            </div>
            {% else %}
            <div style="background-color: #d1ecf1; color: #0c5460; padding: 10px; border-radius: 5px; margin-top: 10px;">
                <strong>🟡 LOW STOCK</strong> - Stock is below minimum level
            </div>
            {% endif %}
        </div>
        
        <h3>Recommended Actions</h3>
        <ul>
            <li><strong>Immediate:</strong> Check current demand and sales velocity</li>
            <li><strong>Short-term:</strong> Contact suppliers for restocking</li>
            <li><strong>Consider:</strong> Adjusting minimum stock levels if needed</li>
            {% if product_stock.quantity == 0 %}
            <li><strong>Urgent:</strong> Mark product as out of stock on website</li>
            <li><strong>Customer Service:</strong> Notify customers with pending orders</li>
            {% endif %}
        </ul>
        
        <div style="text-align: center;">
            <a href="#" class="button">Manage Inventory</a>
            <a href="#" class="button urgent">Reorder Now</a>
        </div>
        
        <h3>Stock History (Last 30 Days)</h3>
        <p><em>Stock movement data would be displayed here in a full implementation</em></p>
        
        <div class="alert-box">
            <h4>💡 Pro Tip</h4>
            <p>Consider setting up automatic reorder points to prevent stockouts. You can also enable email notifications for when stock reaches certain thresholds.</p>
        </div>
        
        <h3>Need Help?</h3>
        <p>If you need assistance with inventory management or have questions about this alert:</p>
        <ul>
            <li>📞 Phone: +212 XXX XXX XXX</li>
            <li>📧 Email: <EMAIL></li>
            <li>💬 Internal Chat: Contact IT Support</li>
        </ul>
    </div>
    
    <div class="footer">
        <p>This is an automated stock alert from YalaOffice Inventory Management System</p>
        <p>Alert generated on {{ moment().format('MMMM Do YYYY, h:mm:ss a') }}</p>
        <p>&copy; 2024 YalaOffice. All rights reserved.</p>
    </div>
</body>
</html>
