{% extends "base.html" %}

{% block title %}Loyalty Program - YalaOffice{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h2><i class="fas fa-star"></i> Loyalty Program</h2>
            <p class="text-muted">Earn points with every purchase and unlock exclusive rewards</p>
        </div>
        <div class="col-md-4 text-md-end">
            <a href="{{ url_for('client.loyalty_history') }}" class="btn btn-outline-primary">
                <i class="fas fa-history"></i> View History
            </a>
        </div>
    </div>
    
    <!-- Loyalty Status Card -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-{{ 'warning' if loyalty_program.tier_level == 'Gold' else 'info' if loyalty_program.tier_level == 'Silver' else 'dark' if loyalty_program.tier_level == 'Platinum' else 'secondary' }}">
                <div class="card-header bg-{{ 'warning' if loyalty_program.tier_level == 'Gold' else 'info' if loyalty_program.tier_level == 'Silver' else 'dark' if loyalty_program.tier_level == 'Platinum' else 'secondary' }} text-white">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <h4 class="mb-0">
                                <i class="fas fa-crown"></i> {{ loyalty_program.tier_level }} Member
                            </h4>
                            <p class="mb-0">{{ current_user.get_full_name() }}</p>
                        </div>
                        <div class="col-md-6 text-md-end">
                            <h3 class="mb-0">{{ loyalty_program.points_balance }} Points</h3>
                            <small>Available Balance</small>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 text-center">
                            <h5>{{ loyalty_program.total_points_earned }}</h5>
                            <p class="text-muted mb-0">Total Earned</p>
                        </div>
                        <div class="col-md-4 text-center">
                            <h5>{{ loyalty_program.total_points_redeemed }}</h5>
                            <p class="text-muted mb-0">Total Redeemed</p>
                        </div>
                        <div class="col-md-4 text-center">
                            {% set benefits = loyalty_program.get_tier_benefits() %}
                            <h5>{{ benefits.discount }}%</h5>
                            <p class="text-muted mb-0">Tier Discount</p>
                        </div>
                    </div>
                    
                    {% if next_tier %}
                    <hr>
                    <div class="row">
                        <div class="col-12">
                            <h6>Progress to {{ next_tier }}</h6>
                            {% set progress_percentage = ((loyalty_program.total_points_earned - tier_requirements[loyalty_program.tier_level]) / (tier_requirements[next_tier] - tier_requirements[loyalty_program.tier_level]) * 100) %}
                            <div class="progress mb-2">
                                <div class="progress-bar" role="progressbar" style="width: {{ progress_percentage }}%">
                                    {{ "%.0f"|format(progress_percentage) }}%
                                </div>
                            </div>
                            <small class="text-muted">{{ points_to_next_tier }} more points needed for {{ next_tier }} tier</small>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    
    <!-- Tier Benefits -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-gift"></i> Your Tier Benefits</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 text-center">
                            <i class="fas fa-percentage fa-2x text-success mb-2"></i>
                            <h6>{{ benefits.discount }}% Discount</h6>
                            <p class="text-muted small">On all purchases</p>
                        </div>
                        <div class="col-md-3 text-center">
                            <i class="fas fa-coins fa-2x text-warning mb-2"></i>
                            <h6>{{ benefits.points_multiplier }}x Points</h6>
                            <p class="text-muted small">Points multiplier</p>
                        </div>
                        <div class="col-md-3 text-center">
                            <i class="fas fa-shipping-fast fa-2x {{ 'text-success' if benefits.free_shipping else 'text-muted' }} mb-2"></i>
                            <h6>{{ 'Free' if benefits.free_shipping else 'Standard' }} Shipping</h6>
                            <p class="text-muted small">Delivery benefit</p>
                        </div>
                        <div class="col-md-3 text-center">
                            <i class="fas fa-headset fa-2x text-info mb-2"></i>
                            <h6>Priority Support</h6>
                            <p class="text-muted small">{{ 'Premium' if loyalty_program.tier_level in ['Gold', 'Platinum'] else 'Standard' }} support</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Available Rewards -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-trophy"></i> Available Rewards</h5>
                </div>
                <div class="card-body">
                    {% if available_rewards %}
                    <div class="row">
                        {% for reward in available_rewards %}
                        <div class="col-lg-4 col-md-6 mb-3">
                            <div class="card border-{{ 'success' if reward.can_be_redeemed_by(loyalty_program) else 'secondary' }}">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                        <h6 class="card-title">{{ reward.title }}</h6>
                                        <span class="badge bg-primary">{{ reward.points_required }} pts</span>
                                    </div>
                                    
                                    <p class="card-text small text-muted">{{ reward.description }}</p>
                                    
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            {% if reward.reward_type == 'discount' %}
                                            <span class="badge bg-success">{{ reward.reward_value }}% Off</span>
                                            {% elif reward.reward_type == 'free_shipping' %}
                                            <span class="badge bg-info">Free Shipping</span>
                                            {% elif reward.reward_type == 'free_product' %}
                                            <span class="badge bg-warning">Free Product</span>
                                            {% endif %}
                                        </div>
                                        
                                        {% if reward.can_be_redeemed_by(loyalty_program) %}
                                        <form method="POST" action="{{ url_for('client.redeem_reward', reward_id=reward.id) }}" class="d-inline">
                                            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                                            <button type="submit" class="btn btn-sm btn-success" 
                                                    onclick="return confirm('Redeem this reward for {{ reward.points_required }} points?')">
                                                <i class="fas fa-gift"></i> Redeem
                                            </button>
                                        </form>
                                        {% else %}
                                        <button class="btn btn-sm btn-secondary" disabled>
                                            <i class="fas fa-lock"></i> 
                                            {% if loyalty_program.points_balance < reward.points_required %}
                                            Need {{ reward.points_required - loyalty_program.points_balance }} more
                                            {% else %}
                                            Unavailable
                                            {% endif %}
                                        </button>
                                        {% endif %}
                                    </div>
                                    
                                    {% if reward.valid_until %}
                                    <small class="text-muted">
                                        <i class="fas fa-clock"></i> 
                                        Expires: {{ reward.valid_until.strftime('%B %d, %Y') }}
                                    </small>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-gift fa-3x text-muted mb-3"></i>
                        <h5>No rewards available</h5>
                        <p class="text-muted">Check back later for exciting rewards!</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    
    <!-- How to Earn Points -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-question-circle"></i> How to Earn Points</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 text-center mb-3">
                            <i class="fas fa-shopping-cart fa-2x text-primary mb-2"></i>
                            <h6>Make Purchases</h6>
                            <p class="text-muted small">Earn {{ benefits.points_multiplier }} point(s) for every 1 Dh spent</p>
                        </div>
                        <div class="col-md-3 text-center mb-3">
                            <i class="fas fa-user-plus fa-2x text-success mb-2"></i>
                            <h6>Refer Friends</h6>
                            <p class="text-muted small">Get 500 points for each successful referral</p>
                        </div>
                        <div class="col-md-3 text-center mb-3">
                            <i class="fas fa-star fa-2x text-warning mb-2"></i>
                            <h6>Write Reviews</h6>
                            <p class="text-muted small">Earn 50 points for each product review</p>
                        </div>
                        <div class="col-md-3 text-center mb-3">
                            <i class="fas fa-birthday-cake fa-2x text-info mb-2"></i>
                            <h6>Birthday Bonus</h6>
                            <p class="text-muted small">Special birthday points every year</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Recent Transactions -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-history"></i> Recent Activity</h5>
                    <a href="{{ url_for('client.loyalty_history') }}" class="btn btn-sm btn-outline-primary">
                        View All
                    </a>
                </div>
                <div class="card-body">
                    {% if recent_transactions %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Activity</th>
                                    <th>Points</th>
                                    <th>Type</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for transaction in recent_transactions %}
                                <tr>
                                    <td>{{ transaction.created_at.strftime('%m/%d/%Y') if transaction.created_at }}</td>
                                    <td>{{ transaction.reason }}</td>
                                    <td>
                                        <span class="badge bg-{{ 'success' if transaction.transaction_type == 'earned' else 'danger' }}">
                                            {{ '+' if transaction.transaction_type == 'earned' else '' }}{{ transaction.points }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-{{ 'primary' if transaction.transaction_type == 'earned' else 'warning' }}">
                                            {{ transaction.transaction_type.title() }}
                                        </span>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-3">
                        <i class="fas fa-history fa-2x text-muted mb-2"></i>
                        <p class="text-muted">No activity yet. Start shopping to earn points!</p>
                        <a href="{{ url_for('main.products') }}" class="btn btn-primary">
                            <i class="fas fa-shopping-cart"></i> Start Shopping
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Auto-refresh loyalty data every 30 seconds
setInterval(function() {
    // Could implement AJAX refresh of points balance here
}, 30000);

// Animate progress bars
document.addEventListener('DOMContentLoaded', function() {
    const progressBars = document.querySelectorAll('.progress-bar');
    progressBars.forEach(bar => {
        const width = bar.style.width;
        bar.style.width = '0%';
        setTimeout(() => {
            bar.style.transition = 'width 1s ease-in-out';
            bar.style.width = width;
        }, 100);
    });
});
</script>
{% endblock %}
