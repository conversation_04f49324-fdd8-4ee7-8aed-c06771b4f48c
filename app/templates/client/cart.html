{% extends "base.html" %}

{% block title %}Shopping Cart - YalaOffice{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ url_for('main.index') }}">Home</a></li>
            <li class="breadcrumb-item active">Shopping Cart</li>
        </ol>
    </nav>
    
    <h2 class="mb-4"><i class="fas fa-shopping-cart"></i> Shopping Cart</h2>
    
    {% if cart_products %}
    <div class="row">
        <!-- Cart Items -->
        <div class="col-lg-8 mb-4">
            <div class="card">
                <div class="card-body">
                    {% for item in cart_products %}
                    <div class="row align-items-center cart-item mb-3 pb-3 border-bottom" data-product-id="{{ item.product.id }}">
                        <div class="col-md-2">
                            {% if item.product.main_image %}
                            <img src="{{ item.product.main_image }}" class="img-fluid rounded" alt="{{ item.product.title }}">
                            {% else %}
                            <div class="bg-light d-flex align-items-center justify-content-center rounded" style="height: 80px;">
                                <i class="fas fa-image text-muted"></i>
                            </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-4">
                            <h6 class="mb-1">{{ item.product.title }}</h6>
                            <p class="text-muted small mb-1">{{ item.product.brand or 'No Brand' }}</p>
                            <p class="text-muted small mb-0">SKU: {{ item.product.sku or 'N/A' }}</p>
                        </div>
                        
                        <div class="col-md-2">
                            <span class="fw-bold">{{ item.price }} Dh</span>
                            {% if current_user.is_reseller() and item.product.normal_price != item.price %}
                            <br><small class="text-muted text-decoration-line-through">{{ item.product.normal_price }} Dh</small>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-2">
                            <div class="input-group input-group-sm">
                                <button class="btn btn-outline-secondary quantity-btn" type="button" data-action="decrease">-</button>
                                <input type="number" class="form-control text-center quantity-input" 
                                       value="{{ item.quantity }}" min="1" max="99">
                                <button class="btn btn-outline-secondary quantity-btn" type="button" data-action="increase">+</button>
                            </div>
                        </div>
                        
                        <div class="col-md-1">
                            <span class="fw-bold item-total">{{ item.subtotal }} Dh</span>
                        </div>
                        
                        <div class="col-md-1">
                            <button class="btn btn-outline-danger btn-sm remove-item" data-product-id="{{ item.product.id }}">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
        
        <!-- Cart Summary -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-calculator"></i> Order Summary</h5>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between mb-3">
                        <span>Items ({{ cart_products|length }}):</span>
                        <span id="cart-total">{{ total }} Dh</span>
                    </div>
                    
                    <div class="d-flex justify-content-between mb-3">
                        <span>Delivery:</span>
                        <span class="text-success">Free</span>
                    </div>
                    
                    <hr>
                    
                    <div class="d-flex justify-content-between mb-4">
                        <strong>Total:</strong>
                        <strong id="final-total">{{ total }} Dh</strong>
                    </div>
                    
                    <div class="d-grid gap-2">
                        <a href="{{ url_for('client.checkout') }}" class="btn btn-success btn-lg">
                            <i class="fas fa-credit-card"></i> Proceed to Checkout
                        </a>
                        <a href="{{ url_for('main.products') }}" class="btn btn-outline-primary">
                            <i class="fas fa-shopping-bag"></i> Continue Shopping
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Delivery Information -->
            <div class="card mt-3">
                <div class="card-body">
                    <h6><i class="fas fa-truck text-primary"></i> Delivery Information</h6>
                    <ul class="list-unstyled small mb-0">
                        <li><i class="fas fa-check text-success"></i> Free delivery to all branches</li>
                        <li><i class="fas fa-check text-success"></i> Delivery within 2-3 business days</li>
                        <li><i class="fas fa-check text-success"></i> Real-time order tracking</li>
                        <li><i class="fas fa-check text-success"></i> Multiple payment options</li>
                    </ul>
                </div>
            </div>
            
            <!-- Available Branches -->
            <div class="card mt-3">
                <div class="card-body">
                    <h6><i class="fas fa-map-marker-alt text-info"></i> Delivery Branches</h6>
                    {% for branch in branches %}
                    <div class="small mb-1">
                        <strong>{{ branch.name }}</strong><br>
                        <span class="text-muted">{{ branch.address }}</span>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
    
    {% else %}
    <!-- Empty Cart -->
    <div class="text-center py-5">
        <i class="fas fa-shopping-cart fa-5x text-muted mb-4"></i>
        <h3>Your cart is empty</h3>
        <p class="text-muted mb-4">Looks like you haven't added any items to your cart yet.</p>
        <a href="{{ url_for('main.products') }}" class="btn btn-primary btn-lg">
            <i class="fas fa-shopping-bag"></i> Start Shopping
        </a>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Quantity update handlers
    document.querySelectorAll('.quantity-btn').forEach(button => {
        button.addEventListener('click', function() {
            const action = this.dataset.action;
            const cartItem = this.closest('.cart-item');
            const productId = cartItem.dataset.productId;
            const quantityInput = cartItem.querySelector('.quantity-input');
            let quantity = parseInt(quantityInput.value);
            
            if (action === 'increase') {
                quantity++;
            } else if (action === 'decrease' && quantity > 1) {
                quantity--;
            }
            
            quantityInput.value = quantity;
            updateCartItem(productId, quantity);
        });
    });
    
    // Direct quantity input change
    document.querySelectorAll('.quantity-input').forEach(input => {
        input.addEventListener('change', function() {
            const cartItem = this.closest('.cart-item');
            const productId = cartItem.dataset.productId;
            const quantity = parseInt(this.value);
            
            if (quantity > 0) {
                updateCartItem(productId, quantity);
            }
        });
    });
    
    // Remove item handlers
    document.querySelectorAll('.remove-item').forEach(button => {
        button.addEventListener('click', function() {
            const productId = this.dataset.productId;
            if (confirm('Are you sure you want to remove this item from your cart?')) {
                removeCartItem(productId);
            }
        });
    });
    
    function updateCartItem(productId, quantity) {
        fetch(`/client/update_cart/${productId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ quantity: quantity })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Reload page to update totals
                location.reload();
            } else {
                alert(data.message || 'Error updating cart');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error updating cart');
        });
    }
    
    function removeCartItem(productId) {
        fetch(`/client/remove_from_cart/${productId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Remove item from DOM or reload page
                location.reload();
            } else {
                alert(data.message || 'Error removing item');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error removing item');
        });
    }
});
</script>
{% endblock %}
