{% extends "base.html" %}

{% block title %}My Wishlist - YalaOffice{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h2><i class="fas fa-heart text-danger"></i> My Wishlist</h2>
            <p class="text-muted">Save your favorite products for later</p>
        </div>
        <div class="col-md-4 text-md-end">
            <a href="{{ url_for('main.products') }}" class="btn btn-outline-primary">
                <i class="fas fa-shopping-bag"></i> Continue Shopping
            </a>
        </div>
    </div>
    
    {% if wishlist_items.items %}
    <!-- Wishlist Items -->
    <div class="row">
        {% for item in wishlist_items.items %}
        <div class="col-lg-4 col-md-6 mb-4" id="wishlist-item-{{ item.product.id }}">
            <div class="card h-100 card-hover">
                <div class="position-relative">
                    {% if item.product.main_image %}
                    <img src="{{ item.product.main_image }}" class="card-img-top" 
                         style="height: 200px; object-fit: cover;" alt="{{ item.product.title }}">
                    {% else %}
                    <div class="card-img-top bg-light d-flex align-items-center justify-content-center" 
                         style="height: 200px;">
                        <i class="fas fa-image fa-3x text-muted"></i>
                    </div>
                    {% endif %}
                    
                    <!-- Remove from wishlist button -->
                    <button class="btn btn-sm btn-outline-danger position-absolute top-0 end-0 m-2" 
                            onclick="removeFromWishlist({{ item.product.id }})" 
                            title="Remove from wishlist">
                        <i class="fas fa-times"></i>
                    </button>
                    
                    <!-- Stock status badge -->
                    {% set total_stock = item.product.get_total_stock() %}
                    {% if total_stock == 0 %}
                    <span class="badge bg-danger position-absolute top-0 start-0 m-2">Out of Stock</span>
                    {% elif total_stock <= 10 %}
                    <span class="badge bg-warning position-absolute top-0 start-0 m-2">Low Stock</span>
                    {% endif %}
                </div>
                
                <div class="card-body d-flex flex-column">
                    <h5 class="card-title">{{ item.product.title }}</h5>
                    <p class="card-text text-muted small">{{ item.product.description[:100] }}{% if item.product.description|length > 100 %}...{% endif %}</p>
                    
                    <div class="mt-auto">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div>
                                {% if current_user.role == 'reseller' %}
                                <span class="price-tag">{{ item.product.reseller_price }} Dh</span>
                                <br><small class="text-muted">Normal: {{ item.product.normal_price }} Dh</small>
                                {% else %}
                                <span class="price-tag">{{ item.product.normal_price }} Dh</span>
                                {% endif %}
                            </div>
                            <small class="text-muted">
                                Added {{ item.created_at.strftime('%m/%d/%Y') if item.created_at }}
                            </small>
                        </div>
                        
                        <div class="d-grid gap-2">
                            {% if total_stock > 0 %}
                            <button class="btn btn-primary" onclick="addToCart({{ item.product.id }})">
                                <i class="fas fa-shopping-cart"></i> Add to Cart
                            </button>
                            {% else %}
                            <button class="btn btn-secondary" disabled>
                                <i class="fas fa-ban"></i> Out of Stock
                            </button>
                            {% endif %}
                            
                            <a href="{{ url_for('main.product_detail', id=item.product.id) }}" 
                               class="btn btn-outline-info">
                                <i class="fas fa-eye"></i> View Details
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
    
    <!-- Pagination -->
    {% if wishlist_items.pages > 1 %}
    <nav aria-label="Wishlist pagination" class="mt-4">
        <ul class="pagination justify-content-center">
            {% if wishlist_items.has_prev %}
            <li class="page-item">
                <a class="page-link" href="{{ url_for('client.wishlist', page=wishlist_items.prev_num) }}">
                    Previous
                </a>
            </li>
            {% endif %}
            
            {% for page_num in wishlist_items.iter_pages() %}
                {% if page_num %}
                    {% if page_num != wishlist_items.page %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('client.wishlist', page=page_num) }}">
                            {{ page_num }}
                        </a>
                    </li>
                    {% else %}
                    <li class="page-item active">
                        <span class="page-link">{{ page_num }}</span>
                    </li>
                    {% endif %}
                {% else %}
                <li class="page-item disabled">
                    <span class="page-link">...</span>
                </li>
                {% endif %}
            {% endfor %}
            
            {% if wishlist_items.has_next %}
            <li class="page-item">
                <a class="page-link" href="{{ url_for('client.wishlist', page=wishlist_items.next_num) }}">
                    Next
                </a>
            </li>
            {% endif %}
        </ul>
    </nav>
    {% endif %}
    
    <!-- Bulk Actions -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Wishlist Actions</h5>
                    <div class="row">
                        <div class="col-md-6">
                            <button class="btn btn-success w-100" onclick="addAllToCart()">
                                <i class="fas fa-shopping-cart"></i> Add All Available to Cart
                            </button>
                        </div>
                        <div class="col-md-6">
                            <button class="btn btn-outline-danger w-100" onclick="clearWishlist()">
                                <i class="fas fa-trash"></i> Clear Wishlist
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    {% else %}
    <!-- Empty Wishlist -->
    <div class="text-center py-5">
        <i class="fas fa-heart fa-5x text-muted mb-4"></i>
        <h3>Your wishlist is empty</h3>
        <p class="text-muted mb-4">Save products you love for later by clicking the heart icon</p>
        <a href="{{ url_for('main.products') }}" class="btn btn-primary">
            <i class="fas fa-shopping-bag"></i> Start Shopping
        </a>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
function removeFromWishlist(productId) {
    if (confirm('Remove this product from your wishlist?')) {
        fetch(`/client/wishlist/remove/${productId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Remove the item from the page
                const item = document.getElementById(`wishlist-item-${productId}`);
                if (item) {
                    item.remove();
                }
                
                // Check if wishlist is now empty
                const remainingItems = document.querySelectorAll('[id^="wishlist-item-"]');
                if (remainingItems.length === 0) {
                    location.reload(); // Reload to show empty state
                }
                
                alert('Product removed from wishlist');
            } else {
                alert(data.message || 'Error removing product from wishlist');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error removing product from wishlist');
        });
    }
}

function addToCart(productId) {
    // Get available branches for the product
    fetch(`/api/product_stock/${productId}`)
        .then(response => response.json())
        .then(data => {
            if (data.branches && data.branches.length > 0) {
                // If only one branch, add directly
                if (data.branches.length === 1) {
                    addToCartWithBranch(productId, data.branches[0].id);
                } else {
                    // Show branch selection modal
                    showBranchSelectionModal(productId, data.branches);
                }
            } else {
                alert('Product is out of stock in all branches');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error checking product availability');
        });
}

function addToCartWithBranch(productId, branchId) {
    fetch('/client/add_to_cart', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            product_id: productId,
            branch_id: branchId,
            quantity: 1
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Product added to cart!');
            updateCartCount();
        } else {
            alert(data.message || 'Error adding product to cart');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error adding product to cart');
    });
}

function addAllToCart() {
    if (confirm('Add all available products to cart?')) {
        const productIds = [];
        document.querySelectorAll('[id^="wishlist-item-"]').forEach(item => {
            const productId = item.id.replace('wishlist-item-', '');
            productIds.push(productId);
        });
        
        // Add each product to cart
        let addedCount = 0;
        productIds.forEach(productId => {
            addToCart(productId);
            addedCount++;
        });
        
        if (addedCount > 0) {
            setTimeout(() => {
                alert(`${addedCount} products added to cart!`);
            }, 1000);
        }
    }
}

function clearWishlist() {
    if (confirm('Are you sure you want to clear your entire wishlist? This action cannot be undone.')) {
        const productIds = [];
        document.querySelectorAll('[id^="wishlist-item-"]').forEach(item => {
            const productId = item.id.replace('wishlist-item-', '');
            productIds.push(productId);
        });
        
        // Remove each product from wishlist
        productIds.forEach(productId => {
            fetch(`/client/wishlist/remove/${productId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            });
        });
        
        // Reload page after a short delay
        setTimeout(() => {
            location.reload();
        }, 1000);
    }
}

function updateCartCount() {
    // This would update the cart count in the navigation
    // Implementation depends on your cart count display logic
}
</script>
{% endblock %}
