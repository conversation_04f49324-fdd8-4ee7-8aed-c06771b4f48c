{% extends "base.html" %}

{% block title %}Checkout - YalaOffice{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ url_for('main.index') }}">Home</a></li>
            <li class="breadcrumb-item"><a href="{{ url_for('client.cart') }}">Cart</a></li>
            <li class="breadcrumb-item active">Checkout</li>
        </ol>
    </nav>
    
    <h2 class="mb-4"><i class="fas fa-credit-card"></i> Checkout</h2>
    
    <div class="row">
        <!-- Order Summary -->
        <div class="col-lg-4 order-lg-2 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-shopping-cart"></i> Order Summary</h5>
                </div>
                <div class="card-body">
                    {% for item in cart_products %}
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div class="flex-grow-1">
                            <h6 class="mb-1">{{ item.product.title }}</h6>
                            <small class="text-muted">{{ item.quantity }} x {{ item.price }} Dh</small>
                        </div>
                        <span class="fw-bold">{{ item.subtotal }} Dh</span>
                    </div>
                    {% endfor %}
                    
                    <hr>
                    
                    <div class="d-flex justify-content-between mb-2">
                        <span>Subtotal:</span>
                        <span id="subtotal">{{ subtotal }} Dh</span>
                    </div>
                    
                    <div class="d-flex justify-content-between mb-2" id="discount-row" style="display: none;">
                        <span class="text-success">Discount:</span>
                        <span class="text-success" id="discount-amount">0 Dh</span>
                    </div>
                    
                    <hr>
                    
                    <div class="d-flex justify-content-between">
                        <strong>Total:</strong>
                        <strong id="total-amount">{{ subtotal }} Dh</strong>
                    </div>
                </div>
            </div>
            
            <!-- Promo Code -->
            <div class="card mt-3">
                <div class="card-body">
                    <h6>Promo Code</h6>
                    <div class="input-group">
                        <input type="text" class="form-control" id="promo-code-input" placeholder="Enter promo code">
                        <button class="btn btn-outline-primary" type="button" id="apply-promo">Apply</button>
                    </div>
                    <div id="promo-message" class="mt-2"></div>
                </div>
            </div>
        </div>
        
        <!-- Checkout Form -->
        <div class="col-lg-8 order-lg-1">
            <form method="POST" id="checkout-form">
                {{ csrf_token() }}
                
                <!-- Delivery Information -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-truck"></i> Delivery Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="branch_id" class="form-label">Delivery Branch *</label>
                            <select class="form-select" id="branch_id" name="branch_id" required>
                                <option value="">Choose delivery branch...</option>
                                {% for branch in branches %}
                                <option value="{{ branch.id }}">{{ branch.name }} - {{ branch.address }}</option>
                                {% endfor %}
                            </select>
                            <div class="form-text">Select the branch closest to your delivery address</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="delivery_address" class="form-label">Delivery Address *</label>
                            <textarea class="form-control" id="delivery_address" name="delivery_address" 
                                      rows="3" required placeholder="Enter your complete delivery address">{{ current_user.address or '' }}</textarea>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="delivery_phone" class="form-label">Phone Number *</label>
                                <input type="tel" class="form-control" id="delivery_phone" name="delivery_phone" 
                                       required value="{{ current_user.phone or '' }}">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="delivery_notes" class="form-label">Delivery Notes</label>
                                <input type="text" class="form-control" id="delivery_notes" name="delivery_notes" 
                                       placeholder="Special instructions (optional)">
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Payment Information -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-credit-card"></i> Payment Method</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="payment_method" 
                                           id="payment_cash" value="cash" required>
                                    <label class="form-check-label" for="payment_cash">
                                        <i class="fas fa-money-bill-wave text-success"></i> Cash on Delivery
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="payment_method" 
                                           id="payment_check" value="check" required>
                                    <label class="form-check-label" for="payment_check">
                                        <i class="fas fa-money-check text-primary"></i> Check
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="payment_method" 
                                           id="payment_transfer" value="bank_transfer" required>
                                    <label class="form-check-label" for="payment_transfer">
                                        <i class="fas fa-university text-info"></i> Bank Transfer
                                    </label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            <strong>Payment Information:</strong>
                            <ul class="mb-0 mt-2">
                                <li><strong>Cash on Delivery:</strong> Pay when your order is delivered</li>
                                <li><strong>Check:</strong> Provide check details during delivery</li>
                                <li><strong>Bank Transfer:</strong> Transfer details will be provided after order confirmation</li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <!-- Terms and Conditions -->
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="terms" required>
                            <label class="form-check-label" for="terms">
                                I agree to the <a href="#" data-bs-toggle="modal" data-bs-target="#termsModal">Terms and Conditions</a>
                                and <a href="#" data-bs-toggle="modal" data-bs-target="#privacyModal">Privacy Policy</a> *
                            </label>
                        </div>
                    </div>
                </div>
                
                <!-- Hidden field for promo code -->
                <input type="hidden" id="promo_code" name="promo_code" value="">
                
                <!-- Submit Button -->
                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                    <a href="{{ url_for('client.cart') }}" class="btn btn-outline-secondary btn-lg me-md-2">
                        <i class="fas fa-arrow-left"></i> Back to Cart
                    </a>
                    <button type="submit" class="btn btn-success btn-lg" id="place-order-btn">
                        <i class="fas fa-check"></i> Place Order
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Terms Modal -->
<div class="modal fade" id="termsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Terms and Conditions</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <h6>Order Terms</h6>
                <ul>
                    <li>All orders are subject to product availability</li>
                    <li>Delivery times may vary based on location and stock availability</li>
                    <li>Payment must be completed as per the selected payment method</li>
                    <li>Orders can be cancelled before confirmation</li>
                    <li>Returns are accepted within 7 days of delivery for unopened items</li>
                </ul>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Privacy Modal -->
<div class="modal fade" id="privacyModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Privacy Policy</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <h6>Data Protection</h6>
                <p>Your personal information is protected and used only for order processing and delivery.</p>
                <ul>
                    <li>Personal data is encrypted and stored securely</li>
                    <li>Information is not shared with third parties</li>
                    <li>You can request data deletion at any time</li>
                </ul>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const promoInput = document.getElementById('promo-code-input');
    const applyPromoBtn = document.getElementById('apply-promo');
    const promoMessage = document.getElementById('promo-message');
    const discountRow = document.getElementById('discount-row');
    const discountAmount = document.getElementById('discount-amount');
    const totalAmount = document.getElementById('total-amount');
    const promoCodeField = document.getElementById('promo_code');
    const subtotal = {{ subtotal }};
    
    let currentDiscount = 0;
    
    // Apply promo code
    applyPromoBtn.addEventListener('click', function() {
        const code = promoInput.value.trim();
        if (!code) {
            showPromoMessage('Please enter a promo code', 'danger');
            return;
        }
        
        fetch('/api/validate_promo', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                code: code,
                order_amount: subtotal
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.valid) {
                currentDiscount = data.discount;
                promoCodeField.value = code;
                
                // Update UI
                discountAmount.textContent = `-${currentDiscount.toFixed(2)} Dh`;
                discountRow.style.display = 'flex';
                totalAmount.textContent = `${(subtotal - currentDiscount).toFixed(2)} Dh`;
                
                showPromoMessage(`Promo code applied! You saved ${currentDiscount.toFixed(2)} Dh`, 'success');
                applyPromoBtn.textContent = 'Applied';
                applyPromoBtn.disabled = true;
                promoInput.disabled = true;
            } else {
                showPromoMessage(data.message, 'danger');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showPromoMessage('Error validating promo code', 'danger');
        });
    });
    
    function showPromoMessage(message, type) {
        promoMessage.innerHTML = `<div class="alert alert-${type} alert-sm">${message}</div>`;
        setTimeout(() => {
            promoMessage.innerHTML = '';
        }, 5000);
    }
    
    // Form validation
    const form = document.getElementById('checkout-form');
    form.addEventListener('submit', function(e) {
        const placeOrderBtn = document.getElementById('place-order-btn');
        placeOrderBtn.disabled = true;
        placeOrderBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
    });
});
</script>
{% endblock %}
