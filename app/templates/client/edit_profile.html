{% extends "base.html" %}

{% block title %}Edit Profile - YalaOffice{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h2><i class="fas fa-edit"></i> Edit Profile</h2>
            <p class="text-muted">Update your account information</p>
        </div>
        <div class="col-md-4 text-md-end">
            <a href="{{ url_for('client.profile') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> Back to Profile
            </a>
        </div>
    </div>
    
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-user-edit"></i> Personal Information</h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                        
                        <!-- Basic Information -->
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="first_name" class="form-label">First Name *</label>
                                <input type="text" class="form-control" id="first_name" name="first_name" 
                                       value="{{ current_user.first_name or '' }}" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="last_name" class="form-label">Last Name *</label>
                                <input type="text" class="form-control" id="last_name" name="last_name" 
                                       value="{{ current_user.last_name or '' }}" required>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">Email Address *</label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       value="{{ current_user.email }}" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label">Phone Number</label>
                                <input type="tel" class="form-control" id="phone" name="phone" 
                                       value="{{ current_user.phone or '' }}" placeholder="+212 XXX XXX XXX">
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="address" class="form-label">Address</label>
                            <textarea class="form-control" id="address" name="address" rows="3" 
                                      placeholder="Enter your full address">{{ current_user.address or '' }}</textarea>
                        </div>
                        
                        <!-- Account Information (Read-only) -->
                        <hr class="my-4">
                        <h6 class="mb-3">Account Information</h6>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Username</label>
                                <input type="text" class="form-control" value="{{ current_user.username }}" readonly>
                                <div class="form-text">Username cannot be changed</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Account Type</label>
                                <input type="text" class="form-control" value="{{ current_user.role.title() }}" readonly>
                                <div class="form-text">Contact support to change account type</div>
                            </div>
                        </div>
                        
                        <!-- Password Change -->
                        <hr class="my-4">
                        <h6 class="mb-3">Change Password</h6>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i> Leave password fields empty if you don't want to change your password
                        </div>
                        
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="current_password" class="form-label">Current Password</label>
                                <input type="password" class="form-control" id="current_password" name="current_password">
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="new_password" class="form-label">New Password</label>
                                <input type="password" class="form-control" id="new_password" name="new_password" 
                                       minlength="6">
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="confirm_password" class="form-label">Confirm New Password</label>
                                <input type="password" class="form-control" id="confirm_password" name="confirm_password">
                            </div>
                        </div>
                        
                        <!-- Submit Buttons -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <hr>
                                <div class="d-flex justify-content-between">
                                    <a href="{{ url_for('client.profile') }}" class="btn btn-outline-secondary">
                                        <i class="fas fa-times"></i> Cancel
                                    </a>
                                    <button type="submit" class="btn btn-success">
                                        <i class="fas fa-save"></i> Save Changes
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Account Actions -->
    <div class="row justify-content-center mt-4">
        <div class="col-lg-8">
            <div class="card border-warning">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0"><i class="fas fa-exclamation-triangle"></i> Account Actions</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <h6>Download My Data</h6>
                            <p class="text-muted small">Download a copy of all your account data</p>
                            <button class="btn btn-outline-info" onclick="downloadData()">
                                <i class="fas fa-download"></i> Download Data
                            </button>
                        </div>
                        <div class="col-md-6 mb-3">
                            <h6>Delete Account</h6>
                            <p class="text-muted small">Permanently delete your account and all data</p>
                            <button class="btn btn-outline-danger" onclick="deleteAccount()">
                                <i class="fas fa-trash"></i> Delete Account
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const newPasswordInput = document.getElementById('new_password');
    const confirmPasswordInput = document.getElementById('confirm_password');
    const currentPasswordInput = document.getElementById('current_password');
    const form = document.querySelector('form');
    
    // Password validation
    function validatePasswords() {
        const newPassword = newPasswordInput.value;
        const confirmPassword = confirmPasswordInput.value;
        
        if (newPassword && newPassword !== confirmPassword) {
            confirmPasswordInput.setCustomValidity('Passwords do not match');
        } else {
            confirmPasswordInput.setCustomValidity('');
        }
    }
    
    // Require current password if new password is entered
    function validateCurrentPassword() {
        const newPassword = newPasswordInput.value;
        const currentPassword = currentPasswordInput.value;
        
        if (newPassword && !currentPassword) {
            currentPasswordInput.setCustomValidity('Current password is required to set a new password');
        } else {
            currentPasswordInput.setCustomValidity('');
        }
    }
    
    newPasswordInput.addEventListener('input', function() {
        validatePasswords();
        validateCurrentPassword();
    });
    
    confirmPasswordInput.addEventListener('input', validatePasswords);
    currentPasswordInput.addEventListener('input', validateCurrentPassword);
    
    // Form submission validation
    form.addEventListener('submit', function(e) {
        validatePasswords();
        validateCurrentPassword();
        
        if (!form.checkValidity()) {
            e.preventDefault();
            e.stopPropagation();
        }
        
        form.classList.add('was-validated');
    });
    
    // Phone number formatting
    const phoneInput = document.getElementById('phone');
    phoneInput.addEventListener('input', function() {
        let value = this.value.replace(/\D/g, '');
        if (value.startsWith('212')) {
            value = '+' + value;
        } else if (value.startsWith('0')) {
            value = '+212' + value.substring(1);
        } else if (value && !value.startsWith('+')) {
            value = '+212' + value;
        }
        this.value = value;
    });
});

function downloadData() {
    if (confirm('Download a copy of all your account data? This may take a few minutes.')) {
        fetch('/client/download_data', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => {
            if (response.ok) {
                return response.blob();
            }
            throw new Error('Failed to download data');
        })
        .then(blob => {
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'my_yalaoffice_data.json';
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error downloading data. Please try again.');
        });
    }
}

function deleteAccount() {
    const confirmation = prompt('This action cannot be undone. Type "DELETE" to confirm account deletion:');
    
    if (confirmation === 'DELETE') {
        if (confirm('Are you absolutely sure? This will permanently delete your account and all associated data.')) {
            fetch('/client/delete_account', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Account deleted successfully. You will be redirected to the home page.');
                    window.location.href = '/';
                } else {
                    alert(data.message || 'Error deleting account');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error deleting account. Please contact support.');
            });
        }
    } else if (confirmation !== null) {
        alert('Account deletion cancelled. You must type "DELETE" exactly to confirm.');
    }
}
</script>
{% endblock %}
