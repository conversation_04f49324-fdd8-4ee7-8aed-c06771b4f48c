{% extends "base.html" %}

{% block title %}Dashboard - YalaOffice{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- Welcome Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <h2 class="mb-0">
                        <i class="fas fa-user-circle"></i> Welcome, {{ current_user.get_full_name() }}!
                    </h2>
                    <p class="mb-0">
                        {% if current_user.is_reseller() %}
                        Reseller Account - Enjoy special pricing on all products
                        {% else %}
                        Client Account - Start shopping for office and school supplies
                        {% endif %}
                    </p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Quick Stats -->
    <div class="row mb-4">
        <div class="col-md-4 mb-3">
            <div class="card text-center">
                <div class="card-body">
                    <i class="fas fa-shopping-cart fa-2x text-primary mb-2"></i>
                    <h5 class="card-title">{{ cart_count }}</h5>
                    <p class="card-text">Items in Cart</p>
                    <a href="{{ url_for('client.cart') }}" class="btn btn-primary btn-sm">View Cart</a>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-3">
            <div class="card text-center">
                <div class="card-body">
                    <i class="fas fa-box fa-2x text-success mb-2"></i>
                    <h5 class="card-title">{{ recent_orders|length }}</h5>
                    <p class="card-text">Recent Orders</p>
                    <a href="{{ url_for('client.orders') }}" class="btn btn-success btn-sm">View Orders</a>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-3">
            <div class="card text-center">
                <div class="card-body">
                    <i class="fas fa-star fa-2x text-warning mb-2"></i>
                    <h5 class="card-title">
                        {% if current_user.is_reseller() %}
                        Reseller
                        {% else %}
                        Client
                        {% endif %}
                    </h5>
                    <p class="card-text">Account Type</p>
                    {% if not current_user.is_reseller() %}
                    <a href="{{ url_for('auth.register_reseller') }}" class="btn btn-warning btn-sm">Upgrade to Reseller</a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    
    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-bolt"></i> Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-2">
                            <a href="{{ url_for('main.products') }}" class="btn btn-outline-primary w-100">
                                <i class="fas fa-shopping-bag"></i> Browse Products
                            </a>
                        </div>
                        <div class="col-md-3 mb-2">
                            <a href="{{ url_for('client.cart') }}" class="btn btn-outline-success w-100">
                                <i class="fas fa-shopping-cart"></i> View Cart
                            </a>
                        </div>
                        <div class="col-md-3 mb-2">
                            <a href="{{ url_for('client.orders') }}" class="btn btn-outline-info w-100">
                                <i class="fas fa-history"></i> Order History
                            </a>
                        </div>
                        <div class="col-md-3 mb-2">
                            <a href="{{ url_for('main.contact') }}" class="btn btn-outline-secondary w-100">
                                <i class="fas fa-headset"></i> Support
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Recent Orders -->
    {% if recent_orders %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-clock"></i> Recent Orders</h5>
                    <a href="{{ url_for('client.orders') }}" class="btn btn-sm btn-outline-primary">View All</a>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Order #</th>
                                    <th>Date</th>
                                    <th>Status</th>
                                    <th>Total</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for order in recent_orders %}
                                <tr>
                                    <td>
                                        <strong>{{ order.order_number }}</strong>
                                    </td>
                                    <td>{{ order.created_at.strftime('%Y-%m-%d') if order.created_at }}</td>
                                    <td>
                                        {% if order.status == 'pending' %}
                                            <span class="badge bg-warning">Pending</span>
                                        {% elif order.status == 'confirmed' %}
                                            <span class="badge bg-info">Confirmed</span>
                                        {% elif order.status == 'picked' %}
                                            <span class="badge bg-primary">Picked</span>
                                        {% elif order.status == 'out_for_delivery' %}
                                            <span class="badge bg-secondary">Out for Delivery</span>
                                        {% elif order.status == 'delivered' %}
                                            <span class="badge bg-success">Delivered</span>
                                        {% elif order.status == 'cancelled' %}
                                            <span class="badge bg-danger">Cancelled</span>
                                        {% endif %}
                                    </td>
                                    <td><strong>{{ order.total_amount }} Dh</strong></td>
                                    <td>
                                        <a href="{{ url_for('client.order_detail', id=order.id) }}" 
                                           class="btn btn-sm btn-outline-primary">
                                            View
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
    
    <!-- Featured Categories -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-star"></i> Featured Categories</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <div class="card border-0 bg-light">
                                <div class="card-body text-center">
                                    <i class="fas fa-pen fa-2x text-primary mb-2"></i>
                                    <h6>Writing Instruments</h6>
                                    <a href="{{ url_for('main.products', category=1) }}" class="btn btn-sm btn-primary">Shop Now</a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="card border-0 bg-light">
                                <div class="card-body text-center">
                                    <i class="fas fa-book fa-2x text-success mb-2"></i>
                                    <h6>Paper & Notebooks</h6>
                                    <a href="{{ url_for('main.products', category=2) }}" class="btn btn-sm btn-success">Shop Now</a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="card border-0 bg-light">
                                <div class="card-body text-center">
                                    <i class="fas fa-briefcase fa-2x text-info mb-2"></i>
                                    <h6>Office Supplies</h6>
                                    <a href="{{ url_for('main.products', category=3) }}" class="btn btn-sm btn-info">Shop Now</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Update cart count on page load
    updateCartCount();
});

function updateCartCount() {
    // This would fetch the actual cart count from the server
    // For now, it's handled by the template
}
</script>
{% endblock %}
