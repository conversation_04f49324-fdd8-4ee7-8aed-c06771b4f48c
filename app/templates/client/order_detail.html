{% extends "base.html" %} {% block title %}Order {{ order.order_number }} -
YalaOffice{% endblock %} {% block content %}
<div class="container py-4">
  <!-- Breadcrumb -->
  <nav aria-label="breadcrumb">
    <ol class="breadcrumb">
      <li class="breadcrumb-item">
        <a href="{{ url_for('main.index') }}">Home</a>
      </li>
      <li class="breadcrumb-item">
        <a href="{{ url_for('client.orders') }}">My Orders</a>
      </li>
      <li class="breadcrumb-item active">{{ order.order_number }}</li>
    </ol>
  </nav>

  <!-- Order Header -->
  <div class="row mb-4">
    <div class="col-md-8">
      <h2><i class="fas fa-receipt"></i> Order {{ order.order_number }}</h2>
      <p class="text-muted">
        Placed on {{ order.created_at.strftime('%B %d, %Y at %I:%M %p') if
        order.created_at }}
      </p>
    </div>
    <div class="col-md-4 text-md-end">
      {% if order.status == 'pending' %}
      <span class="badge bg-warning fs-6">Pending</span>
      {% elif order.status == 'confirmed' %}
      <span class="badge bg-info fs-6">Confirmed</span>
      {% elif order.status == 'picked' %}
      <span class="badge bg-primary fs-6">Picked</span>
      {% elif order.status == 'out_for_delivery' %}
      <span class="badge bg-secondary fs-6">Out for Delivery</span>
      {% elif order.status == 'delivered' %}
      <span class="badge bg-success fs-6">Delivered</span>
      {% elif order.status == 'cancelled' %}
      <span class="badge bg-danger fs-6">Cancelled</span>
      {% endif %}
    </div>
  </div>

  <!-- Order Progress -->
  <div class="card mb-4">
    <div class="card-body">
      <h5 class="card-title"><i class="fas fa-route"></i> Order Progress</h5>
      <div class="progress-container">
        <div class="row text-center">
          <div class="col">
            <div
              class="progress-step {{ 'active' if order.status in ['pending', 'confirmed', 'picked', 'out_for_delivery', 'delivered'] else '' }}"
            >
              <div class="progress-icon">
                <i class="fas fa-shopping-cart"></i>
              </div>
              <div class="progress-label">Order Placed</div>
              {% if order.created_at %}
              <div class="progress-time">
                {{ order.created_at.strftime('%m/%d %I:%M %p') }}
              </div>
              {% endif %}
            </div>
          </div>
          <div class="col">
            <div
              class="progress-step {{ 'active' if order.status in ['confirmed', 'picked', 'out_for_delivery', 'delivered'] else '' }}"
            >
              <div class="progress-icon">
                <i class="fas fa-check-circle"></i>
              </div>
              <div class="progress-label">Confirmed</div>
              {% if order.confirmed_at %}
              <div class="progress-time">
                {{ order.confirmed_at.strftime('%m/%d %I:%M %p') }}
              </div>
              {% endif %}
            </div>
          </div>
          <div class="col">
            <div
              class="progress-step {{ 'active' if order.status in ['picked', 'out_for_delivery', 'delivered'] else '' }}"
            >
              <div class="progress-icon">
                <i class="fas fa-box"></i>
              </div>
              <div class="progress-label">Picked</div>
              {% if order.picked_at %}
              <div class="progress-time">
                {{ order.picked_at.strftime('%m/%d %I:%M %p') }}
              </div>
              {% endif %}
            </div>
          </div>
          <div class="col">
            <div
              class="progress-step {{ 'active' if order.status in ['out_for_delivery', 'delivered'] else '' }}"
            >
              <div class="progress-icon">
                <i class="fas fa-truck"></i>
              </div>
              <div class="progress-label">Out for Delivery</div>
              {% if order.out_for_delivery_at %}
              <div class="progress-time">
                {{ order.out_for_delivery_at.strftime('%m/%d %I:%M %p') }}
              </div>
              {% endif %}
            </div>
          </div>
          <div class="col">
            <div
              class="progress-step {{ 'active' if order.status == 'delivered' else '' }}"
            >
              <div class="progress-icon">
                <i class="fas fa-home"></i>
              </div>
              <div class="progress-label">Delivered</div>
              {% if order.delivered_at %}
              <div class="progress-time">
                {{ order.delivered_at.strftime('%m/%d %I:%M %p') }}
              </div>
              {% endif %}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="row">
    <!-- Order Items -->
    <div class="col-lg-8 mb-4">
      <div class="card">
        <div class="card-header">
          <h5 class="mb-0"><i class="fas fa-list"></i> Order Items</h5>
        </div>
        <div class="card-body">
          {% for item in order.items %}
          <div
            class="row align-items-center mb-3 pb-3 {{ 'border-bottom' if not loop.last }}"
          >
            <div class="col-md-2">
              {% if item.product.main_image %}
              <img
                src="{{ item.product.main_image }}"
                class="img-fluid rounded"
                alt="{{ item.product.title }}"
              />
              {% else %}
              <div
                class="bg-light d-flex align-items-center justify-content-center rounded"
                style="height: 60px"
              >
                <i class="fas fa-image text-muted"></i>
              </div>
              {% endif %}
            </div>
            <div class="col-md-5">
              <h6 class="mb-1">{{ item.product.title }}</h6>
              <p class="text-muted small mb-0">
                {{ item.product.brand or 'No Brand' }}
              </p>
            </div>
            <div class="col-md-2 text-center">
              <span>{{ item.quantity }}</span>
            </div>
            <div class="col-md-2 text-center">
              <span>{{ item.unit_price }} Dh</span>
            </div>
            <div class="col-md-1 text-end">
              <strong>{{ item.total_price }} Dh</strong>
            </div>
          </div>
          {% endfor %}
        </div>
      </div>
    </div>

    <!-- Order Summary & Details -->
    <div class="col-lg-4">
      <!-- Order Summary -->
      <div class="card mb-4">
        <div class="card-header">
          <h5 class="mb-0"><i class="fas fa-calculator"></i> Order Summary</h5>
        </div>
        <div class="card-body">
          <div class="d-flex justify-content-between mb-2">
            <span>Subtotal:</span>
            <span>{{ order.subtotal }} Dh</span>
          </div>

          {% if order.discount_amount > 0 %}
          <div class="d-flex justify-content-between mb-2 text-success">
            <span>Discount:</span>
            <span>-{{ order.discount_amount }} Dh</span>
          </div>
          {% endif %}

          <div class="d-flex justify-content-between mb-2">
            <span>Delivery:</span>
            <span class="text-success">Free</span>
          </div>

          <hr />

          <div class="d-flex justify-content-between">
            <strong>Total:</strong>
            <strong>{{ order.total_amount }} Dh</strong>
          </div>
        </div>
      </div>

      <!-- Delivery Information -->
      <div class="card mb-4">
        <div class="card-header">
          <h5 class="mb-0">
            <i class="fas fa-truck"></i> Delivery Information
          </h5>
        </div>
        <div class="card-body">
          <p><strong>Branch:</strong><br />{{ order.branch.name }}</p>
          <p><strong>Address:</strong><br />{{ order.delivery_address }}</p>
          <p><strong>Phone:</strong><br />{{ order.delivery_phone }}</p>
          {% if order.delivery_notes %}
          <p><strong>Notes:</strong><br />{{ order.delivery_notes }}</p>
          {% endif %} {% if order.delivery_person %}
          <p>
            <strong>Delivery Person:</strong><br />{{
            order.delivery_person.get_full_name() }}
          </p>
          {% endif %}
        </div>
      </div>

      <!-- Payment Information -->
      <div class="card mb-4">
        <div class="card-header">
          <h5 class="mb-0">
            <i class="fas fa-credit-card"></i> Payment Information
          </h5>
        </div>
        <div class="card-body">
          <p>
            <strong>Method:</strong><br />
            {% if order.payment_method == 'cash' %}
            <i class="fas fa-money-bill-wave text-success"></i> Cash on Delivery
            {% elif order.payment_method == 'check' %}
            <i class="fas fa-money-check text-primary"></i> Check {% elif
            order.payment_method == 'bank_transfer' %}
            <i class="fas fa-university text-info"></i> Bank Transfer {% endif
            %}
          </p>

          <p>
            <strong>Status:</strong><br />
            {% if order.payment_status == 'pending' %}
            <span class="badge bg-warning">Pending</span>
            {% elif order.payment_status == 'partial' %}
            <span class="badge bg-info">Partial</span>
            {% elif order.payment_status == 'paid' %}
            <span class="badge bg-success">Paid</span>
            {% endif %}
          </p>

          {% if order.paid_amount > 0 %}
          <p><strong>Paid Amount:</strong><br />{{ order.paid_amount }} Dh</p>
          {% endif %} {% if order.get_remaining_amount() > 0 %}
          <p>
            <strong>Remaining:</strong><br />{{ order.get_remaining_amount() }}
            Dh
          </p>
          {% endif %} {% if order.promo_code %}
          <p><strong>Promo Code:</strong><br />{{ order.promo_code }}</p>
          {% endif %}
        </div>
      </div>

      <!-- Actions -->
      <div class="card">
        <div class="card-body">
          <div class="d-grid gap-2">
            {% if order.status == 'pending' %}
            <button class="btn btn-outline-danger" onclick="cancelOrder()">
              <i class="fas fa-times"></i> Cancel Order
            </button>
            {% endif %} {% if order.invoice %}
            <a
              href="{{ url_for('client.download_invoice', order_id=order.id) }}"
              class="btn btn-outline-primary"
            >
              <i class="fas fa-download"></i> Download Invoice
            </a>
            {% endif %}

            <a
              href="{{ url_for('client.orders') }}"
              class="btn btn-outline-secondary"
            >
              <i class="fas fa-arrow-left"></i> Back to Orders
            </a>

            <button class="btn btn-outline-info" onclick="trackOrder()">
              <i class="fas fa-map-marker-alt"></i> Track Order
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %} {% block extra_css %}
<style>
  .progress-container {
    position: relative;
  }

  .progress-step {
    position: relative;
    text-align: center;
  }

  .progress-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: #e9ecef;
    color: #6c757d;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 10px;
    font-size: 20px;
  }

  .progress-step.active .progress-icon {
    background-color: #007bff;
    color: white;
  }

  .progress-label {
    font-weight: 500;
    margin-bottom: 5px;
  }

  .progress-time {
    font-size: 12px;
    color: #6c757d;
  }

  .progress-step.active .progress-label {
    color: #007bff;
    font-weight: 600;
  }
</style>
{% endblock %} {% block extra_js %}
<script>
  function cancelOrder() {
    if (confirm("Are you sure you want to cancel this order?")) {
      // Implement order cancellation
      alert("Order cancellation functionality will be implemented");
    }
  }

  function trackOrder() {
    // Implement order tracking
    alert("Order tracking functionality will be implemented");
  }
</script>
{% endblock %}
