{% extends "base.html" %}

{% block title %}Order Tracking - YalaOffice{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h2><i class="fas fa-map-marker-alt"></i> Order Tracking</h2>
            <p class="text-muted">Track your order in real-time</p>
        </div>
        <div class="col-md-4 text-md-end">
            <a href="{{ url_for('client.orders') }}" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left"></i> Back to Orders
            </a>
        </div>
    </div>
    
    <!-- Order Information -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <h5 class="mb-0">Order #{{ order.order_number }}</h5>
                            <small class="text-muted">Placed on {{ order.created_at.strftime('%B %d, %Y at %I:%M %p') if order.created_at }}</small>
                        </div>
                        <div class="col-md-6 text-md-end">
                            <span class="badge bg-{{ 'success' if order.status == 'delivered' else 'primary' if order.status == 'out_for_delivery' else 'warning' if order.status == 'confirmed' else 'secondary' }} fs-6">
                                {{ order.status.replace('_', ' ').title() }}
                            </span>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <h6>Delivery Address</h6>
                            <p class="text-muted">
                                {{ order.delivery_address }}<br>
                                {{ order.delivery_city }}, {{ order.delivery_postal_code }}
                            </p>
                        </div>
                        <div class="col-md-4">
                            <h6>Order Total</h6>
                            <h4 class="text-success">{{ order.total_amount }} Dh</h4>
                        </div>
                        <div class="col-md-4">
                            <h6>Estimated Delivery</h6>
                            <p class="text-muted">
                                {% if order.tracking_updates %}
                                    {% set latest_tracking = order.tracking_updates[-1] %}
                                    {% if latest_tracking.estimated_delivery %}
                                        {{ latest_tracking.estimated_delivery.strftime('%B %d, %Y') }}
                                    {% else %}
                                        To be determined
                                    {% endif %}
                                {% else %}
                                    To be determined
                                {% endif %}
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Tracking Timeline -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-route"></i> Tracking Timeline</h5>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        {% set status_order = ['pending', 'confirmed', 'picked', 'out_for_delivery', 'delivered'] %}
                        {% set current_status_index = status_order.index(order.status) if order.status in status_order else 0 %}
                        
                        {% for status in status_order %}
                        {% set is_completed = status_order.index(status) <= current_status_index %}
                        {% set is_current = status == order.status %}
                        
                        <div class="timeline-item {{ 'completed' if is_completed else 'pending' }} {{ 'current' if is_current else '' }}">
                            <div class="timeline-marker">
                                <i class="fas {{ 'fa-check' if is_completed and not is_current else 'fa-clock' if is_current else 'fa-circle' }}"></i>
                            </div>
                            <div class="timeline-content">
                                <h6 class="timeline-title">{{ status.replace('_', ' ').title() }}</h6>
                                <p class="timeline-description">
                                    {% if status == 'pending' %}
                                        Order received and being processed
                                    {% elif status == 'confirmed' %}
                                        Order confirmed and preparing for pickup
                                    {% elif status == 'picked' %}
                                        Order picked up and ready for delivery
                                    {% elif status == 'out_for_delivery' %}
                                        Order is on the way to your location
                                    {% elif status == 'delivered' %}
                                        Order successfully delivered
                                    {% endif %}
                                </p>
                                
                                {% if is_completed %}
                                <small class="text-muted">
                                    {% if status == 'confirmed' and order.confirmed_at %}
                                        {{ order.confirmed_at.strftime('%m/%d/%Y %I:%M %p') }}
                                    {% elif status == 'picked' and order.picked_at %}
                                        {{ order.picked_at.strftime('%m/%d/%Y %I:%M %p') }}
                                    {% elif status == 'out_for_delivery' and order.out_for_delivery_at %}
                                        {{ order.out_for_delivery_at.strftime('%m/%d/%Y %I:%M %p') }}
                                    {% elif status == 'delivered' and order.delivered_at %}
                                        {{ order.delivered_at.strftime('%m/%d/%Y %I:%M %p') }}
                                    {% endif %}
                                </small>
                                {% endif %}
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Detailed Tracking Updates -->
    {% if order.tracking_updates %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-list"></i> Detailed Updates</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Date & Time</th>
                                    <th>Status</th>
                                    <th>Location</th>
                                    <th>Notes</th>
                                    <th>Updated By</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for update in order.tracking_updates|reverse %}
                                <tr>
                                    <td>{{ update.created_at.strftime('%m/%d/%Y %I:%M %p') if update.created_at }}</td>
                                    <td>
                                        <span class="badge bg-{{ update.get_status_color() }}">
                                            <i class="{{ update.get_status_icon() }}"></i>
                                            {{ update.status.replace('_', ' ').title() }}
                                        </span>
                                    </td>
                                    <td>{{ update.location or '-' }}</td>
                                    <td>{{ update.notes or '-' }}</td>
                                    <td>
                                        {% if update.updated_by_user %}
                                            {{ update.updated_by_user.get_full_name() }}
                                        {% else %}
                                            System
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
    
    <!-- Delivery Information -->
    {% if order.status in ['out_for_delivery', 'delivered'] and order.delivery_person %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-info">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0"><i class="fas fa-user"></i> Delivery Information</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Delivery Person</h6>
                            <p class="mb-1"><strong>{{ order.delivery_person.get_full_name() }}</strong></p>
                            {% if order.delivery_person.phone %}
                            <p class="mb-1">
                                <i class="fas fa-phone text-primary"></i> 
                                <a href="tel:{{ order.delivery_person.phone }}">{{ order.delivery_person.phone }}</a>
                            </p>
                            {% endif %}
                            {% if order.delivery_person.email %}
                            <p class="mb-0">
                                <i class="fas fa-envelope text-primary"></i> 
                                <a href="mailto:{{ order.delivery_person.email }}">{{ order.delivery_person.email }}</a>
                            </p>
                            {% endif %}
                        </div>
                        <div class="col-md-6">
                            {% if order.status == 'out_for_delivery' %}
                            <div class="alert alert-success">
                                <i class="fas fa-truck"></i>
                                <strong>Your order is on the way!</strong><br>
                                The delivery person will contact you when they arrive.
                            </div>
                            {% elif order.status == 'delivered' %}
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle"></i>
                                <strong>Order Delivered Successfully!</strong><br>
                                Thank you for your business.
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
    
    <!-- Order Items -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-box"></i> Order Items</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Product</th>
                                    <th>Quantity</th>
                                    <th>Unit Price</th>
                                    <th>Total</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in order.items %}
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            {% if item.product.main_image %}
                                            <img src="{{ item.product.main_image }}" alt="{{ item.product.title }}" 
                                                 class="me-3" style="width: 50px; height: 50px; object-fit: cover;">
                                            {% endif %}
                                            <div>
                                                <h6 class="mb-0">{{ item.product.title }}</h6>
                                                <small class="text-muted">SKU: {{ item.product.sku }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>{{ item.quantity }}</td>
                                    <td>{{ item.unit_price }} Dh</td>
                                    <td><strong>{{ item.total_price }} Dh</strong></td>
                                </tr>
                                {% endfor %}
                            </tbody>
                            <tfoot>
                                <tr>
                                    <th colspan="3">Total Amount:</th>
                                    <th>{{ order.total_amount }} Dh</th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Actions -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body text-center">
                    {% if order.status == 'pending' %}
                    <button class="btn btn-danger me-2" onclick="cancelOrder()">
                        <i class="fas fa-times"></i> Cancel Order
                    </button>
                    {% endif %}
                    
                    <a href="{{ url_for('client.orders') }}" class="btn btn-outline-primary me-2">
                        <i class="fas fa-list"></i> View All Orders
                    </a>
                    
                    {% if order.status == 'delivered' %}
                    <a href="#" class="btn btn-success me-2" onclick="rateOrder()">
                        <i class="fas fa-star"></i> Rate Order
                    </a>
                    {% endif %}
                    
                    <button class="btn btn-info" onclick="shareTracking()">
                        <i class="fas fa-share"></i> Share Tracking
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}

.timeline-item {
    position: relative;
    margin-bottom: 30px;
}

.timeline-marker {
    position: absolute;
    left: -22px;
    top: 0;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: #6c757d;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
}

.timeline-item.completed .timeline-marker {
    background: #28a745;
}

.timeline-item.current .timeline-marker {
    background: #007bff;
    animation: pulse 2s infinite;
}

.timeline-content {
    padding-left: 20px;
}

.timeline-title {
    margin-bottom: 5px;
    font-weight: 600;
}

.timeline-description {
    margin-bottom: 5px;
    color: #6c757d;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(0, 123, 255, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(0, 123, 255, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(0, 123, 255, 0);
    }
}
</style>
{% endblock %}

{% block extra_js %}
<script>
function cancelOrder() {
    if (confirm('Are you sure you want to cancel this order?')) {
        // Implement order cancellation
        alert('Order cancellation feature will be implemented');
    }
}

function rateOrder() {
    // Implement order rating
    alert('Order rating feature will be implemented');
}

function shareTracking() {
    if (navigator.share) {
        navigator.share({
            title: 'Order Tracking - {{ order.order_number }}',
            text: 'Track my order from YalaOffice',
            url: window.location.href
        });
    } else {
        // Fallback for browsers that don't support Web Share API
        navigator.clipboard.writeText(window.location.href).then(() => {
            alert('Tracking link copied to clipboard!');
        });
    }
}

// Auto-refresh tracking every 30 seconds
setInterval(function() {
    if (document.hidden) return; // Don't refresh if tab is not active
    
    // Only refresh if order is not delivered
    const orderStatus = '{{ order.status }}';
    if (orderStatus !== 'delivered' && orderStatus !== 'cancelled') {
        location.reload();
    }
}, 30000);
</script>
{% endblock %}
