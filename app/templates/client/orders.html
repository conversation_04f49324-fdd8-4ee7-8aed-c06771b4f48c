{% extends "base.html" %}

{% block title %}My Orders - YalaOffice{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ url_for('main.index') }}">Home</a></li>
            <li class="breadcrumb-item"><a href="{{ url_for('client.dashboard') }}">Dashboard</a></li>
            <li class="breadcrumb-item active">My Orders</li>
        </ol>
    </nav>
    
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h2><i class="fas fa-history"></i> My Orders</h2>
            <p class="text-muted">Track and manage your orders</p>
        </div>
        <div class="col-md-4 text-md-end">
            <a href="{{ url_for('main.products') }}" class="btn btn-primary">
                <i class="fas fa-shopping-bag"></i> Continue Shopping
            </a>
        </div>
    </div>
    
    <!-- Filter Options -->
    <div class="card mb-4">
        <div class="card-body">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <div class="btn-group" role="group">
                        <a href="{{ url_for('client.orders') }}" 
                           class="btn {{ 'btn-primary' if not request.args.get('status') else 'btn-outline-primary' }}">
                            All Orders
                        </a>
                        <a href="{{ url_for('client.orders', status='pending') }}" 
                           class="btn {{ 'btn-primary' if request.args.get('status') == 'pending' else 'btn-outline-primary' }}">
                            Pending
                        </a>
                        <a href="{{ url_for('client.orders', status='confirmed') }}" 
                           class="btn {{ 'btn-primary' if request.args.get('status') == 'confirmed' else 'btn-outline-primary' }}">
                            Confirmed
                        </a>
                        <a href="{{ url_for('client.orders', status='delivered') }}" 
                           class="btn {{ 'btn-primary' if request.args.get('status') == 'delivered' else 'btn-outline-primary' }}">
                            Delivered
                        </a>
                    </div>
                </div>
                <div class="col-md-6 text-md-end">
                    <span class="text-muted">{{ orders.total }} orders found</span>
                </div>
            </div>
        </div>
    </div>
    
    {% if orders.items %}
    <!-- Orders List -->
    <div class="row">
        {% for order in orders.items %}
        <div class="col-12 mb-4">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <!-- Order Info -->
                        <div class="col-md-3">
                            <h6 class="mb-1">{{ order.order_number }}</h6>
                            <p class="text-muted small mb-0">{{ order.created_at.strftime('%B %d, %Y') if order.created_at }}</p>
                        </div>
                        
                        <!-- Status -->
                        <div class="col-md-2">
                            {% if order.status == 'pending' %}
                                <span class="badge bg-warning">Pending</span>
                            {% elif order.status == 'confirmed' %}
                                <span class="badge bg-info">Confirmed</span>
                            {% elif order.status == 'picked' %}
                                <span class="badge bg-primary">Picked</span>
                            {% elif order.status == 'out_for_delivery' %}
                                <span class="badge bg-secondary">Out for Delivery</span>
                            {% elif order.status == 'delivered' %}
                                <span class="badge bg-success">Delivered</span>
                            {% elif order.status == 'cancelled' %}
                                <span class="badge bg-danger">Cancelled</span>
                            {% endif %}
                        </div>
                        
                        <!-- Items Count -->
                        <div class="col-md-2">
                            <span class="text-muted">{{ order.items.count() }} items</span>
                        </div>
                        
                        <!-- Total -->
                        <div class="col-md-2">
                            <strong>{{ order.total_amount }} Dh</strong>
                        </div>
                        
                        <!-- Payment Status -->
                        <div class="col-md-2">
                            {% if order.payment_status == 'pending' %}
                                <span class="badge bg-outline-warning">Payment Pending</span>
                            {% elif order.payment_status == 'partial' %}
                                <span class="badge bg-outline-info">Partially Paid</span>
                            {% elif order.payment_status == 'paid' %}
                                <span class="badge bg-outline-success">Paid</span>
                            {% endif %}
                        </div>
                        
                        <!-- Actions -->
                        <div class="col-md-1 text-end">
                            <div class="dropdown">
                                <button class="btn btn-outline-secondary btn-sm dropdown-toggle" type="button" 
                                        data-bs-toggle="dropdown">
                                    Actions
                                </button>
                                <ul class="dropdown-menu">
                                    <li>
                                        <a class="dropdown-item" href="{{ url_for('client.order_detail', id=order.id) }}">
                                            <i class="fas fa-eye"></i> View Details
                                        </a>
                                    </li>
                                    {% if order.status == 'pending' %}
                                    <li>
                                        <a class="dropdown-item text-danger" href="#" onclick="cancelOrder({{ order.id }})">
                                            <i class="fas fa-times"></i> Cancel Order
                                        </a>
                                    </li>
                                    {% endif %}
                                    {% if order.invoice %}
                                    <li>
                                        <a class="dropdown-item" href="#">
                                            <i class="fas fa-download"></i> Download Invoice
                                        </a>
                                    </li>
                                    {% endif %}
                                    <li>
                                        <a class="dropdown-item" href="#" onclick="trackOrder({{ order.id }})">
                                            <i class="fas fa-map-marker-alt"></i> Track Order
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Order Items Preview -->
                    <div class="row mt-3">
                        <div class="col-12">
                            <div class="d-flex flex-wrap gap-2">
                                {% for item in order.items[:3] %}
                                <div class="d-flex align-items-center bg-light rounded p-2">
                                    {% if item.product.main_image %}
                                    <img src="{{ item.product.main_image }}" class="me-2" 
                                         style="width: 30px; height: 30px; object-fit: cover;" alt="{{ item.product.title }}">
                                    {% else %}
                                    <div class="bg-secondary rounded me-2" style="width: 30px; height: 30px;"></div>
                                    {% endif %}
                                    <span class="small">{{ item.product.title[:20] }}{% if item.product.title|length > 20 %}...{% endif %} ({{ item.quantity }})</span>
                                </div>
                                {% endfor %}
                                {% if order.items.count() > 3 %}
                                <div class="d-flex align-items-center">
                                    <span class="small text-muted">+{{ order.items.count() - 3 }} more items</span>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <!-- Delivery Info -->
                    <div class="row mt-2">
                        <div class="col-12">
                            <small class="text-muted">
                                <i class="fas fa-map-marker-alt"></i> Delivery to: {{ order.branch.name }}
                                {% if order.delivery_person %}
                                | <i class="fas fa-user"></i> Delivery by: {{ order.delivery_person.get_full_name() }}
                                {% endif %}
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
    
    <!-- Pagination -->
    {% if orders.pages > 1 %}
    <nav aria-label="Orders pagination">
        <ul class="pagination justify-content-center">
            {% if orders.has_prev %}
            <li class="page-item">
                <a class="page-link" href="{{ url_for('client.orders', page=orders.prev_num, status=request.args.get('status')) }}">
                    Previous
                </a>
            </li>
            {% endif %}
            
            {% for page_num in orders.iter_pages() %}
                {% if page_num %}
                    {% if page_num != orders.page %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('client.orders', page=page_num, status=request.args.get('status')) }}">
                            {{ page_num }}
                        </a>
                    </li>
                    {% else %}
                    <li class="page-item active">
                        <span class="page-link">{{ page_num }}</span>
                    </li>
                    {% endif %}
                {% else %}
                <li class="page-item disabled">
                    <span class="page-link">...</span>
                </li>
                {% endif %}
            {% endfor %}
            
            {% if orders.has_next %}
            <li class="page-item">
                <a class="page-link" href="{{ url_for('client.orders', page=orders.next_num, status=request.args.get('status')) }}">
                    Next
                </a>
            </li>
            {% endif %}
        </ul>
    </nav>
    {% endif %}
    
    {% else %}
    <!-- No Orders -->
    <div class="text-center py-5">
        <i class="fas fa-shopping-cart fa-5x text-muted mb-4"></i>
        <h3>No orders found</h3>
        {% if request.args.get('status') %}
        <p class="text-muted mb-4">You don't have any {{ request.args.get('status') }} orders yet.</p>
        <a href="{{ url_for('client.orders') }}" class="btn btn-outline-primary me-2">
            View All Orders
        </a>
        {% else %}
        <p class="text-muted mb-4">You haven't placed any orders yet.</p>
        {% endif %}
        <a href="{{ url_for('main.products') }}" class="btn btn-primary">
            <i class="fas fa-shopping-bag"></i> Start Shopping
        </a>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
function cancelOrder(orderId) {
    if (confirm('Are you sure you want to cancel this order?')) {
        // Implement order cancellation
        fetch(`/client/cancel_order/${orderId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Order cancelled successfully');
                location.reload();
            } else {
                alert(data.message || 'Error cancelling order');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error cancelling order');
        });
    }
}

function trackOrder(orderId) {
    // Implement order tracking
    window.location.href = `/client/order/${orderId}`;
}
</script>
{% endblock %}
