{% extends "base.html" %}

{% block title %}My Profile - YalaOffice{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h2><i class="fas fa-user"></i> My Profile</h2>
            <p class="text-muted">Manage your account information and preferences</p>
        </div>
        <div class="col-md-4 text-md-end">
            <a href="{{ url_for('client.edit_profile') }}" class="btn btn-primary">
                <i class="fas fa-edit"></i> Edit Profile
            </a>
        </div>
    </div>
    
    <div class="row">
        <!-- Profile Information -->
        <div class="col-lg-8 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-info-circle"></i> Personal Information</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">First Name</label>
                            <p class="form-control-plaintext">{{ current_user.first_name or 'Not provided' }}</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">Last Name</label>
                            <p class="form-control-plaintext">{{ current_user.last_name or 'Not provided' }}</p>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">Username</label>
                            <p class="form-control-plaintext">{{ current_user.username }}</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">Email</label>
                            <p class="form-control-plaintext">{{ current_user.email }}</p>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">Phone</label>
                            <p class="form-control-plaintext">{{ current_user.phone or 'Not provided' }}</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">Account Type</label>
                            <p class="form-control-plaintext">
                                <span class="badge bg-{{ 'success' if current_user.role == 'reseller' else 'primary' }}">
                                    {{ current_user.role.title() }}
                                </span>
                            </p>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label text-muted">Address</label>
                        <p class="form-control-plaintext">{{ current_user.address or 'Not provided' }}</p>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">Member Since</label>
                            <p class="form-control-plaintext">{{ current_user.created_at.strftime('%B %d, %Y') if current_user.created_at }}</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">Last Login</label>
                            <p class="form-control-plaintext">{{ current_user.last_login.strftime('%B %d, %Y at %I:%M %p') if current_user.last_login else 'Never' }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Account Statistics -->
        <div class="col-lg-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-chart-bar"></i> Account Statistics</h5>
                </div>
                <div class="card-body">
                    <div class="text-center mb-3">
                        <h3 class="text-primary">{{ current_user.orders.count() }}</h3>
                        <p class="text-muted">Total Orders</p>
                    </div>
                    
                    <div class="text-center mb-3">
                        <h3 class="text-success">{{ current_user.orders.filter_by(status='delivered').count() }}</h3>
                        <p class="text-muted">Completed Orders</p>
                    </div>
                    
                    <div class="text-center mb-3">
                        <h3 class="text-info">{{ current_user.wishlist_items.count() }}</h3>
                        <p class="text-muted">Wishlist Items</p>
                    </div>
                    
                    <div class="text-center">
                        <h3 class="text-warning">{{ current_user.notifications.filter_by(is_read=False).count() }}</h3>
                        <p class="text-muted">Unread Notifications</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-bolt"></i> Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-2">
                            <a href="{{ url_for('client.orders') }}" class="btn btn-outline-primary w-100">
                                <i class="fas fa-shopping-bag"></i><br>My Orders
                            </a>
                        </div>
                        <div class="col-md-3 mb-2">
                            <a href="{{ url_for('client.wishlist') }}" class="btn btn-outline-danger w-100">
                                <i class="fas fa-heart"></i><br>Wishlist
                            </a>
                        </div>
                        <div class="col-md-3 mb-2">
                            <a href="{{ url_for('client.notifications') }}" class="btn btn-outline-info w-100">
                                <i class="fas fa-bell"></i><br>Notifications
                            </a>
                        </div>
                        <div class="col-md-3 mb-2">
                            <a href="{{ url_for('main.products') }}" class="btn btn-outline-success w-100">
                                <i class="fas fa-shopping-cart"></i><br>Shop Now
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Recent Orders -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-history"></i> Recent Orders</h5>
                    <a href="{{ url_for('client.orders') }}" class="btn btn-sm btn-outline-primary">View All</a>
                </div>
                <div class="card-body">
                    {% set recent_orders = current_user.orders.order_by(current_user.orders.property.mapper.class_.created_at.desc()).limit(5).all() %}
                    {% if recent_orders %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Order #</th>
                                    <th>Date</th>
                                    <th>Status</th>
                                    <th>Total</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for order in recent_orders %}
                                <tr>
                                    <td><strong>{{ order.order_number }}</strong></td>
                                    <td>{{ order.created_at.strftime('%m/%d/%Y') if order.created_at }}</td>
                                    <td>
                                        {% if order.status == 'pending' %}
                                            <span class="badge bg-warning">Pending</span>
                                        {% elif order.status == 'confirmed' %}
                                            <span class="badge bg-info">Confirmed</span>
                                        {% elif order.status == 'picked' %}
                                            <span class="badge bg-primary">Picked</span>
                                        {% elif order.status == 'out_for_delivery' %}
                                            <span class="badge bg-secondary">Out for Delivery</span>
                                        {% elif order.status == 'delivered' %}
                                            <span class="badge bg-success">Delivered</span>
                                        {% elif order.status == 'cancelled' %}
                                            <span class="badge bg-danger">Cancelled</span>
                                        {% endif %}
                                    </td>
                                    <td><strong>{{ order.total_amount }} Dh</strong></td>
                                    <td>
                                        <a href="{{ url_for('client.order_detail', id=order.id) }}" 
                                           class="btn btn-sm btn-outline-info">View</a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-shopping-bag fa-3x text-muted mb-3"></i>
                        <h5>No orders yet</h5>
                        <p class="text-muted">Start shopping to see your orders here</p>
                        <a href="{{ url_for('main.products') }}" class="btn btn-primary">
                            <i class="fas fa-shopping-cart"></i> Start Shopping
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Auto-refresh notifications count
function updateNotificationCount() {
    fetch('/api/notification_count')
        .then(response => response.json())
        .then(data => {
            if (data.count > 0) {
                // Update notification badge in navigation if it exists
                const badge = document.querySelector('.notification-badge');
                if (badge) {
                    badge.textContent = data.count;
                    badge.style.display = 'inline';
                }
            }
        })
        .catch(error => console.error('Error updating notification count:', error));
}

// Update notification count on page load
document.addEventListener('DOMContentLoaded', updateNotificationCount);

// Refresh every 5 minutes
setInterval(updateNotificationCount, 300000);
</script>
{% endblock %}
