{% extends "base.html" %}

{% block title %}Notifications - YalaOffice{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h2><i class="fas fa-bell"></i> Notifications</h2>
            <p class="text-muted">Stay updated with your account activity</p>
        </div>
        <div class="col-md-4 text-md-end">
            {% if notifications.items %}
            <button class="btn btn-outline-primary" onclick="markAllAsRead()">
                <i class="fas fa-check-double"></i> Mark All Read
            </button>
            {% endif %}
        </div>
    </div>
    
    <!-- Notification Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="btn-group" role="group" aria-label="Notification filters">
                        <a href="{{ url_for('client.notifications') }}" 
                           class="btn {{ 'btn-primary' if not request.args.get('filter') else 'btn-outline-primary' }}">
                            All Notifications
                        </a>
                        <a href="{{ url_for('client.notifications', filter='unread') }}" 
                           class="btn {{ 'btn-primary' if request.args.get('filter') == 'unread' else 'btn-outline-primary' }}">
                            Unread
                        </a>
                        <a href="{{ url_for('client.notifications', filter='orders') }}" 
                           class="btn {{ 'btn-primary' if request.args.get('filter') == 'orders' else 'btn-outline-primary' }}">
                            Orders
                        </a>
                        <a href="{{ url_for('client.notifications', filter='promotions') }}" 
                           class="btn {{ 'btn-primary' if request.args.get('filter') == 'promotions' else 'btn-outline-primary' }}">
                            Promotions
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    {% if notifications.items %}
    <!-- Notifications List -->
    <div class="row">
        <div class="col-12">
            {% for notification in notifications.items %}
            <div class="card mb-3 {{ 'border-primary' if not notification.is_read else '' }}" 
                 id="notification-{{ notification.id }}">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-1 text-center">
                            <!-- Notification Icon -->
                            {% if notification.type == 'success' %}
                                <i class="fas fa-check-circle fa-2x text-success"></i>
                            {% elif notification.type == 'warning' %}
                                <i class="fas fa-exclamation-triangle fa-2x text-warning"></i>
                            {% elif notification.type == 'error' %}
                                <i class="fas fa-times-circle fa-2x text-danger"></i>
                            {% else %}
                                <i class="fas fa-info-circle fa-2x text-info"></i>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-9">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <h6 class="mb-1 {{ 'fw-bold' if not notification.is_read else '' }}">
                                        {{ notification.title }}
                                        {% if not notification.is_read %}
                                        <span class="badge bg-primary ms-2">New</span>
                                        {% endif %}
                                    </h6>
                                    <p class="mb-2 text-muted">{{ notification.message }}</p>
                                    <small class="text-muted">
                                        <i class="fas fa-clock"></i> 
                                        {{ notification.created_at.strftime('%B %d, %Y at %I:%M %p') if notification.created_at }}
                                    </small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-2 text-end">
                            <div class="btn-group-vertical" role="group">
                                {% if not notification.is_read %}
                                <button class="btn btn-sm btn-outline-primary" 
                                        onclick="markAsRead({{ notification.id }})">
                                    <i class="fas fa-check"></i> Mark Read
                                </button>
                                {% endif %}
                                
                                <button class="btn btn-sm btn-outline-danger" 
                                        onclick="deleteNotification({{ notification.id }})">
                                    <i class="fas fa-trash"></i> Delete
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
    
    <!-- Pagination -->
    {% if notifications.pages > 1 %}
    <nav aria-label="Notifications pagination" class="mt-4">
        <ul class="pagination justify-content-center">
            {% if notifications.has_prev %}
            <li class="page-item">
                <a class="page-link" href="{{ url_for('client.notifications', page=notifications.prev_num, **request.args) }}">
                    Previous
                </a>
            </li>
            {% endif %}
            
            {% for page_num in notifications.iter_pages() %}
                {% if page_num %}
                    {% if page_num != notifications.page %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('client.notifications', page=page_num, **request.args) }}">
                            {{ page_num }}
                        </a>
                    </li>
                    {% else %}
                    <li class="page-item active">
                        <span class="page-link">{{ page_num }}</span>
                    </li>
                    {% endif %}
                {% else %}
                <li class="page-item disabled">
                    <span class="page-link">...</span>
                </li>
                {% endif %}
            {% endfor %}
            
            {% if notifications.has_next %}
            <li class="page-item">
                <a class="page-link" href="{{ url_for('client.notifications', page=notifications.next_num, **request.args) }}">
                    Next
                </a>
            </li>
            {% endif %}
        </ul>
    </nav>
    {% endif %}
    
    {% else %}
    <!-- Empty State -->
    <div class="text-center py-5">
        <i class="fas fa-bell-slash fa-5x text-muted mb-4"></i>
        <h3>No notifications</h3>
        {% if request.args.get('filter') %}
        <p class="text-muted mb-4">No notifications found for the selected filter.</p>
        <a href="{{ url_for('client.notifications') }}" class="btn btn-primary">
            <i class="fas fa-bell"></i> View All Notifications
        </a>
        {% else %}
        <p class="text-muted mb-4">You're all caught up! No new notifications.</p>
        <a href="{{ url_for('main.products') }}" class="btn btn-primary">
            <i class="fas fa-shopping-cart"></i> Continue Shopping
        </a>
        {% endif %}
    </div>
    {% endif %}
    
    <!-- Notification Settings -->
    <div class="row mt-5">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-cog"></i> Notification Settings</h5>
                </div>
                <div class="card-body">
                    <form id="notificationSettings">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Email Notifications</h6>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="email_orders" checked>
                                    <label class="form-check-label" for="email_orders">
                                        Order updates and confirmations
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="email_promotions" checked>
                                    <label class="form-check-label" for="email_promotions">
                                        Promotions and special offers
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="email_newsletter">
                                    <label class="form-check-label" for="email_newsletter">
                                        Weekly newsletter
                                    </label>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <h6>In-App Notifications</h6>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="app_orders" checked>
                                    <label class="form-check-label" for="app_orders">
                                        Order status changes
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="app_stock" checked>
                                    <label class="form-check-label" for="app_stock">
                                        Wishlist item back in stock
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="app_promotions">
                                    <label class="form-check-label" for="app_promotions">
                                        New promotions and deals
                                    </label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mt-3">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Save Settings
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function markAsRead(notificationId) {
    fetch(`/client/notifications/mark-read/${notificationId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const notification = document.getElementById(`notification-${notificationId}`);
            if (notification) {
                // Remove the "New" badge and border
                notification.classList.remove('border-primary');
                const badge = notification.querySelector('.badge');
                if (badge) badge.remove();
                
                // Remove the mark read button
                const markReadBtn = notification.querySelector('button[onclick*="markAsRead"]');
                if (markReadBtn) markReadBtn.remove();
                
                // Make title less bold
                const title = notification.querySelector('h6');
                if (title) title.classList.remove('fw-bold');
            }
        } else {
            alert('Error marking notification as read');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error marking notification as read');
    });
}

function markAllAsRead() {
    if (confirm('Mark all notifications as read?')) {
        fetch('/client/notifications/mark-all-read', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error marking all notifications as read');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error marking all notifications as read');
        });
    }
}

function deleteNotification(notificationId) {
    if (confirm('Delete this notification?')) {
        fetch(`/client/notifications/delete/${notificationId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const notification = document.getElementById(`notification-${notificationId}`);
                if (notification) {
                    notification.remove();
                }
                
                // Check if there are any notifications left
                const remainingNotifications = document.querySelectorAll('[id^="notification-"]');
                if (remainingNotifications.length === 0) {
                    location.reload(); // Reload to show empty state
                }
            } else {
                alert('Error deleting notification');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error deleting notification');
        });
    }
}

// Notification settings form
document.getElementById('notificationSettings').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const settings = {};
    
    // Collect all checkbox values
    this.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
        settings[checkbox.id] = checkbox.checked;
    });
    
    fetch('/client/notification_settings', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(settings)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Notification settings saved successfully');
        } else {
            alert('Error saving notification settings');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error saving notification settings');
    });
});

// Auto-refresh every 30 seconds for new notifications
setInterval(function() {
    // Only refresh if we're on the first page and showing all notifications
    if (window.location.search === '' || window.location.search === '?page=1') {
        location.reload();
    }
}, 30000);
</script>
{% endblock %}
