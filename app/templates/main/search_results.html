{% extends "base.html" %}

{% block title %}Search Results - YalaOffice{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- Search Header -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h2><i class="fas fa-search"></i> Search Results</h2>
            {% if query %}
            <p class="text-muted">Results for "<strong>{{ query }}</strong>"</p>
            {% endif %}
            {% if products.items %}
            <p class="text-info">Found {{ products.total }} product(s) - Page {{ products.page }} of {{ products.pages }}</p>
            {% endif %}
        </div>
        <div class="col-md-4 text-md-end">
            <a href="{{ url_for('main.advanced_search') }}" class="btn btn-outline-primary">
                <i class="fas fa-filter"></i> Advanced Search
            </a>
        </div>
    </div>
    
    <!-- Active Filters -->
    {% if current_filters %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body py-2">
                    <div class="d-flex align-items-center flex-wrap">
                        <span class="me-3"><strong>Active Filters:</strong></span>
                        
                        {% if query %}
                        <span class="badge bg-primary me-2 mb-2">
                            Keywords: {{ query }}
                            <a href="{{ url_for('main.search', **dict(request.args, q='')) }}" 
                               class="text-white ms-1" style="text-decoration: none;">×</a>
                        </span>
                        {% endif %}
                        
                        {% if current_filters.category %}
                        {% for category in categories if category.id == current_filters.category %}
                        <span class="badge bg-success me-2 mb-2">
                            Category: {{ category.name }}
                            <a href="{{ url_for('main.search', **dict(request.args, category='')) }}" 
                               class="text-white ms-1" style="text-decoration: none;">×</a>
                        </span>
                        {% endfor %}
                        {% endif %}
                        
                        {% if current_filters.brand %}
                        <span class="badge bg-info me-2 mb-2">
                            Brand: {{ current_filters.brand }}
                            <a href="{{ url_for('main.search', **dict(request.args, brand='')) }}" 
                               class="text-white ms-1" style="text-decoration: none;">×</a>
                        </span>
                        {% endif %}
                        
                        {% if current_filters.min_price or current_filters.max_price %}
                        <span class="badge bg-warning me-2 mb-2">
                            Price: 
                            {% if current_filters.min_price %}{{ current_filters.min_price }}{% else %}0{% endif %} - 
                            {% if current_filters.max_price %}{{ current_filters.max_price }}{% else %}∞{% endif %} Dh
                            <a href="{{ url_for('main.search', **dict(request.args, min_price='', max_price='')) }}" 
                               class="text-white ms-1" style="text-decoration: none;">×</a>
                        </span>
                        {% endif %}
                        
                        <a href="{{ url_for('main.search') }}" class="btn btn-sm btn-outline-secondary ms-auto">
                            <i class="fas fa-times"></i> Clear All
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
    
    <!-- Sort and View Options -->
    {% if products.items %}
    <div class="row mb-4">
        <div class="col-md-6">
            <form method="GET" class="d-flex align-items-center">
                <!-- Preserve current search parameters -->
                {% for key, value in request.args.items() %}
                    {% if key != 'sort' %}
                    <input type="hidden" name="{{ key }}" value="{{ value }}">
                    {% endif %}
                {% endfor %}
                
                <label for="sort" class="form-label me-2 mb-0">Sort by:</label>
                <select class="form-select form-select-sm" id="sort" name="sort" onchange="this.form.submit()" style="width: auto;">
                    <option value="relevance" {{ 'selected' if current_filters.sort == 'relevance' }}>Relevance</option>
                    <option value="name" {{ 'selected' if current_filters.sort == 'name' }}>Name (A-Z)</option>
                    <option value="price_low" {{ 'selected' if current_filters.sort == 'price_low' }}>Price: Low to High</option>
                    <option value="price_high" {{ 'selected' if current_filters.sort == 'price_high' }}>Price: High to Low</option>
                    <option value="newest" {{ 'selected' if current_filters.sort == 'newest' }}>Newest First</option>
                </select>
            </form>
        </div>
        <div class="col-md-6 text-md-end">
            <div class="btn-group" role="group" aria-label="View options">
                <button type="button" class="btn btn-outline-secondary active" onclick="setView('grid')">
                    <i class="fas fa-th"></i> Grid
                </button>
                <button type="button" class="btn btn-outline-secondary" onclick="setView('list')">
                    <i class="fas fa-list"></i> List
                </button>
            </div>
        </div>
    </div>
    {% endif %}
    
    {% if products.items %}
        <!-- Products Grid -->
        <div class="row" id="products-container">
            {% for product in products.items %}
            <div class="col-lg-3 col-md-4 col-sm-6 mb-4 product-item">
                <div class="card h-100 card-hover">
                    <div class="position-relative">
                        {% if product.main_image %}
                        <img src="{{ product.main_image }}" class="card-img-top" 
                             style="height: 200px; object-fit: cover;" alt="{{ product.title }}">
                        {% else %}
                        <div class="card-img-top bg-light d-flex align-items-center justify-content-center" 
                             style="height: 200px;">
                            <i class="fas fa-image fa-3x text-muted"></i>
                        </div>
                        {% endif %}
                        
                        <!-- Wishlist button -->
                        {% if current_user.is_authenticated and current_user.is_client() %}
                        <button class="btn btn-sm btn-outline-danger position-absolute top-0 end-0 m-2" 
                                onclick="toggleWishlist({{ product.id }})" 
                                title="Add to wishlist">
                            <i class="fas fa-heart"></i>
                        </button>
                        {% endif %}
                        
                        <!-- Stock status -->
                        {% set total_stock = product.get_total_stock() %}
                        {% if total_stock == 0 %}
                        <span class="badge bg-danger position-absolute top-0 start-0 m-2">Out of Stock</span>
                        {% elif total_stock <= 10 %}
                        <span class="badge bg-warning position-absolute top-0 start-0 m-2">Low Stock</span>
                        {% endif %}
                    </div>
                    
                    <div class="card-body d-flex flex-column">
                        <h5 class="card-title">{{ product.title }}</h5>
                        <p class="card-text text-muted small">{{ product.description[:100] }}{% if product.description|length > 100 %}...{% endif %}</p>
                        
                        {% if product.brand %}
                        <p class="card-text"><small class="text-muted">Brand: {{ product.brand }}</small></p>
                        {% endif %}
                        
                        <div class="mt-auto">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                {% if current_user.is_authenticated and current_user.role == 'reseller' %}
                                <span class="price-tag">{{ product.reseller_price }} Dh</span>
                                <small class="text-muted">Normal: {{ product.normal_price }} Dh</small>
                                {% else %}
                                <span class="price-tag">{{ product.normal_price }} Dh</span>
                                {% endif %}
                            </div>
                            
                            <div class="d-grid gap-2">
                                <a href="{{ url_for('main.product_detail', id=product.id) }}" 
                                   class="btn btn-outline-primary">
                                    <i class="fas fa-eye"></i> View Details
                                </a>
                                {% if current_user.is_authenticated and current_user.is_client() and total_stock > 0 %}
                                <button class="btn btn-primary" onclick="addToCart({{ product.id }})">
                                    <i class="fas fa-shopping-cart"></i> Add to Cart
                                </button>
                                {% elif total_stock == 0 %}
                                <button class="btn btn-secondary" disabled>
                                    <i class="fas fa-ban"></i> Out of Stock
                                </button>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        
        <!-- Pagination -->
        {% if products.pages > 1 %}
        <nav aria-label="Search results pagination" class="mt-4">
            <ul class="pagination justify-content-center">
                {% if products.has_prev %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('main.search', page=products.prev_num, **request.args) }}">
                        Previous
                    </a>
                </li>
                {% endif %}
                
                {% for page_num in products.iter_pages() %}
                    {% if page_num %}
                        {% if page_num != products.page %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('main.search', page=page_num, **request.args) }}">
                                {{ page_num }}
                            </a>
                        </li>
                        {% else %}
                        <li class="page-item active">
                            <span class="page-link">{{ page_num }}</span>
                        </li>
                        {% endif %}
                    {% else %}
                    <li class="page-item disabled">
                        <span class="page-link">...</span>
                    </li>
                    {% endif %}
                {% endfor %}
                
                {% if products.has_next %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('main.search', page=products.next_num, **request.args) }}">
                        Next
                    </a>
                </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}
        
    {% else %}
        <!-- No Results -->
        <div class="text-center py-5">
            <i class="fas fa-search fa-5x text-muted mb-4"></i>
            <h3>No products found</h3>
            <p class="text-muted mb-4">
                {% if query %}
                No products match your search for "<strong>{{ query }}</strong>".
                {% else %}
                No products match your current filters.
                {% endif %}
            </p>
            
            <div class="row justify-content-center">
                <div class="col-md-6">
                    <h5>Try these suggestions:</h5>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-check text-success"></i> Check your spelling</li>
                        <li><i class="fas fa-check text-success"></i> Use more general keywords</li>
                        <li><i class="fas fa-check text-success"></i> Remove some filters</li>
                        <li><i class="fas fa-check text-success"></i> Browse our categories</li>
                    </ul>
                </div>
            </div>
            
            <div class="mt-4">
                <a href="{{ url_for('main.products') }}" class="btn btn-primary me-2">
                    <i class="fas fa-th-large"></i> Browse All Products
                </a>
                <a href="{{ url_for('main.advanced_search') }}" class="btn btn-outline-primary">
                    <i class="fas fa-filter"></i> Try Advanced Search
                </a>
            </div>
        </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
function setView(viewType) {
    const container = document.getElementById('products-container');
    const items = container.querySelectorAll('.product-item');
    const buttons = document.querySelectorAll('[onclick^="setView"]');
    
    // Update button states
    buttons.forEach(btn => btn.classList.remove('active'));
    event.target.classList.add('active');
    
    if (viewType === 'list') {
        // List view
        items.forEach(item => {
            item.className = 'col-12 mb-3 product-item';
            const card = item.querySelector('.card');
            card.classList.add('card-horizontal');
        });
    } else {
        // Grid view
        items.forEach(item => {
            item.className = 'col-lg-3 col-md-4 col-sm-6 mb-4 product-item';
            const card = item.querySelector('.card');
            card.classList.remove('card-horizontal');
        });
    }
    
    // Save preference
    localStorage.setItem('productView', viewType);
}

function toggleWishlist(productId) {
    fetch(`/client/wishlist/add/${productId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update wishlist count in navigation
            const wishlistCount = document.getElementById('wishlist-count');
            if (wishlistCount) {
                wishlistCount.textContent = parseInt(wishlistCount.textContent) + 1;
            }
            
            // Update button state
            const button = event.target.closest('button');
            button.classList.remove('btn-outline-danger');
            button.classList.add('btn-danger');
            button.onclick = () => removeFromWishlist(productId);
            
            alert('Product added to wishlist!');
        } else {
            alert(data.message || 'Error adding to wishlist');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error adding to wishlist');
    });
}

function removeFromWishlist(productId) {
    fetch(`/client/wishlist/remove/${productId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update wishlist count in navigation
            const wishlistCount = document.getElementById('wishlist-count');
            if (wishlistCount) {
                wishlistCount.textContent = Math.max(0, parseInt(wishlistCount.textContent) - 1);
            }
            
            // Update button state
            const button = event.target.closest('button');
            button.classList.remove('btn-danger');
            button.classList.add('btn-outline-danger');
            button.onclick = () => toggleWishlist(productId);
            
            alert('Product removed from wishlist!');
        } else {
            alert(data.message || 'Error removing from wishlist');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error removing from wishlist');
    });
}

function addToCart(productId) {
    // Implementation for adding to cart
    // This would be similar to existing cart functionality
    alert('Add to cart functionality - to be implemented');
}

// Load saved view preference
document.addEventListener('DOMContentLoaded', function() {
    const savedView = localStorage.getItem('productView');
    if (savedView === 'list') {
        setView('list');
    }
});
</script>
{% endblock %}
