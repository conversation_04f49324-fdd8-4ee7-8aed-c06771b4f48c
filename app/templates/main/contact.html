{% extends "base.html" %}

{% block title %}Contact Us - YalaOffice{% endblock %}

{% block content %}
<div class="container py-5">
    <!-- Header -->
    <div class="row mb-5">
        <div class="col-12 text-center">
            <h1 class="display-4 mb-4">Contact Us</h1>
            <p class="lead">Get in touch with our team - we're here to help!</p>
        </div>
    </div>
    
    <div class="row">
        <!-- Contact Information -->
        <div class="col-lg-4 mb-5">
            <div class="card h-100">
                <div class="card-body">
                    <h4 class="card-title mb-4">Get in Touch</h4>
                    
                    <div class="mb-4">
                        <h6><i class="fas fa-map-marker-alt text-primary me-2"></i> Head Office</h6>
                        <p class="text-muted">Boulevard Mohammed V<br>Casablanca, Morocco</p>
                    </div>
                    
                    <div class="mb-4">
                        <h6><i class="fas fa-phone text-success me-2"></i> Phone</h6>
                        <p class="text-muted">+212 522 123 456</p>
                    </div>
                    
                    <div class="mb-4">
                        <h6><i class="fas fa-envelope text-info me-2"></i> Email</h6>
                        <p class="text-muted"><EMAIL><br><EMAIL></p>
                    </div>
                    
                    <div class="mb-4">
                        <h6><i class="fas fa-clock text-warning me-2"></i> Business Hours</h6>
                        <p class="text-muted">
                            Monday - Friday: 8:00 AM - 6:00 PM<br>
                            Saturday: 9:00 AM - 4:00 PM<br>
                            Sunday: Closed
                        </p>
                    </div>
                    
                    <!-- Social Media -->
                    <div>
                        <h6>Follow Us</h6>
                        <div class="d-flex gap-2">
                            <a href="#" class="btn btn-outline-primary btn-sm">
                                <i class="fab fa-facebook-f"></i>
                            </a>
                            <a href="#" class="btn btn-outline-info btn-sm">
                                <i class="fab fa-twitter"></i>
                            </a>
                            <a href="#" class="btn btn-outline-danger btn-sm">
                                <i class="fab fa-instagram"></i>
                            </a>
                            <a href="#" class="btn btn-outline-primary btn-sm">
                                <i class="fab fa-linkedin-in"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Contact Form -->
        <div class="col-lg-8 mb-5">
            <div class="card">
                <div class="card-body">
                    <h4 class="card-title mb-4">Send us a Message</h4>
                    
                    <form>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="firstName" class="form-label">First Name *</label>
                                <input type="text" class="form-control" id="firstName" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="lastName" class="form-label">Last Name *</label>
                                <input type="text" class="form-control" id="lastName" required>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">Email Address *</label>
                                <input type="email" class="form-control" id="email" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label">Phone Number</label>
                                <input type="tel" class="form-control" id="phone">
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="subject" class="form-label">Subject *</label>
                            <select class="form-select" id="subject" required>
                                <option value="">Choose a subject...</option>
                                <option value="general">General Inquiry</option>
                                <option value="order">Order Support</option>
                                <option value="product">Product Information</option>
                                <option value="reseller">Reseller Program</option>
                                <option value="technical">Technical Support</option>
                                <option value="complaint">Complaint</option>
                                <option value="other">Other</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="message" class="form-label">Message *</label>
                            <textarea class="form-control" id="message" rows="5" 
                                      placeholder="Please describe your inquiry in detail..." required></textarea>
                        </div>
                        
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="newsletter">
                            <label class="form-check-label" for="newsletter">
                                Subscribe to our newsletter for updates and special offers
                            </label>
                        </div>
                        
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-paper-plane"></i> Send Message
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Branch Locations -->
    <div class="row mt-5">
        <div class="col-12">
            <h3 class="text-center mb-4">Our Branch Locations</h3>
            <div class="row">
                <div class="col-md-4 mb-4">
                    <div class="card">
                        <div class="card-body text-center">
                            <i class="fas fa-map-marker-alt fa-2x text-primary mb-3"></i>
                            <h5>Casablanca Main Branch</h5>
                            <p class="text-muted">Boulevard Mohammed V<br>Casablanca, Morocco</p>
                            <p><strong>Phone:</strong> +212 522 123 456</p>
                            <p><strong>Email:</strong> <EMAIL></p>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4 mb-4">
                    <div class="card">
                        <div class="card-body text-center">
                            <i class="fas fa-map-marker-alt fa-2x text-success mb-3"></i>
                            <h5>Rabat Branch</h5>
                            <p class="text-muted">Avenue Mohammed V<br>Rabat, Morocco</p>
                            <p><strong>Phone:</strong> +212 537 123 456</p>
                            <p><strong>Email:</strong> <EMAIL></p>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4 mb-4">
                    <div class="card">
                        <div class="card-body text-center">
                            <i class="fas fa-map-marker-alt fa-2x text-info mb-3"></i>
                            <h5>Marrakech Branch</h5>
                            <p class="text-muted">Gueliz<br>Marrakech, Morocco</p>
                            <p><strong>Phone:</strong> +212 524 123 456</p>
                            <p><strong>Email:</strong> <EMAIL></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- FAQ Section -->
    <div class="row mt-5">
        <div class="col-12">
            <h3 class="text-center mb-4">Frequently Asked Questions</h3>
            <div class="accordion" id="faqAccordion">
                <div class="accordion-item">
                    <h2 class="accordion-header" id="faq1">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" 
                                data-bs-target="#collapse1">
                            How can I place an order?
                        </button>
                    </h2>
                    <div id="collapse1" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                        <div class="accordion-body">
                            You can place an order through our website by browsing products, adding them to your cart, and proceeding to checkout. You can also call our customer service team or visit any of our branch locations.
                        </div>
                    </div>
                </div>
                
                <div class="accordion-item">
                    <h2 class="accordion-header" id="faq2">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" 
                                data-bs-target="#collapse2">
                            What payment methods do you accept?
                        </button>
                    </h2>
                    <div id="collapse2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                        <div class="accordion-body">
                            We accept cash, check, and bank transfer payments. Payment can be made upon delivery or in advance for larger orders.
                        </div>
                    </div>
                </div>
                
                <div class="accordion-item">
                    <h2 class="accordion-header" id="faq3">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" 
                                data-bs-target="#collapse3">
                            Do you offer delivery services?
                        </button>
                    </h2>
                    <div id="collapse3" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                        <div class="accordion-body">
                            Yes, we offer delivery services across Morocco. Delivery times and costs vary depending on your location and order size. You can track your order in real-time through our platform.
                        </div>
                    </div>
                </div>
                
                <div class="accordion-item">
                    <h2 class="accordion-header" id="faq4">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" 
                                data-bs-target="#collapse4">
                            How can I become a reseller?
                        </button>
                    </h2>
                    <div id="collapse4" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                        <div class="accordion-body">
                            To become a reseller, register for a reseller account on our website or contact our sales team. Resellers enjoy special pricing and bulk order discounts.
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const contactForm = document.querySelector('form');
    
    contactForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        // Here you would typically send the form data to the server
        alert('Thank you for your message! We will get back to you soon.');
        
        // Reset form
        contactForm.reset();
    });
});
</script>
{% endblock %}
