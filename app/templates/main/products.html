{% extends "base.html" %}

{% block title %}Products - YalaOffice{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row">
        <!-- Sidebar Filters -->
        <div class="col-lg-3 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-filter"></i> Filters</h5>
                </div>
                <div class="card-body">
                    <!-- Search -->
                    <form method="GET" action="{{ url_for('main.products') }}">
                        <div class="mb-3">
                            <label for="search" class="form-label">Search Products</label>
                            <input type="text" class="form-control" id="search" name="search" 
                                   value="{{ search }}" placeholder="Search...">
                        </div>
                        
                        <!-- Categories -->
                        <div class="mb-3">
                            <label class="form-label">Categories</label>
                            <div class="list-group">
                                <a href="{{ url_for('main.products') }}" 
                                   class="list-group-item list-group-item-action {{ 'active' if not current_category }}">
                                    All Categories
                                </a>
                                {% for category in categories %}
                                <a href="{{ url_for('main.products', category=category.id) }}" 
                                   class="list-group-item list-group-item-action {{ 'active' if current_category == category.id }}">
                                    {{ category.name }}
                                </a>
                                {% endfor %}
                            </div>
                        </div>
                        
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-search"></i> Search
                        </button>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- Products Grid -->
        <div class="col-lg-9">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2>Products</h2>
                    {% if current_category %}
                        {% for category in categories %}
                            {% if category.id == current_category %}
                                <p class="text-muted">Category: {{ category.name }}</p>
                            {% endif %}
                        {% endfor %}
                    {% endif %}
                    {% if search %}
                        <p class="text-muted">Search results for: "{{ search }}"</p>
                    {% endif %}
                </div>
                <div>
                    <span class="text-muted">{{ products.total }} products found</span>
                </div>
            </div>
            
            <!-- Products Grid -->
            {% if products.items %}
                <div class="row">
                    {% for product in products.items %}
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="card h-100 card-hover">
                            {% if product.main_image %}
                            <img src="{{ product.main_image }}" class="card-img-top" 
                                 style="height: 200px; object-fit: cover;" alt="{{ product.title }}">
                            {% else %}
                            <div class="card-img-top bg-light d-flex align-items-center justify-content-center" 
                                 style="height: 200px;">
                                <i class="fas fa-image fa-3x text-muted"></i>
                            </div>
                            {% endif %}
                            
                            <div class="card-body d-flex flex-column">
                                <h6 class="card-title">{{ product.title }}</h6>
                                <p class="card-text text-muted small flex-grow-1">
                                    {{ product.description[:100] + '...' if product.description and product.description|length > 100 else product.description }}
                                </p>
                                
                                {% if product.brand %}
                                <p class="card-text"><small class="text-muted">Brand: {{ product.brand }}</small></p>
                                {% endif %}
                                
                                <div class="mt-auto">
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <div>
                                            <span class="price-tag">{{ product.normal_price }} Dh</span>
                                            {% if current_user.is_authenticated and current_user.is_reseller() %}
                                            <br><small class="text-success">Reseller: {{ product.reseller_price }} Dh</small>
                                            {% endif %}
                                        </div>
                                        <span class="badge bg-secondary">{{ product.category.name }}</span>
                                    </div>
                                    
                                    <div class="d-grid gap-2">
                                        <a href="{{ url_for('main.product_detail', id=product.id) }}" 
                                           class="btn btn-primary btn-sm">
                                            <i class="fas fa-eye"></i> View Details
                                        </a>
                                        {% if current_user.is_authenticated and current_user.is_client() %}
                                        <button class="btn btn-outline-success btn-sm add-to-cart" 
                                                data-product-id="{{ product.id }}">
                                            <i class="fas fa-cart-plus"></i> Add to Cart
                                        </button>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                
                <!-- Pagination -->
                {% if products.pages > 1 %}
                <nav aria-label="Products pagination">
                    <ul class="pagination justify-content-center">
                        {% if products.has_prev %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('main.products', page=products.prev_num, category=current_category, search=search) }}">
                                Previous
                            </a>
                        </li>
                        {% endif %}
                        
                        {% for page_num in products.iter_pages() %}
                            {% if page_num %}
                                {% if page_num != products.page %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('main.products', page=page_num, category=current_category, search=search) }}">
                                        {{ page_num }}
                                    </a>
                                </li>
                                {% else %}
                                <li class="page-item active">
                                    <span class="page-link">{{ page_num }}</span>
                                </li>
                                {% endif %}
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">...</span>
                            </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if products.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('main.products', page=products.next_num, category=current_category, search=search) }}">
                                Next
                            </a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
            {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-search fa-3x text-muted mb-3"></i>
                    <h4>No products found</h4>
                    <p class="text-muted">Try adjusting your search criteria or browse all categories.</p>
                    <a href="{{ url_for('main.products') }}" class="btn btn-primary">
                        View All Products
                    </a>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Add to Cart Modal -->
<div class="modal fade" id="addToCartModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add to Cart</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addToCartForm">
                    <div class="mb-3">
                        <label for="quantity" class="form-label">Quantity</label>
                        <input type="number" class="form-control" id="quantity" name="quantity" value="1" min="1">
                    </div>
                    <div class="mb-3">
                        <label for="branch" class="form-label">Select Branch for Delivery</label>
                        <select class="form-select" id="branch" name="branch_id" required>
                            <option value="">Choose a branch...</option>
                            <!-- Branches will be loaded dynamically -->
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="confirmAddToCart">Add to Cart</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const addToCartButtons = document.querySelectorAll('.add-to-cart');
    const modal = new bootstrap.Modal(document.getElementById('addToCartModal'));
    let currentProductId = null;
    
    // Add to cart button click
    addToCartButtons.forEach(button => {
        button.addEventListener('click', function() {
            currentProductId = this.dataset.productId;
            modal.show();
        });
    });
    
    // Confirm add to cart
    document.getElementById('confirmAddToCart').addEventListener('click', function() {
        const quantity = document.getElementById('quantity').value;
        const branchId = document.getElementById('branch').value;
        
        if (!branchId) {
            alert('Please select a branch for delivery');
            return;
        }
        
        // Add to cart via AJAX
        fetch(`/client/add_to_cart/${currentProductId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                quantity: parseInt(quantity),
                branch_id: parseInt(branchId)
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Product added to cart successfully!');
                modal.hide();
                // Update cart count if needed
                updateCartCount();
            } else {
                alert(data.message || 'Error adding product to cart');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error adding product to cart');
        });
    });
});
</script>
{% endblock %}
