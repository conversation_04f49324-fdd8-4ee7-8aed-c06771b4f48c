{% extends "base.html" %}

{% block title %}{{ product.title }} - YalaOffice{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ url_for('main.index') }}">Home</a></li>
            <li class="breadcrumb-item"><a href="{{ url_for('main.products') }}">Products</a></li>
            <li class="breadcrumb-item"><a href="{{ url_for('main.products', category=product.category.id) }}">{{ product.category.name }}</a></li>
            <li class="breadcrumb-item active">{{ product.title }}</li>
        </ol>
    </nav>
    
    <div class="row">
        <!-- Product Images -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-body">
                    {% if product.main_image %}
                    <img src="{{ product.main_image }}" class="img-fluid rounded" alt="{{ product.title }}">
                    {% else %}
                    <div class="bg-light d-flex align-items-center justify-content-center rounded" style="height: 400px;">
                        <i class="fas fa-image fa-5x text-muted"></i>
                    </div>
                    {% endif %}
                    
                    <!-- Gallery images would go here if implemented -->
                </div>
            </div>
        </div>
        
        <!-- Product Details -->
        <div class="col-lg-6">
            <div class="card">
                <div class="card-body">
                    <h1 class="h3 mb-3">{{ product.title }}</h1>
                    
                    {% if product.brand %}
                    <p class="text-muted mb-2">
                        <strong>Brand:</strong> {{ product.brand }}
                    </p>
                    {% endif %}
                    
                    {% if product.sku %}
                    <p class="text-muted mb-3">
                        <strong>SKU:</strong> {{ product.sku }}
                    </p>
                    {% endif %}
                    
                    <div class="mb-3">
                        <span class="badge bg-secondary">{{ product.category.name }}</span>
                    </div>
                    
                    <!-- Pricing -->
                    <div class="mb-4">
                        <div class="price-section">
                            <span class="h4 text-success">{{ product.normal_price }} Dh</span>
                            <span class="text-muted">Normal Price</span>
                        </div>
                        {% if current_user.is_authenticated and current_user.is_reseller() %}
                        <div class="mt-2">
                            <span class="h5 text-primary">{{ product.reseller_price }} Dh</span>
                            <span class="text-muted">Your Reseller Price</span>
                        </div>
                        {% endif %}
                    </div>
                    
                    <!-- Stock Information -->
                    <div class="mb-4">
                        <h6>Stock Availability by Branch:</h6>
                        {% for branch in product.stock_items %}
                            {% if branch.branch.is_active %}
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span>{{ branch.branch.name }}</span>
                                <span class="badge {{ 'bg-success' if branch.quantity > 10 else 'bg-warning' if branch.quantity > 0 else 'bg-danger' }}">
                                    {{ branch.quantity }} in stock
                                </span>
                            </div>
                            {% endif %}
                        {% endfor %}
                    </div>
                    
                    <!-- Add to Cart Form -->
                    {% if current_user.is_authenticated and current_user.is_client() %}
                    <form id="addToCartForm" class="mb-4">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="quantity" class="form-label">Quantity</label>
                                <input type="number" class="form-control" id="quantity" name="quantity" value="1" min="1">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="branch" class="form-label">Delivery Branch</label>
                                <select class="form-select" id="branch" name="branch_id" required>
                                    <option value="">Choose a branch...</option>
                                    {% for stock in product.stock_items %}
                                        {% if stock.branch.is_active and stock.quantity > 0 %}
                                        <option value="{{ stock.branch.id }}">
                                            {{ stock.branch.name }} ({{ stock.quantity }} available)
                                        </option>
                                        {% endif %}
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        <button type="submit" class="btn btn-success btn-lg w-100">
                            <i class="fas fa-cart-plus"></i> Add to Cart
                        </button>
                    </form>
                    {% elif not current_user.is_authenticated %}
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        <a href="{{ url_for('auth.login') }}">Login</a> or 
                        <a href="{{ url_for('auth.register') }}">Register</a> to add products to cart.
                    </div>
                    {% endif %}
                    
                    <!-- Product Description -->
                    {% if product.description %}
                    <div class="mt-4">
                        <h6>Description</h6>
                        <p class="text-muted">{{ product.description }}</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    
    <!-- Related Products -->
    {% if related_products %}
    <div class="mt-5">
        <h3 class="mb-4">Related Products</h3>
        <div class="row">
            {% for related_product in related_products %}
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="card h-100 card-hover">
                    {% if related_product.main_image %}
                    <img src="{{ related_product.main_image }}" class="card-img-top" 
                         style="height: 200px; object-fit: cover;" alt="{{ related_product.title }}">
                    {% else %}
                    <div class="card-img-top bg-light d-flex align-items-center justify-content-center" 
                         style="height: 200px;">
                        <i class="fas fa-image fa-3x text-muted"></i>
                    </div>
                    {% endif %}
                    
                    <div class="card-body d-flex flex-column">
                        <h6 class="card-title">{{ related_product.title }}</h6>
                        <p class="card-text text-muted small flex-grow-1">
                            {{ related_product.description[:80] + '...' if related_product.description and related_product.description|length > 80 else related_product.description }}
                        </p>
                        <div class="mt-auto">
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="price-tag">{{ related_product.normal_price }} Dh</span>
                                <a href="{{ url_for('main.product_detail', id=related_product.id) }}" 
                                   class="btn btn-primary btn-sm">
                                    View
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('addToCartForm');
    
    if (form) {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const quantity = document.getElementById('quantity').value;
            const branchId = document.getElementById('branch').value;
            
            if (!branchId) {
                alert('Please select a branch for delivery');
                return;
            }
            
            // Add to cart via AJAX
            fetch(`/client/add_to_cart/{{ product.id }}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    quantity: parseInt(quantity),
                    branch_id: parseInt(branchId)
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Product added to cart successfully!');
                    // Update cart count if needed
                    updateCartCount();
                } else {
                    alert(data.message || 'Error adding product to cart');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error adding product to cart');
            });
        });
    }
});
</script>
{% endblock %}
