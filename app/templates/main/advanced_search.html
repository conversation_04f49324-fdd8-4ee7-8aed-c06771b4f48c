{% extends "base.html" %}

{% block title %}Advanced Search - YalaOffice{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h2><i class="fas fa-search"></i> Advanced Search</h2>
            <p class="text-muted">Find exactly what you're looking for with our advanced search filters</p>
        </div>
        <div class="col-md-4 text-md-end">
            <a href="{{ url_for('main.products') }}" class="btn btn-outline-primary">
                <i class="fas fa-th-large"></i> Browse All Products
            </a>
        </div>
    </div>
    
    <!-- Search Form -->
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-filter"></i> Search Filters</h5>
                </div>
                <div class="card-body">
                    <form method="GET" action="{{ url_for('main.search') }}" id="advancedSearchForm">
                        <!-- Text Search -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <label for="q" class="form-label">Search Keywords</label>
                                <input type="text" class="form-control form-control-lg" id="q" name="q" 
                                       placeholder="Enter product name, description, or brand..." 
                                       value="{{ request.args.get('q', '') }}">
                                <div class="form-text">Search in product titles, descriptions, and brands</div>
                            </div>
                        </div>
                        
                        <!-- Filters Row 1 -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <label for="category" class="form-label">Category</label>
                                <select class="form-select" id="category" name="category">
                                    <option value="">All Categories</option>
                                    {% for category in categories %}
                                    <option value="{{ category.id }}" 
                                            {{ 'selected' if request.args.get('category', type=int) == category.id }}>
                                        {{ category.name }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                            
                            <div class="col-md-6">
                                <label for="brand" class="form-label">Brand</label>
                                <select class="form-select" id="brand" name="brand">
                                    <option value="">All Brands</option>
                                    {% for brand in brands %}
                                    <option value="{{ brand }}" 
                                            {{ 'selected' if request.args.get('brand') == brand }}>
                                        {{ brand }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        
                        <!-- Price Range -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <label class="form-label">Price Range (Dh)</label>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="input-group">
                                            <span class="input-group-text">Min</span>
                                            <input type="number" class="form-control" id="min_price" name="min_price" 
                                                   placeholder="0" min="0" step="0.01"
                                                   value="{{ request.args.get('min_price', '') }}">
                                            <span class="input-group-text">Dh</span>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="input-group">
                                            <span class="input-group-text">Max</span>
                                            <input type="number" class="form-control" id="max_price" name="max_price" 
                                                   placeholder="{{ price_range[1] if price_range else '1000' }}" 
                                                   min="0" step="0.01"
                                                   value="{{ request.args.get('max_price', '') }}">
                                            <span class="input-group-text">Dh</span>
                                        </div>
                                    </div>
                                </div>
                                {% if price_range %}
                                <div class="form-text">
                                    Available range: {{ price_range[0] }} Dh - {{ price_range[1] }} Dh
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <!-- Sort Options -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <label for="sort" class="form-label">Sort Results By</label>
                                <select class="form-select" id="sort" name="sort">
                                    <option value="relevance" {{ 'selected' if request.args.get('sort') == 'relevance' }}>
                                        Relevance
                                    </option>
                                    <option value="name" {{ 'selected' if request.args.get('sort') == 'name' }}>
                                        Product Name (A-Z)
                                    </option>
                                    <option value="price_low" {{ 'selected' if request.args.get('sort') == 'price_low' }}>
                                        Price: Low to High
                                    </option>
                                    <option value="price_high" {{ 'selected' if request.args.get('sort') == 'price_high' }}>
                                        Price: High to Low
                                    </option>
                                    <option value="newest" {{ 'selected' if request.args.get('sort') == 'newest' }}>
                                        Newest First
                                    </option>
                                </select>
                            </div>
                        </div>
                        
                        <!-- Action Buttons -->
                        <div class="row">
                            <div class="col-12">
                                <hr>
                                <div class="d-flex justify-content-between">
                                    <button type="button" class="btn btn-outline-secondary" onclick="clearFilters()">
                                        <i class="fas fa-eraser"></i> Clear All Filters
                                    </button>
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <i class="fas fa-search"></i> Search Products
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Quick Search Suggestions -->
    <div class="row mt-5">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-lightbulb"></i> Popular Searches</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <h6>By Category</h6>
                            {% for category in categories[:5] %}
                            <a href="{{ url_for('main.search', category=category.id) }}" 
                               class="btn btn-outline-primary btn-sm me-2 mb-2">
                                {{ category.name }}
                            </a>
                            {% endfor %}
                        </div>
                        
                        <div class="col-md-3 mb-3">
                            <h6>By Brand</h6>
                            {% for brand in brands[:5] %}
                            <a href="{{ url_for('main.search', brand=brand) }}" 
                               class="btn btn-outline-success btn-sm me-2 mb-2">
                                {{ brand }}
                            </a>
                            {% endfor %}
                        </div>
                        
                        <div class="col-md-3 mb-3">
                            <h6>By Price Range</h6>
                            <a href="{{ url_for('main.search', max_price=50) }}" 
                               class="btn btn-outline-info btn-sm me-2 mb-2">
                                Under 50 Dh
                            </a>
                            <a href="{{ url_for('main.search', min_price=50, max_price=100) }}" 
                               class="btn btn-outline-info btn-sm me-2 mb-2">
                                50-100 Dh
                            </a>
                            <a href="{{ url_for('main.search', min_price=100) }}" 
                               class="btn btn-outline-info btn-sm me-2 mb-2">
                                Over 100 Dh
                            </a>
                        </div>
                        
                        <div class="col-md-3 mb-3">
                            <h6>Popular Keywords</h6>
                            <a href="{{ url_for('main.search', q='pen') }}" 
                               class="btn btn-outline-warning btn-sm me-2 mb-2">
                                Pens
                            </a>
                            <a href="{{ url_for('main.search', q='notebook') }}" 
                               class="btn btn-outline-warning btn-sm me-2 mb-2">
                                Notebooks
                            </a>
                            <a href="{{ url_for('main.search', q='paper') }}" 
                               class="btn btn-outline-warning btn-sm me-2 mb-2">
                                Paper
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Search Tips -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card border-info">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0"><i class="fas fa-info-circle"></i> Search Tips</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success"></i> Use specific keywords for better results</li>
                                <li><i class="fas fa-check text-success"></i> Combine multiple filters to narrow down results</li>
                                <li><i class="fas fa-check text-success"></i> Try different spellings or synonyms</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success"></i> Use price range to find products in your budget</li>
                                <li><i class="fas fa-check text-success"></i> Sort by price to compare options easily</li>
                                <li><i class="fas fa-check text-success"></i> Browse by category for related products</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function clearFilters() {
    // Clear all form inputs
    document.getElementById('q').value = '';
    document.getElementById('category').selectedIndex = 0;
    document.getElementById('brand').selectedIndex = 0;
    document.getElementById('min_price').value = '';
    document.getElementById('max_price').value = '';
    document.getElementById('sort').selectedIndex = 0;
}

// Auto-suggest functionality
document.getElementById('q').addEventListener('input', function() {
    const query = this.value;
    if (query.length >= 2) {
        // Implement auto-suggest here if needed
        // For now, just a placeholder
    }
});

// Price range validation
document.getElementById('min_price').addEventListener('change', function() {
    const minPrice = parseFloat(this.value);
    const maxPriceInput = document.getElementById('max_price');
    const maxPrice = parseFloat(maxPriceInput.value);
    
    if (minPrice && maxPrice && minPrice > maxPrice) {
        maxPriceInput.value = minPrice;
    }
});

document.getElementById('max_price').addEventListener('change', function() {
    const maxPrice = parseFloat(this.value);
    const minPriceInput = document.getElementById('min_price');
    const minPrice = parseFloat(minPriceInput.value);
    
    if (minPrice && maxPrice && maxPrice < minPrice) {
        minPriceInput.value = maxPrice;
    }
});

// Form submission with loading state
document.getElementById('advancedSearchForm').addEventListener('submit', function() {
    const submitBtn = this.querySelector('button[type="submit"]');
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Searching...';
    submitBtn.disabled = true;
});
</script>
{% endblock %}
