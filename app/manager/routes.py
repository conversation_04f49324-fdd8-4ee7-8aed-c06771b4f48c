from flask import render_template, redirect, url_for, flash, request, jsonify
from datetime import datetime
from flask_login import login_required, current_user
from functools import wraps
from app.manager import bp
from app.models import Product, Category, Order, Branch, ProductStock, User
from app import db

def manager_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated or not current_user.is_manager():
            flash('Access denied. Manager privileges required.', 'error')
            return redirect(url_for('main.index'))
        return f(*args, **kwargs)
    return decorated_function

@bp.route('/dashboard')
@login_required
@manager_required
def dashboard():
    """Manager dashboard"""
    # Get branch-specific statistics if manager is assigned to a branch
    branch_filter = {}
    if current_user.branch_id:
        branch_filter['branch_id'] = current_user.branch_id
    
    # Statistics
    total_orders = Order.query.filter_by(**branch_filter).count()
    pending_orders = Order.query.filter_by(status='pending', **branch_filter).count()
    confirmed_orders = Order.query.filter_by(status='confirmed', **branch_filter).count()
    
    # Recent orders
    recent_orders = Order.query.filter_by(**branch_filter)\
                              .order_by(Order.created_at.desc()).limit(10).all()
    
    # Low stock products for this branch
    low_stock_products = []
    if current_user.branch_id:
        stocks = ProductStock.query.filter_by(branch_id=current_user.branch_id).all()
        low_stock_products = [stock for stock in stocks if stock.is_low_stock()]
    
    return render_template('manager/dashboard.html',
                         total_orders=total_orders,
                         pending_orders=pending_orders,
                         confirmed_orders=confirmed_orders,
                         recent_orders=recent_orders,
                         low_stock_products=low_stock_products)

@bp.route('/orders')
@login_required
@manager_required
def orders():
    """Order management page"""
    page = request.args.get('page', 1, type=int)
    status_filter = request.args.get('status', '')

    # Base query - filter by branch if manager has specific branch
    query = Order.query
    if current_user.branch_id:
        query = query.filter_by(branch_id=current_user.branch_id)

    # Apply status filter
    if status_filter:
        query = query.filter_by(status=status_filter)

    orders = query.order_by(Order.created_at.desc())\
                  .paginate(page=page, per_page=20, error_out=False)

    return render_template('manager/orders.html', orders=orders, status_filter=status_filter)

@bp.route('/order/<int:id>')
@login_required
@manager_required
def order_detail(id):
    """Order detail page"""
    order = Order.query.get_or_404(id)

    # Check if manager can access this order
    if current_user.branch_id and order.branch_id != current_user.branch_id:
        flash('You can only access orders from your branch.', 'error')
        return redirect(url_for('manager.orders'))

    return render_template('manager/order_detail.html', order=order)

@bp.route('/order/<int:id>/confirm', methods=['POST'])
@login_required
@manager_required
def confirm_order(id):
    """Confirm an order"""
    order = Order.query.get_or_404(id)

    # Check permissions
    if current_user.branch_id and order.branch_id != current_user.branch_id:
        return jsonify({'success': False, 'message': 'Access denied'})

    if order.status != 'pending':
        return jsonify({'success': False, 'message': 'Order cannot be confirmed'})

    # Check stock availability
    for item in order.items:
        stock = ProductStock.query.filter_by(
            product_id=item.product_id,
            branch_id=order.branch_id
        ).first()

        if not stock or stock.quantity < item.quantity:
            return jsonify({
                'success': False,
                'message': f'Insufficient stock for {item.product.title}'
            })

    # Update order status
    old_status = order.status
    order.status = 'confirmed'
    order.confirmed_at = datetime.utcnow()
    order.confirmed_by_id = current_user.id

    # Reserve stock
    for item in order.items:
        stock = ProductStock.query.filter_by(
            product_id=item.product_id,
            branch_id=order.branch_id
        ).first()
        stock.quantity -= item.quantity

    db.session.commit()

    # Send notification email
    from app.utils import send_order_status_update_email
    send_order_status_update_email(order, old_status, 'confirmed')

    return jsonify({'success': True, 'message': 'Order confirmed successfully'})

@bp.route('/order/<int:id>/assign-delivery', methods=['POST'])
@login_required
@manager_required
def assign_delivery(id):
    """Assign order to delivery person"""
    order = Order.query.get_or_404(id)
    delivery_person_id = request.json.get('delivery_person_id')

    # Check permissions
    if current_user.branch_id and order.branch_id != current_user.branch_id:
        return jsonify({'success': False, 'message': 'Access denied'})

    if order.status not in ['confirmed', 'picked']:
        return jsonify({'success': False, 'message': 'Order cannot be assigned'})

    # Verify delivery person exists and is active
    delivery_person = User.query.filter_by(
        id=delivery_person_id,
        role='delivery',
        is_active=True
    ).first()

    if not delivery_person:
        return jsonify({'success': False, 'message': 'Invalid delivery person'})

    # Assign delivery person
    order.delivery_person_id = delivery_person_id
    order.assigned_at = datetime.utcnow()

    db.session.commit()

    return jsonify({'success': True, 'message': 'Order assigned successfully'})

@bp.route('/inventory')
@login_required
@manager_required
def inventory():
    """Inventory management page"""
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '')
    category_filter = request.args.get('category', '')
    stock_filter = request.args.get('stock', '')

    # Base query - filter by branch if manager has specific branch
    query = db.session.query(ProductStock).join(Product)
    if current_user.branch_id:
        query = query.filter(ProductStock.branch_id == current_user.branch_id)

    # Apply filters
    if search:
        query = query.filter(Product.title.contains(search))

    if category_filter:
        query = query.filter(Product.category_id == category_filter)

    if stock_filter == 'low':
        query = query.filter(ProductStock.quantity <= ProductStock.min_stock_level)
    elif stock_filter == 'out':
        query = query.filter(ProductStock.quantity == 0)

    stock_items = query.order_by(Product.title)\
                      .paginate(page=page, per_page=20, error_out=False)

    categories = Category.query.filter_by(is_active=True).all()

    return render_template('manager/inventory.html',
                         stock_items=stock_items,
                         categories=categories,
                         search=search,
                         category_filter=category_filter,
                         stock_filter=stock_filter)


