# 🚀 YalaOffice System - Running Guide

## ✅ System Status: RUNNING SUCCESSFULLY!

**YalaOffice is now live and accessible at:** http://127.0.0.1:5001

---

## 🔐 Default Login Credentials

### Administrator Access
- **Username:** `admin`
- **Password:** `admin123`
- **Role:** System Administrator
- **Access:** Full system control, analytics, reports, user management

### Manager Access
- **Username:** `manager1`
- **Password:** `manager123`
- **Role:** Store Manager
- **Access:** Inventory management, order processing, branch operations

### Delivery Personnel
- **Username:** `delivery1`
- **Password:** `delivery123`
- **Role:** Delivery Person
- **Access:** Order delivery, status updates, route management

### Customer (Client)
- **Username:** `client1`
- **Password:** `client123`
- **Role:** Regular Customer
- **Access:** Shopping, orders, loyalty program, wishlist

### Reseller
- **Username:** `reseller1`
- **Password:** `reseller123`
- **Role:** Reseller
- **Access:** Bulk orders, special pricing, reseller features

---

## 🎯 Key Features Available

### 🛍️ **E-commerce Features**
- **Product Catalog:** Browse 5 sample products across 10 categories
- **Shopping Cart:** Add products and checkout
- **Order Management:** Complete order workflow
- **Multi-branch Support:** 3 branches (Casablanca, Rabat, Marrakech)
- **Pricing Tiers:** Different prices for clients vs resellers

### 📊 **Analytics & Reports**
- **Admin Dashboard:** Real-time business metrics
- **Sales Reports:** Detailed sales analytics with charts
- **Customer Reports:** Customer behavior and segmentation
- **Branch Reports:** Performance comparison across branches
- **Inventory Reports:** Stock levels and alerts
- **Performance Monitoring:** System health and metrics

### 🎁 **Customer Loyalty Program**
- **Points System:** Earn points on purchases
- **Tier Levels:** Bronze, Silver, Gold, Platinum
- **Rewards:** Redeem points for discounts and benefits
- **Transaction History:** Track all loyalty activities

### 📧 **Communication System**
- **Email Notifications:** Order confirmations, status updates
- **In-app Notifications:** Real-time alerts
- **Professional Templates:** HTML email designs

### 🚚 **Order & Delivery Management**
- **Order Tracking:** Real-time status updates
- **Delivery Assignment:** Route optimization
- **Status Workflow:** Pending → Confirmed → Picked → Out for Delivery → Delivered

---

## 🌐 System URLs

### Main Application
- **Homepage:** http://127.0.0.1:5001
- **Login:** http://127.0.0.1:5001/auth/login
- **Register:** http://127.0.0.1:5001/auth/register
- **Products:** http://127.0.0.1:5001/products

### Admin Panel
- **Dashboard:** http://127.0.0.1:5001/admin/dashboard
- **Analytics:** http://127.0.0.1:5001/admin/analytics
- **Reports:** http://127.0.0.1:5001/admin/reports
- **Settings:** http://127.0.0.1:5001/admin/settings
- **Performance:** http://127.0.0.1:5001/admin/performance_monitoring

### API Endpoints
- **Categories:** http://127.0.0.1:5001/api/categories
- **Products:** http://127.0.0.1:5001/api/products
- **System Health:** http://127.0.0.1:5001/admin/system_health

---

## 📱 Testing the System

### 1. **Admin Experience**
1. Login as `admin` / `admin123`
2. Explore the comprehensive dashboard
3. View analytics and reports
4. Manage users and system settings
5. Monitor performance metrics

### 2. **Customer Experience**
1. Login as `client1` / `client123`
2. Browse products and categories
3. Add items to cart and wishlist
4. Place an order
5. Track order status
6. Check loyalty points

### 3. **Manager Experience**
1. Login as `manager1` / `manager123`
2. Manage inventory and stock
3. Process orders
4. Assign deliveries
5. View branch performance

### 4. **Delivery Experience**
1. Login as `delivery1` / `delivery123`
2. View assigned orders
3. Update delivery status
4. Manage delivery routes

---

## 🔧 System Management

### Starting the System
```bash
cd YalaOffice
source venv/bin/activate
python run.py
```

### Stopping the System
- Press `Ctrl+C` in the terminal running the server

### Reinitializing Database
```bash
rm -f instance/yalaoffice_dev.db
python init_db.py
```

### Viewing Logs
- Check the terminal where `python run.py` is running
- All requests and errors are logged there

---

## 📊 Sample Data Included

### Products (5 items)
- Blue Ballpoint Pen Pack (10 pcs) - 25 Dh
- A4 Spiral Notebook - 35 Dh
- Stapler with Staples - 45 Dh
- Highlighter Set (4 colors) - 30 Dh
- A4 Copy Paper (500 sheets) - 55 Dh

### Categories (10 categories)
- Writing Instruments
- Paper & Notebooks
- School & Office Supplies
- Art & Craft Supplies
- Filing & Organization
- And 5 more...

### Branches (3 locations)
- Casablanca Main Branch
- Rabat Branch
- Marrakech Branch

---

## 🚨 Important Notes

### Security
- **Change default passwords** before production use
- All passwords are currently set to simple values for testing
- CSRF protection is enabled on all forms

### Development Mode
- Debug mode is ON for development
- Auto-reload on code changes
- Detailed error messages

### Database
- Using SQLite for development
- Database file: `instance/yalaoffice_dev.db`
- All tables and relationships are properly set up

---

## 🎉 Success! Your YalaOffice System is Running

The complete enterprise business management system is now operational with:
- ✅ Multi-user authentication and roles
- ✅ E-commerce functionality
- ✅ Inventory management
- ✅ Order processing and tracking
- ✅ Customer loyalty program
- ✅ Analytics and reporting
- ✅ Performance monitoring
- ✅ Professional UI/UX

**Enjoy exploring your fully functional YalaOffice system!** 🚀
