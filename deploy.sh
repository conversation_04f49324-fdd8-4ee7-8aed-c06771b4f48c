#!/bin/bash

# YalaOffice Deployment Script
# This script helps deploy YalaOffice to a production environment

set -e

echo "🚀 Starting YalaOffice deployment..."

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    echo "📦 Creating virtual environment..."
    python3 -m venv venv
fi

# Activate virtual environment
echo "🔧 Activating virtual environment..."
source venv/bin/activate

# Install dependencies
echo "📥 Installing dependencies..."
pip install -r requirements.txt

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo "⚙️ Creating .env file from template..."
    cp .env.example .env
    echo "⚠️  Please edit .env file with your configuration before continuing!"
    echo "   - Set SECRET_KEY to a secure random string"
    echo "   - Configure DATABASE_URL for your database"
    echo "   - Set up email configuration for notifications"
    read -p "Press Enter after configuring .env file..."
fi

# Database setup
echo "🗄️ Setting up database..."
if [ ! -f "yalaoffice.db" ]; then
    echo "📊 Initializing database with sample data..."
    python init_db.py
else
    echo "ℹ️  Database already exists. Skipping initialization."
    echo "   To reset database, delete yalaoffice.db and run this script again."
fi

# Create necessary directories
echo "📁 Creating upload directories..."
mkdir -p app/static/uploads/products
mkdir -p app/static/uploads/invoices

# Set permissions (for production)
if [ "$1" = "production" ]; then
    echo "🔒 Setting production permissions..."
    chmod 755 app/static/uploads
    chmod 755 app/static/uploads/products
    chmod 755 app/static/uploads/invoices
fi

# Check if running in production mode
if [ "$1" = "production" ]; then
    echo "🏭 Production deployment mode"
    export FLASK_ENV=production
    
    # Install gunicorn if not already installed
    pip install gunicorn
    
    echo "🌐 Starting production server with Gunicorn..."
    echo "   Server will be available at http://localhost:8000"
    echo "   Press Ctrl+C to stop the server"
    
    gunicorn --bind 0.0.0.0:8000 --workers 4 --timeout 120 run:app
else
    echo "🛠️ Development deployment mode"
    export FLASK_ENV=development
    
    echo "🌐 Starting development server..."
    echo "   Server will be available at http://localhost:5001"
    echo "   Press Ctrl+C to stop the server"
    
    python run.py
fi
