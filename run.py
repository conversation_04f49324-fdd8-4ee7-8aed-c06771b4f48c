import os
from app import create_app, db
from app.models import User, Branch, Product, Category, Order
from flask_migrate import upgrade

app = create_app(os.getenv('FLASK_CONFIG') or 'default')

@app.shell_context_processor
def make_shell_context():
    return dict(db=db, User=User, Branch=Branch, Product=Product, 
                Category=Category, Order=Order)

@app.cli.command()
def deploy():
    """Run deployment tasks."""
    # migrate database to latest revision
    upgrade()

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5001)
